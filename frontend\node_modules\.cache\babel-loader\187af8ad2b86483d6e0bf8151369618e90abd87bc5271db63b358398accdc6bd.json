{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { orderService } from '../services/orderService';\nimport { wishlistService } from '../services/wishlistService';\nexport const useUserStats = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalOrders: 0,\n    totalSpent: 0,\n    wishlistCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchStats = useCallback(async () => {\n    if (!isAuthenticated) {\n      setStats({\n        totalOrders: 0,\n        totalSpent: 0,\n        wishlistCount: 0,\n        reviewsCount: 0\n      });\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Fetch data from multiple sources\n      const [orders, wishlist] = await Promise.all([orderService.getUserOrders().catch(() => []), wishlistService.getWishlist().catch(() => [])]);\n\n      // Calculate statistics\n      const totalOrders = orders.length;\n      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n      const wishlistCount = wishlist.length;\n\n      // Count reviews (simulate based on delivered orders)\n      const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n      setStats({\n        totalOrders,\n        totalSpent,\n        wishlistCount,\n        reviewsCount\n      });\n    } catch (err) {\n      console.error('Error fetching user statistics:', err);\n      setError('Failed to load statistics');\n    } finally {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n\n  // Fetch stats on mount and when authentication changes\n  useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  // Function to refresh stats manually\n  const refreshStats = useCallback(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  // Function to update specific stat without full refresh\n  const updateStat = useCallback((statName, value) => {\n    setStats(prev => ({\n      ...prev,\n      [statName]: value\n    }));\n  }, []);\n\n  // Function to increment/decrement stats\n  const incrementStat = useCallback((statName, increment = 1) => {\n    setStats(prev => ({\n      ...prev,\n      [statName]: Math.max(0, prev[statName] + increment)\n    }));\n  }, []);\n  return {\n    stats,\n    loading,\n    error,\n    refreshStats,\n    updateStat,\n    incrementStat,\n    fetchStats\n  };\n};\n_s(useUserStats, \"o2qkBA2MZdZ4dAg8QIPlFEFcLOM=\", false, function () {\n  return [useAuth];\n});\nexport default useUserStats;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useAuth", "orderService", "wishlistService", "useUserStats", "_s", "isAuthenticated", "stats", "setStats", "totalOrders", "totalSpent", "wishlistCount", "reviewsCount", "loading", "setLoading", "error", "setError", "fetchStats", "orders", "wishlist", "Promise", "all", "getUserOrders", "catch", "getWishlist", "length", "reduce", "sum", "order", "total", "filter", "status", "err", "console", "refreshStats", "updateStat", "statName", "value", "prev", "incrementStat", "increment", "Math", "max"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/hooks/useUserStats.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { orderService } from '../services/orderService';\nimport { wishlistService } from '../services/wishlistService';\n\nexport interface UserStats {\n  totalOrders: number;\n  totalSpent: number;\n  wishlistCount: number;\n  reviewsCount: number;\n}\n\nexport const useUserStats = () => {\n  const { isAuthenticated } = useAuth();\n  const [stats, setStats] = useState<UserStats>({\n    totalOrders: 0,\n    totalSpent: 0,\n    wishlistCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = useCallback(async () => {\n    if (!isAuthenticated) {\n      setStats({\n        totalOrders: 0,\n        totalSpent: 0,\n        wishlistCount: 0,\n        reviewsCount: 0\n      });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Fetch data from multiple sources\n      const [orders, wishlist] = await Promise.all([\n        orderService.getUserOrders().catch(() => []),\n        wishlistService.getWishlist().catch(() => [])\n      ]);\n\n      // Calculate statistics\n      const totalOrders = orders.length;\n      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n      const wishlistCount = wishlist.length;\n      \n      // Count reviews (simulate based on delivered orders)\n      const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n\n      setStats({\n        totalOrders,\n        totalSpent,\n        wishlistCount,\n        reviewsCount\n      });\n    } catch (err) {\n      console.error('Error fetching user statistics:', err);\n      setError('Failed to load statistics');\n    } finally {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n\n  // Fetch stats on mount and when authentication changes\n  useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  // Function to refresh stats manually\n  const refreshStats = useCallback(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  // Function to update specific stat without full refresh\n  const updateStat = useCallback((statName: keyof UserStats, value: number) => {\n    setStats(prev => ({\n      ...prev,\n      [statName]: value\n    }));\n  }, []);\n\n  // Function to increment/decrement stats\n  const incrementStat = useCallback((statName: keyof UserStats, increment: number = 1) => {\n    setStats(prev => ({\n      ...prev,\n      [statName]: Math.max(0, prev[statName] + increment)\n    }));\n  }, []);\n\n  return {\n    stats,\n    loading,\n    error,\n    refreshStats,\n    updateStat,\n    incrementStat,\n    fetchStats\n  };\n};\n\nexport default useUserStats;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,eAAe,QAAQ,6BAA6B;AAS7D,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAgB,CAAC,GAAGL,OAAO,CAAC,CAAC;EACrC,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAY;IAC5CW,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMmB,UAAU,GAAGjB,WAAW,CAAC,YAAY;IACzC,IAAI,CAACM,eAAe,EAAE;MACpBE,QAAQ,CAAC;QACPC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFE,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFA,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM,CAACE,MAAM,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3CnB,YAAY,CAACoB,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAC5CpB,eAAe,CAACqB,WAAW,CAAC,CAAC,CAACD,KAAK,CAAC,MAAM,EAAE,CAAC,CAC9C,CAAC;;MAEF;MACA,MAAMd,WAAW,GAAGS,MAAM,CAACO,MAAM;MACjC,MAAMf,UAAU,GAAGQ,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,KAAK,EAAE,CAAC,CAAC;MACtE,MAAMlB,aAAa,GAAGQ,QAAQ,CAACM,MAAM;;MAErC;MACA,MAAMb,YAAY,GAAGM,MAAM,CAACY,MAAM,CAACF,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,WAAW,CAAC,CAACN,MAAM;MAEhFjB,QAAQ,CAAC;QACPC,WAAW;QACXC,UAAU;QACVC,aAAa;QACbC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,iCAAiC,EAAEiB,GAAG,CAAC;MACrDhB,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;;EAErB;EACAP,SAAS,CAAC,MAAM;IACdkB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiB,YAAY,GAAGlC,WAAW,CAAC,MAAM;IACrCiB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMkB,UAAU,GAAGnC,WAAW,CAAC,CAACoC,QAAyB,EAAEC,KAAa,KAAK;IAC3E7B,QAAQ,CAAC8B,IAAI,KAAK;MAChB,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,aAAa,GAAGvC,WAAW,CAAC,CAACoC,QAAyB,EAAEI,SAAiB,GAAG,CAAC,KAAK;IACtFhC,QAAQ,CAAC8B,IAAI,KAAK;MAChB,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACF,QAAQ,CAAC,GAAGI,SAAS;IACpD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLjC,KAAK;IACLM,OAAO;IACPE,KAAK;IACLmB,YAAY;IACZC,UAAU;IACVI,aAAa;IACbtB;EACF,CAAC;AACH,CAAC;AAACZ,EAAA,CA1FWD,YAAY;EAAA,QACKH,OAAO;AAAA;AA2FrC,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}