{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n/**\n * @typedef {Object} Order\n * @property {string} _id\n * @property {string} orderNumber\n * @property {Date} date\n * @property {string} status\n * @property {number} total\n * @property {Array} items\n * @property {Object} shippingAddress\n * @property {string} [trackingNumber]\n */\n\nexport const orderService = {\n  // Get user orders\n  getUserOrders: async () => {\n    try {\n      const response = await api.get('/orders');\n      return response.data;\n    } catch (error) {\n      // Return mock data for now since backend orders API might not be implemented\n      console.log('Using mock order data');\n      return getMockOrders();\n    }\n  },\n  // Get specific order\n  getOrder: async orderId => {\n    const response = await api.get(`/orders/${orderId}`);\n    return response.data;\n  },\n  // Cancel order\n  cancelOrder: async orderId => {\n    const response = await api.put(`/orders/${orderId}/cancel`);\n    return response.data;\n  },\n  // Track order\n  trackOrder: async orderId => {\n    const response = await api.get(`/orders/${orderId}/tracking`);\n    return response.data;\n  },\n  // Create order\n  createOrder: async orderData => {\n    const response = await api.post('/orders', orderData);\n    return response.data;\n  },\n  // Update order\n  updateOrder: async (orderId, updateData) => {\n    const response = await api.put(`/orders/${orderId}`, updateData);\n    return response.data;\n  },\n  // Get order history\n  getOrderHistory: async (page = 1, limit = 10) => {\n    const response = await api.get(`/orders/history?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n  // Reorder items\n  reorder: async orderId => {\n    const response = await api.post(`/orders/${orderId}/reorder`);\n    return response.data;\n  },\n  // Get order invoice\n  getOrderInvoice: async orderId => {\n    const response = await api.get(`/orders/${orderId}/invoice`);\n    return response.data;\n  },\n  // Return order\n  returnOrder: async (orderId, returnData) => {\n    const response = await api.post(`/orders/${orderId}/return`, returnData);\n    return response.data;\n  }\n};\n\n// Mock orders data for demonstration\nconst getMockOrders = () => [{\n  _id: '1',\n  orderNumber: 'ORD-2024-001',\n  date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n  status: 'delivered',\n  total: 22990,\n  items: [{\n    productId: '1',\n    productName: 'Sony WH-1000XM4 Wireless Headphones',\n    productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n    quantity: 1,\n    price: 22990,\n    storeName: 'Amazon'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  },\n  trackingNumber: 'TRK123456789'\n}, {\n  _id: '2',\n  orderNumber: 'ORD-2024-002',\n  date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n  status: 'shipped',\n  total: 18999,\n  items: [{\n    productId: '2',\n    productName: 'Adidas Ultraboost 23',\n    productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n    quantity: 1,\n    price: 18999,\n    storeName: 'Adidas'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  },\n  trackingNumber: 'TRK987654321'\n}, {\n  _id: '3',\n  orderNumber: 'ORD-2024-003',\n  date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n  status: 'processing',\n  total: 154999,\n  items: [{\n    productId: '3',\n    productName: 'iPhone 15 Pro Max',\n    productImage: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',\n    quantity: 1,\n    price: 154999,\n    storeName: 'Apple Store'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  }\n}];\nexport default orderService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "orderService", "getUserOrders", "response", "get", "data", "error", "console", "log", "getMockOrders", "getOrder", "orderId", "cancelOrder", "put", "trackOrder", "createOrder", "orderData", "post", "updateOrder", "updateData", "getOrderHistory", "page", "limit", "reorder", "getOrderInvoice", "returnOrder", "returnData", "_id", "orderNumber", "date", "Date", "now", "status", "total", "items", "productId", "productName", "productImage", "quantity", "price", "storeName", "shippingAddress", "name", "address", "city", "state", "pincode", "trackingNumber"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/orderService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n/**\n * @typedef {Object} Order\n * @property {string} _id\n * @property {string} orderNumber\n * @property {Date} date\n * @property {string} status\n * @property {number} total\n * @property {Array} items\n * @property {Object} shippingAddress\n * @property {string} [trackingNumber]\n */\n\nexport const orderService = {\n  // Get user orders\n  getUserOrders: async () => {\n    try {\n      const response = await api.get('/orders');\n      return response.data;\n    } catch (error) {\n      // Return mock data for now since backend orders API might not be implemented\n      console.log('Using mock order data');\n      return getMockOrders();\n    }\n  },\n\n  // Get specific order\n  getOrder: async (orderId) => {\n    const response = await api.get(`/orders/${orderId}`);\n    return response.data;\n  },\n\n  // Cancel order\n  cancelOrder: async (orderId) => {\n    const response = await api.put(`/orders/${orderId}/cancel`);\n    return response.data;\n  },\n\n  // Track order\n  trackOrder: async (orderId) => {\n    const response = await api.get(`/orders/${orderId}/tracking`);\n    return response.data;\n  },\n\n  // Create order\n  createOrder: async (orderData) => {\n    const response = await api.post('/orders', orderData);\n    return response.data;\n  },\n\n  // Update order\n  updateOrder: async (orderId, updateData) => {\n    const response = await api.put(`/orders/${orderId}`, updateData);\n    return response.data;\n  },\n\n  // Get order history\n  getOrderHistory: async (page = 1, limit = 10) => {\n    const response = await api.get(`/orders/history?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // Reorder items\n  reorder: async (orderId) => {\n    const response = await api.post(`/orders/${orderId}/reorder`);\n    return response.data;\n  },\n\n  // Get order invoice\n  getOrderInvoice: async (orderId) => {\n    const response = await api.get(`/orders/${orderId}/invoice`);\n    return response.data;\n  },\n\n  // Return order\n  returnOrder: async (orderId, returnData) => {\n    const response = await api.post(`/orders/${orderId}/return`, returnData);\n    return response.data;\n  }\n};\n\n// Mock orders data for demonstration\nconst getMockOrders = () => [\n  {\n    _id: '1',\n    orderNumber: 'ORD-2024-001',\n    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n    status: 'delivered',\n    total: 22990,\n    items: [\n      {\n        productId: '1',\n        productName: 'Sony WH-1000XM4 Wireless Headphones',\n        productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n        quantity: 1,\n        price: 22990,\n        storeName: 'Amazon'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK123456789'\n  },\n  {\n    _id: '2',\n    orderNumber: 'ORD-2024-002',\n    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n    status: 'shipped',\n    total: 18999,\n    items: [\n      {\n        productId: '2',\n        productName: 'Adidas Ultraboost 23',\n        productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n        quantity: 1,\n        price: 18999,\n        storeName: 'Adidas'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK987654321'\n  },\n  {\n    _id: '3',\n    orderNumber: 'ORD-2024-003',\n    date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n    status: 'processing',\n    total: 154999,\n    items: [\n      {\n        productId: '3',\n        productName: 'iPhone 15 Pro Max',\n        productImage: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',\n        quantity: 1,\n        price: 154999,\n        storeName: 'Apple Store'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    }\n  }\n];\n\nexport default orderService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMK,YAAY,GAAG;EAC1B;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,SAAS,CAAC;MACzC,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,OAAOC,aAAa,CAAC,CAAC;IACxB;EACF,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOC,OAAO,IAAK;IAC3B,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAWO,OAAO,EAAE,CAAC;IACpD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,WAAW,EAAE,MAAOD,OAAO,IAAK;IAC9B,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACwB,GAAG,CAAC,WAAWF,OAAO,SAAS,CAAC;IAC3D,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,UAAU,EAAE,MAAOH,OAAO,IAAK;IAC7B,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAWO,OAAO,WAAW,CAAC;IAC7D,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,WAAW,EAAE,MAAOC,SAAS,IAAK;IAChC,MAAMb,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,SAAS,EAAED,SAAS,CAAC;IACrD,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,WAAW,EAAE,MAAAA,CAAOP,OAAO,EAAEQ,UAAU,KAAK;IAC1C,MAAMhB,QAAQ,GAAG,MAAMd,GAAG,CAACwB,GAAG,CAAC,WAAWF,OAAO,EAAE,EAAEQ,UAAU,CAAC;IAChE,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAe,eAAe,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC/C,MAAMnB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,wBAAwBiB,IAAI,UAAUC,KAAK,EAAE,CAAC;IAC7E,OAAOnB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkB,OAAO,EAAE,MAAOZ,OAAO,IAAK;IAC1B,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,WAAWN,OAAO,UAAU,CAAC;IAC7D,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAmB,eAAe,EAAE,MAAOb,OAAO,IAAK;IAClC,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAWO,OAAO,UAAU,CAAC;IAC5D,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAoB,WAAW,EAAE,MAAAA,CAAOd,OAAO,EAAEe,UAAU,KAAK;IAC1C,MAAMvB,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,WAAWN,OAAO,SAAS,EAAEe,UAAU,CAAC;IACxE,OAAOvB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,MAAMI,aAAa,GAAGA,CAAA,KAAM,CAC1B;EACEkB,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,qCAAqC;IAClDC,YAAY,EAAE,oEAAoE;IAClFC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC,EACD;EACEpB,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,sBAAsB;IACnCC,YAAY,EAAE,iEAAiE;IAC/EC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC,EACD;EACEpB,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,YAAY;EACpBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,mBAAmB;IAChCC,YAAY,EAAE,oEAAoE;IAClFC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX;AACF,CAAC,CACF;AAED,eAAe7C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}