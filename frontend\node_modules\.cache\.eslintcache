[{"C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx": "4", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx": "5", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx": "6", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx": "7", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx": "8", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx": "10", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx": "11", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx": "12", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts": "14", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx": "15", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts": "16", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx": "17", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx": "18", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx": "19", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts": "20", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx": "21", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx": "22", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts": "23", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.tsx": "24", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.tsx": "25", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.tsx": "26", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.ts": "27", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.ts": "28", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.ts": "29", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.tsx": "30"}, {"size": 554, "mtime": 1744737481313, "results": "31", "hashOfConfig": "32"}, {"size": 425, "mtime": 1744737479341, "results": "33", "hashOfConfig": "32"}, {"size": 2128, "mtime": 1751525240349, "results": "34", "hashOfConfig": "32"}, {"size": 5471, "mtime": 1751523368656, "results": "35", "hashOfConfig": "32"}, {"size": 11793, "mtime": 1751524123238, "results": "36", "hashOfConfig": "32"}, {"size": 4306, "mtime": 1751527577932, "results": "37", "hashOfConfig": "32"}, {"size": 2070, "mtime": 1744737916214, "results": "38", "hashOfConfig": "32"}, {"size": 5416, "mtime": 1751523997062, "results": "39", "hashOfConfig": "32"}, {"size": 22940, "mtime": 1751521725591, "results": "40", "hashOfConfig": "32"}, {"size": 1625, "mtime": 1751523337895, "results": "41", "hashOfConfig": "32"}, {"size": 1449, "mtime": 1751523313545, "results": "42", "hashOfConfig": "32"}, {"size": 7386, "mtime": 1751527528103, "results": "43", "hashOfConfig": "32"}, {"size": 295, "mtime": 1751523412225, "results": "44", "hashOfConfig": "32"}, {"size": 1383, "mtime": 1751520946869, "results": "45", "hashOfConfig": "32"}, {"size": 4190, "mtime": 1751519082746, "results": "46", "hashOfConfig": "32"}, {"size": 2569, "mtime": 1751519099130, "results": "47", "hashOfConfig": "32"}, {"size": 9806, "mtime": 1751521600741, "results": "48", "hashOfConfig": "32"}, {"size": 10416, "mtime": 1751520205230, "results": "49", "hashOfConfig": "32"}, {"size": 8457, "mtime": 1751522853960, "results": "50", "hashOfConfig": "32"}, {"size": 1251, "mtime": 1751519110891, "results": "51", "hashOfConfig": "32"}, {"size": 1359, "mtime": 1751520309435, "results": "52", "hashOfConfig": "32"}, {"size": 682, "mtime": 1751520321686, "results": "53", "hashOfConfig": "32"}, {"size": 4773, "mtime": 1751519259239, "results": "54", "hashOfConfig": "32"}, {"size": 11678, "mtime": 1751525268997, "results": "55", "hashOfConfig": "32"}, {"size": 8872, "mtime": 1751524792293, "results": "56", "hashOfConfig": "32"}, {"size": 2137, "mtime": 1751524442430, "results": "57", "hashOfConfig": "32"}, {"size": 1800, "mtime": 1751524672993, "results": "58", "hashOfConfig": "32"}, {"size": 3952, "mtime": 1751524736936, "results": "59", "hashOfConfig": "32"}, {"size": 2818, "mtime": 1751525565086, "results": "60", "hashOfConfig": "32"}, {"size": 1067, "mtime": 1751525178041, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ewlsnn", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx", ["152"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx", ["153", "154"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.tsx", ["155"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.tsx", [], [], {"ruleId": "156", "severity": 1, "message": "157", "line": 3, "column": 52, "nodeType": "158", "messageId": "159", "endLine": 3, "endColumn": 67}, {"ruleId": "156", "severity": 1, "message": "160", "line": 4, "column": 23, "nodeType": "158", "messageId": "159", "endLine": 4, "endColumn": 37}, {"ruleId": "156", "severity": 1, "message": "161", "line": 14, "column": 11, "nodeType": "158", "messageId": "159", "endLine": 14, "endColumn": 15}, {"ruleId": "156", "severity": 1, "message": "161", "line": 10, "column": 11, "nodeType": "158", "messageId": "159", "endLine": 10, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'ShoppingBagIcon' is defined but never used.", "Identifier", "unusedVar", "'HeartSolidIcon' is defined but never used.", "'user' is assigned a value but never used."]