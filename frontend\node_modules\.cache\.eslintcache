[{"C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx": "4", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx": "5", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx": "6", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx": "7", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx": "8", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx": "10", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx": "11", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx": "12", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts": "14", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx": "15", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts": "16", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx": "17", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx": "18", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx": "19", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts": "20", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx": "21", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx": "22", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts": "23"}, {"size": 554, "mtime": 1744737481313, "results": "24", "hashOfConfig": "25"}, {"size": 425, "mtime": 1744737479341, "results": "26", "hashOfConfig": "25"}, {"size": 1671, "mtime": 1751520349644, "results": "27", "hashOfConfig": "25"}, {"size": 5437, "mtime": 1744741065110, "results": "28", "hashOfConfig": "25"}, {"size": 9656, "mtime": 1751521020737, "results": "29", "hashOfConfig": "25"}, {"size": 3895, "mtime": 1745924151004, "results": "30", "hashOfConfig": "25"}, {"size": 2070, "mtime": 1744737916214, "results": "31", "hashOfConfig": "25"}, {"size": 2947, "mtime": 1751520978481, "results": "32", "hashOfConfig": "25"}, {"size": 22748, "mtime": 1751475380040, "results": "33", "hashOfConfig": "25"}, {"size": 1581, "mtime": 1744740517308, "results": "34", "hashOfConfig": "25"}, {"size": 1435, "mtime": 1744740538727, "results": "35", "hashOfConfig": "25"}, {"size": 6404, "mtime": 1751520583448, "results": "36", "hashOfConfig": "25"}, {"size": 274, "mtime": 1744740688023, "results": "37", "hashOfConfig": "25"}, {"size": 1383, "mtime": 1751520946869, "results": "38", "hashOfConfig": "25"}, {"size": 4190, "mtime": 1751519082746, "results": "39", "hashOfConfig": "25"}, {"size": 2569, "mtime": 1751519099130, "results": "40", "hashOfConfig": "25"}, {"size": 9806, "mtime": 1751521600741, "results": "41", "hashOfConfig": "25"}, {"size": 10416, "mtime": 1751520205230, "results": "42", "hashOfConfig": "25"}, {"size": 8154, "mtime": 1751520164592, "results": "43", "hashOfConfig": "25"}, {"size": 1251, "mtime": 1751519110891, "results": "44", "hashOfConfig": "25"}, {"size": 1359, "mtime": 1751520309435, "results": "45", "hashOfConfig": "25"}, {"size": 682, "mtime": 1751520321686, "results": "46", "hashOfConfig": "25"}, {"size": 4773, "mtime": 1751519259239, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ewlsnn", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx", ["117"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx", ["118", "119"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts", [], [], {"ruleId": "120", "severity": 1, "message": "121", "line": 3, "column": 52, "nodeType": "122", "messageId": "123", "endLine": 3, "endColumn": 67}, {"ruleId": "120", "severity": 1, "message": "124", "line": 4, "column": 23, "nodeType": "122", "messageId": "123", "endLine": 4, "endColumn": 37}, {"ruleId": "120", "severity": 1, "message": "125", "line": 14, "column": 11, "nodeType": "122", "messageId": "123", "endLine": 14, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'ShoppingBagIcon' is defined but never used.", "Identifier", "unusedVar", "'HeartSolidIcon' is defined but never used.", "'user' is assigned a value but never used."]