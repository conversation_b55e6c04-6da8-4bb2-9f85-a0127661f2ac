[{"C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx": "4", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx": "5", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx": "6", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx": "7", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx": "8", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx": "10", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx": "11", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx": "12", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts": "14"}, {"size": 554, "mtime": 1744737481313, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1744737479341, "results": "17", "hashOfConfig": "16"}, {"size": 1045, "mtime": 1745923388485, "results": "18", "hashOfConfig": "16"}, {"size": 5437, "mtime": 1744741065110, "results": "19", "hashOfConfig": "16"}, {"size": 9377, "mtime": 1751390284140, "results": "20", "hashOfConfig": "16"}, {"size": 3895, "mtime": 1745924151004, "results": "21", "hashOfConfig": "16"}, {"size": 2070, "mtime": 1744737916214, "results": "22", "hashOfConfig": "16"}, {"size": 2876, "mtime": 1751390240920, "results": "23", "hashOfConfig": "16"}, {"size": 13728, "mtime": 1751390426702, "results": "24", "hashOfConfig": "16"}, {"size": 1581, "mtime": 1744740517308, "results": "25", "hashOfConfig": "16"}, {"size": 1435, "mtime": 1744740538727, "results": "26", "hashOfConfig": "16"}, {"size": 2716, "mtime": 1744740582185, "results": "27", "hashOfConfig": "16"}, {"size": 274, "mtime": 1744740688023, "results": "28", "hashOfConfig": "16"}, {"size": 897, "mtime": 1751390219415, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ewlsnn", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts", [], []]