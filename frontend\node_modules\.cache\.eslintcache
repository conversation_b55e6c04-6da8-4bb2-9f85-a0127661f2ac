[{"C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx": "4", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx": "5", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx": "6", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx": "7", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx": "8", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx": "10", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx": "11", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx": "12", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts": "14", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx": "15", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts": "16", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx": "17", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx": "18", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx": "19", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts": "20", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx": "21", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx": "22", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts": "23", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.tsx": "24", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.tsx": "25", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.tsx": "26", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.ts": "27", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.ts": "28", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.ts": "29", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.tsx": "30", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.js": "31", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.js": "32", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.js": "33", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.js": "34", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.js": "35", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.js": "36", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.js": "37", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.js": "38", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.js": "39", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.js": "40", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.js": "41", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.js": "42", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.js": "43", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.js": "44", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.js": "45", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.js": "46", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.js": "47", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.js": "48", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.js": "49", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.js": "50", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.js": "51", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.js": "52", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.js": "53", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.js": "54", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.js": "55", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.js": "56", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.js": "57"}, {"size": 554, "mtime": 1744737481313, "results": "58", "hashOfConfig": "59"}, {"size": 425, "mtime": 1744737479341, "results": "60", "hashOfConfig": "59"}, {"size": 2128, "mtime": 1751525240349, "results": "61", "hashOfConfig": "59"}, {"size": 5471, "mtime": 1751523368656, "results": "62", "hashOfConfig": "59"}, {"size": 11793, "mtime": 1751524123238, "results": "63", "hashOfConfig": "59"}, {"size": 4306, "mtime": 1751527577932, "results": "64", "hashOfConfig": "59"}, {"size": 2070, "mtime": 1744737916214, "results": "65", "hashOfConfig": "59"}, {"size": 5416, "mtime": 1751523997062, "results": "66", "hashOfConfig": "59"}, {"size": 22940, "mtime": 1751521725591, "results": "67", "hashOfConfig": "59"}, {"size": 1625, "mtime": 1751523337895, "results": "68", "hashOfConfig": "59"}, {"size": 1449, "mtime": 1751523313545, "results": "69", "hashOfConfig": "59"}, {"size": 14072, "mtime": 1751527901537, "results": "70", "hashOfConfig": "59"}, {"size": 295, "mtime": 1751523412225, "results": "71", "hashOfConfig": "59"}, {"size": 1383, "mtime": 1751520946869, "results": "72", "hashOfConfig": "59"}, {"size": 4190, "mtime": 1751519082746, "results": "73", "hashOfConfig": "59"}, {"size": 2569, "mtime": 1751519099130, "results": "74", "hashOfConfig": "59"}, {"size": 9806, "mtime": 1751521600741, "results": "75", "hashOfConfig": "59"}, {"size": 10416, "mtime": 1751520205230, "results": "76", "hashOfConfig": "59"}, {"size": 8457, "mtime": 1751522853960, "results": "77", "hashOfConfig": "59"}, {"size": 1251, "mtime": 1751519110891, "results": "78", "hashOfConfig": "59"}, {"size": 1359, "mtime": 1751520309435, "results": "79", "hashOfConfig": "59"}, {"size": 682, "mtime": 1751520321686, "results": "80", "hashOfConfig": "59"}, {"size": 4773, "mtime": 1751519259239, "results": "81", "hashOfConfig": "59"}, {"size": 11678, "mtime": 1751525268997, "results": "82", "hashOfConfig": "59"}, {"size": 8872, "mtime": 1751524792293, "results": "83", "hashOfConfig": "59"}, {"size": 2137, "mtime": 1751524442430, "results": "84", "hashOfConfig": "59"}, {"size": 1800, "mtime": 1751524672993, "results": "85", "hashOfConfig": "59"}, {"size": 3952, "mtime": 1751524736936, "results": "86", "hashOfConfig": "59"}, {"size": 2818, "mtime": 1751525565086, "results": "87", "hashOfConfig": "59"}, {"size": 1067, "mtime": 1751525178041, "results": "88", "hashOfConfig": "59"}, {"size": 2062, "mtime": 1751528850304, "results": "89", "hashOfConfig": "59"}, {"size": 2901, "mtime": 1751528883962, "results": "90", "hashOfConfig": "59"}, {"size": 2508, "mtime": 1751528903647, "results": "91", "hashOfConfig": "59"}, {"size": 1505, "mtime": 1751528918032, "results": "92", "hashOfConfig": "59"}, {"size": 843, "mtime": 1751528931743, "results": "93", "hashOfConfig": "59"}, {"size": 2801, "mtime": 1751528952863, "results": "94", "hashOfConfig": "59"}, {"size": 3598, "mtime": 1751528978172, "results": "95", "hashOfConfig": "59"}, {"size": 3243, "mtime": 1751529000169, "results": "96", "hashOfConfig": "59"}, {"size": 3566, "mtime": 1751530309646, "results": "97", "hashOfConfig": "59"}, {"size": 4474, "mtime": 1751529053772, "results": "98", "hashOfConfig": "59"}, {"size": 3617, "mtime": 1751529077767, "results": "99", "hashOfConfig": "59"}, {"size": 470, "mtime": 1751529089804, "results": "100", "hashOfConfig": "59"}, {"size": 14746, "mtime": 1751529190953, "results": "101", "hashOfConfig": "59"}, {"size": 713, "mtime": 1751529215878, "results": "102", "hashOfConfig": "59"}, {"size": 535, "mtime": 1751528862746, "results": "103", "hashOfConfig": "59"}, {"size": 4556, "mtime": 1751529556872, "results": "104", "hashOfConfig": "59"}, {"size": 5664, "mtime": 1751529601777, "results": "105", "hashOfConfig": "59"}, {"size": 4153, "mtime": 1751529629577, "results": "106", "hashOfConfig": "59"}, {"size": 6053, "mtime": 1751529667698, "results": "107", "hashOfConfig": "59"}, {"size": 2756, "mtime": 1751529710806, "results": "108", "hashOfConfig": "59"}, {"size": 2603, "mtime": 1751529736398, "results": "109", "hashOfConfig": "59"}, {"size": 2389, "mtime": 1751529765344, "results": "110", "hashOfConfig": "59"}, {"size": 6533, "mtime": 1751530262731, "results": "111", "hashOfConfig": "59"}, {"size": 11533, "mtime": 1751529860034, "results": "112", "hashOfConfig": "59"}, {"size": 9083, "mtime": 1751530489079, "results": "113", "hashOfConfig": "59"}, {"size": 13228, "mtime": 1751529971715, "results": "114", "hashOfConfig": "59"}, {"size": 2123, "mtime": 1751529998937, "results": "115", "hashOfConfig": "59"}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ewlsnn", {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.tsx", ["287", "288"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.tsx", ["289", "290"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.tsx", ["291"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.ts", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.tsx", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.js", ["292"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.js", ["293", "294"], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.js", [], [], {"ruleId": "295", "severity": 1, "message": "296", "line": 8, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 8, "endColumn": 12}, {"ruleId": "295", "severity": 1, "message": "299", "line": 9, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 9, "endColumn": 12}, {"ruleId": "295", "severity": 1, "message": "300", "line": 4, "column": 23, "nodeType": "297", "messageId": "298", "endLine": 4, "endColumn": 37}, {"ruleId": "295", "severity": 1, "message": "301", "line": 14, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 14, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "301", "line": 10, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 10, "endColumn": 15}, {"ruleId": "302", "severity": 1, "message": "301", "line": 8, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 8, "endColumn": 15}, {"ruleId": "302", "severity": 1, "message": "303", "line": 4, "column": 22, "nodeType": "297", "messageId": "298", "endLine": 4, "endColumn": 37}, {"ruleId": "302", "severity": 1, "message": "304", "line": 80, "column": 9, "nodeType": "297", "messageId": "298", "endLine": 80, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'Bars3Icon' is defined but never used.", "Identifier", "unusedVar", "'XMarkIcon' is defined but never used.", "'HeartSolidIcon' is defined but never used.", "'user' is assigned a value but never used.", "no-unused-vars", "'StarOutlineIcon' is defined but never used.", "'handleReviewSubmit' is assigned a value but never used."]