[{"C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.js": "1", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.js": "2", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.js": "4", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.js": "5", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.js": "6", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.js": "7", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.js": "8", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.js": "9", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.js": "10", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.js": "11", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.js": "12", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.js": "13", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.js": "14", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.js": "15", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.js": "16", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.js": "17", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.js": "18", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.js": "19", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.js": "20", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.js": "21", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.js": "22", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.js": "23", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.js": "24", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.js": "25", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.js": "26", "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.js": "27"}, {"size": 2062, "mtime": 1751528850304, "results": "28", "hashOfConfig": "29"}, {"size": 2901, "mtime": 1751528883962, "results": "30", "hashOfConfig": "29"}, {"size": 2508, "mtime": 1751528903647, "results": "31", "hashOfConfig": "29"}, {"size": 1505, "mtime": 1751528918032, "results": "32", "hashOfConfig": "29"}, {"size": 843, "mtime": 1751528931743, "results": "33", "hashOfConfig": "29"}, {"size": 2801, "mtime": 1751528952863, "results": "34", "hashOfConfig": "29"}, {"size": 3598, "mtime": 1751528978172, "results": "35", "hashOfConfig": "29"}, {"size": 3243, "mtime": 1751529000169, "results": "36", "hashOfConfig": "29"}, {"size": 4712, "mtime": 1751531176482, "results": "37", "hashOfConfig": "29"}, {"size": 4474, "mtime": 1751529053772, "results": "38", "hashOfConfig": "29"}, {"size": 3617, "mtime": 1751529077767, "results": "39", "hashOfConfig": "29"}, {"size": 470, "mtime": 1751529089804, "results": "40", "hashOfConfig": "29"}, {"size": 14746, "mtime": 1751529190953, "results": "41", "hashOfConfig": "29"}, {"size": 713, "mtime": 1751529215878, "results": "42", "hashOfConfig": "29"}, {"size": 535, "mtime": 1751528862746, "results": "43", "hashOfConfig": "29"}, {"size": 4556, "mtime": 1751529556872, "results": "44", "hashOfConfig": "29"}, {"size": 5664, "mtime": 1751529601777, "results": "45", "hashOfConfig": "29"}, {"size": 4153, "mtime": 1751529629577, "results": "46", "hashOfConfig": "29"}, {"size": 6053, "mtime": 1751529667698, "results": "47", "hashOfConfig": "29"}, {"size": 2756, "mtime": 1751529710806, "results": "48", "hashOfConfig": "29"}, {"size": 2603, "mtime": 1751529736398, "results": "49", "hashOfConfig": "29"}, {"size": 2389, "mtime": 1751529765344, "results": "50", "hashOfConfig": "29"}, {"size": 7259, "mtime": 1751531222063, "results": "51", "hashOfConfig": "29"}, {"size": 11533, "mtime": 1751529860034, "results": "52", "hashOfConfig": "29"}, {"size": 9077, "mtime": 1751549140307, "results": "53", "hashOfConfig": "29"}, {"size": 12515, "mtime": 1751549401289, "results": "54", "hashOfConfig": "29"}, {"size": 2123, "mtime": 1751529998937, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ewlsnn", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\contexts\\UserStatsContext.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\hooks\\useUserStats.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\wishlistService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\DarkModeToggle.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\components\\ProductCard.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\SearchResultsPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\CategoryPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\TodaysDealsPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\WishlistPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProfilePage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\OrdersPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "C:\\Users\\<USER>\\CascadeProjects\\price-comparison-app\\frontend\\src\\pages\\NotFoundPage.js", [], []]