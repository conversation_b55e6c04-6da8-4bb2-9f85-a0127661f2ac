{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\ProductCard.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = (highestPrice - lowestPrice) / highestPrice * 100;\n  return /*#__PURE__*/_jsxDEV(Link, {\n    to: `/product/${product._id}`,\n    className: \"block group\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md group-hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: getImageUrl(product.images[0]),\n          alt: getImageAlt(product.images[0], product.name),\n          className: \"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 dark:text-gray-100 line-clamp-2\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n            className: \"h-5 w-5 text-yellow-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 dark:text-gray-400\",\n            children: [product.rating.toFixed(1), \" (\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-baseline justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: formatPriceIndian(lowestPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400 ml-1\",\n              children: [\"from \", product.prices.length, \" stores\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), savings >= 15 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\",\n          children: \"Best Deal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this), savings >= 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center rounded-full bg-green-100 dark:bg-green-900 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-200 mt-2\",\n          children: [\"Save up to \", savings.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\",\n            style: {\n              width: `${100 - savings}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Link", "StarIcon", "formatPriceIndian", "getImageUrl", "getImageAlt", "jsxDEV", "_jsxDEV", "ProductCard", "product", "lowestPrice", "Math", "min", "prices", "map", "p", "price", "highestPrice", "max", "savings", "to", "_id", "className", "children", "src", "images", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rating", "toFixed", "reviews", "length", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { Product } from '../types';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const lowestPrice = Math.min(...product.prices.map((p) => p.price));\n  const highestPrice = Math.max(...product.prices.map((p) => p.price));\n  const savings = ((highestPrice - lowestPrice) / highestPrice) * 100;\n\n  return (\n    <Link to={`/product/${product._id}`} className=\"block group\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md group-hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 relative overflow-hidden\">\n        <div className=\"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 flex items-center justify-center\">\n          <img\n            src={getImageUrl(product.images[0])}\n            alt={getImageAlt(product.images[0], product.name)}\n            className=\"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n          />\n        </div>\n\n        <div className=\"mt-4 space-y-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 line-clamp-2\">\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center gap-1\">\n            <StarIcon className=\"h-5 w-5 text-yellow-400\" />\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {product.rating.toFixed(1)} ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          <div className=\"flex items-baseline justify-between\">\n            <div>\n              <span className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                {formatPriceIndian(lowestPrice)}\n              </span>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400 ml-1\">\n                from {product.prices.length} stores\n              </span>\n            </div>\n          </div>\n\n          {savings >= 15 && (\n            <div className=\"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\">\n              Best Deal\n            </div>\n          )}\n          {savings >= 5 && (\n            <div className=\"inline-flex items-center rounded-full bg-green-100 dark:bg-green-900 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-200 mt-2\">\n              Save up to {savings.toFixed(0)}%\n            </div>\n          )}\n          {/* Price Comparison Bar */}\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\">\n            <div className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\" style={{ width: `${100 - savings}%` }}></div>\n          </div>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,2BAA2B;AAEpD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhF,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC/D,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACnE,MAAMC,YAAY,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAGT,OAAO,CAACI,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMG,OAAO,GAAI,CAACF,YAAY,GAAGP,WAAW,IAAIO,YAAY,GAAI,GAAG;EAEnE,oBACEV,OAAA,CAACN,IAAI;IAACmB,EAAE,EAAE,YAAYX,OAAO,CAACY,GAAG,EAAG;IAACC,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1DhB,OAAA;MAAKe,SAAS,EAAC,6KAA6K;MAAAC,QAAA,gBAC1LhB,OAAA;QAAKe,SAAS,EAAC,2KAA2K;QAAAC,QAAA,eACxLhB,OAAA;UACEiB,GAAG,EAAEpB,WAAW,CAACK,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAE;UACpCC,GAAG,EAAErB,WAAW,CAACI,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAEhB,OAAO,CAACkB,IAAI,CAAE;UAClDL,SAAS,EAAC;QAAiH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAIe,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAC9Ed,OAAO,CAACkB;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAELxB,OAAA;UAAKe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtChB,OAAA,CAACL,QAAQ;YAACoB,SAAS,EAAC;UAAyB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDxB,OAAA;YAAMe,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GACvDd,OAAO,CAACuB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACxB,OAAO,CAACyB,OAAO,CAACC,MAAM,EAAC,WACvD;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENxB,OAAA;UAAKe,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDhB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cAAMe,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAClEpB,iBAAiB,CAACO,WAAW;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACPxB,OAAA;cAAMe,SAAS,EAAC,+CAA+C;cAAAC,QAAA,GAAC,OACzD,EAACd,OAAO,CAACI,MAAM,CAACsB,MAAM,EAAC,SAC9B;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELZ,OAAO,IAAI,EAAE,iBACZZ,OAAA;UAAKe,SAAS,EAAC,sJAAsJ;UAAAC,QAAA,EAAC;QAEtK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACAZ,OAAO,IAAI,CAAC,iBACXZ,OAAA;UAAKe,SAAS,EAAC,gJAAgJ;UAAAC,QAAA,GAAC,aACnJ,EAACJ,OAAO,CAACc,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAEDxB,OAAA;UAAKe,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxEhB,OAAA;YAAKe,SAAS,EAAC,8DAA8D;YAACc,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAG,GAAG,GAAGlB,OAAO;YAAI;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACO,EAAA,GAzDI9B,WAAuC;AA2D7C,eAAeA,WAAW;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}