const mongoose = require('mongoose');
const Product = require('./models/Product');
require('dotenv').config();

const sampleProducts = [
  {
    name: "Sony WH-1000XM4 Wireless Noise-Canceling Headphones",
    description: "Industry-leading noise canceling with Dual Noise Sensor technology",
    category: "Electronics",
    images: [
      "https://example.com/sony-headphones-1.jpg",
      "https://example.com/sony-headphones-2.jpg"
    ],
    prices: [
      {
        storeName: "Amazon",
        price: 29058,
        productUrl: "https://amazon.in/sony-wh1000xm4"
      },
      {
        storeName: "Flipkart",
        price: 29224,
        productUrl: "https://flipkart.com/sony-wh1000xm4"
      },
      {
        storeName: "Sony Store",
        price: 31724,
        productUrl: "https://sony.co.in/wh1000xm4"
      }
    ],
    rating: 4.8,
    reviews: [
      {
        userName: "John D.",
        comment: "Best noise-canceling headphones I've ever used!",
        rating: 5,
        createdAt: new Date()
      }
    ]
  },
  {
    name: "Samsung 65\" QLED 4K Smart TV",
    description: "Quantum processor with AI upscaling, HDR, and smart features",
    category: "Electronics",
    images: [
      "https://example.com/samsung-tv-1.jpg",
      "https://example.com/samsung-tv-2.jpg"
    ],
    prices: [
      {
        storeName: "Samsung Store",
        price: 108549,
        productUrl: "https://samsung.com/in/qled-tv"
      },
      {
        storeName: "Flipkart",
        price: 100199,
        productUrl: "https://flipkart.com/samsung-qled"
      },
      {
        storeName: "Amazon",
        price: 96024,
        productUrl: "https://amazon.in/samsung-qled"
      }
    ],
    rating: 4.6,
    reviews: [
      {
        userName: "Sarah M.",
        comment: "Amazing picture quality!",
        rating: 5,
        createdAt: new Date()
      }
    ]
  }
];

const seedDatabase = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/price-comparison');
    console.log('Connected to MongoDB');

    await Product.deleteMany({});
    console.log('Cleared existing products');

    await Product.insertMany(sampleProducts);
    console.log('Sample products inserted successfully');

    mongoose.connection.close();
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedDatabase();
