{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add token to requests if available\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle token expiration\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport const authService = {\n  // Register new user\n  register: async (name, email, password, phone) => {\n    const response = await api.post('/auth/register', {\n      name,\n      email,\n      password,\n      phone\n    });\n    return response.data;\n  },\n  // Login user\n  login: async (email, password) => {\n    const response = await api.post('/auth/login', {\n      email,\n      password\n    });\n    return response.data;\n  },\n  // Get current user profile\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data.user;\n  },\n  // Update user profile\n  updateProfile: async userData => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data.user;\n  },\n  // Change password\n  changePassword: async (currentPassword, newPassword) => {\n    await api.put('/auth/change-password', {\n      currentPassword,\n      newPassword\n    });\n  },\n  // Logout\n  logout: async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API error:', error);\n    }\n  },\n  // Check if email exists\n  checkEmail: async email => {\n    try {\n      const response = await api.post('/auth/check-email', {\n        email\n      });\n      return response.data.exists;\n    } catch (error) {\n      return false;\n    }\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "authService", "register", "name", "email", "password", "phone", "post", "data", "login", "getProfile", "get", "user", "updateProfile", "userData", "put", "changePassword", "currentPassword", "newPassword", "logout", "console", "checkEmail", "exists"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/authService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { User } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add token to requests if available\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle token expiration\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\ninterface LoginResponse {\n  message: string;\n  token: string;\n  user: User;\n}\n\ninterface RegisterResponse {\n  message: string;\n  token: string;\n  user: User;\n}\n\nexport const authService = {\n  // Register new user\n  register: async (name: string, email: string, password: string, phone?: string): Promise<RegisterResponse> => {\n    const response = await api.post('/auth/register', {\n      name,\n      email,\n      password,\n      phone\n    });\n    return response.data;\n  },\n\n  // Login user\n  login: async (email: string, password: string): Promise<LoginResponse> => {\n    const response = await api.post('/auth/login', {\n      email,\n      password\n    });\n    return response.data;\n  },\n\n  // Get current user profile\n  getProfile: async (): Promise<User> => {\n    const response = await api.get('/auth/profile');\n    return response.data.user;\n  },\n\n  // Update user profile\n  updateProfile: async (userData: Partial<User>): Promise<User> => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data.user;\n  },\n\n  // Change password\n  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {\n    await api.put('/auth/change-password', {\n      currentPassword,\n      newPassword\n    });\n  },\n\n  // Logout\n  logout: async (): Promise<void> => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API error:', error);\n    }\n  },\n\n  // Check if email exists\n  checkEmail: async (email: string): Promise<boolean> => {\n    try {\n      const response = await api.post('/auth/check-email', { email });\n      return response.data.exists;\n    } catch (error) {\n      return false;\n    }\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACI,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAC1BM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;AAcD,OAAO,MAAMS,WAAW,GAAG;EACzB;EACAC,QAAQ,EAAE,MAAAA,CAAOC,IAAY,EAAEC,KAAa,EAAEC,QAAgB,EAAEC,KAAc,KAAgC;IAC5G,MAAMf,QAAQ,GAAG,MAAMZ,GAAG,CAAC4B,IAAI,CAAC,gBAAgB,EAAE;MAChDJ,IAAI;MACJC,KAAK;MACLC,QAAQ;MACRC;IACF,CAAC,CAAC;IACF,OAAOf,QAAQ,CAACiB,IAAI;EACtB,CAAC;EAED;EACAC,KAAK,EAAE,MAAAA,CAAOL,KAAa,EAAEC,QAAgB,KAA6B;IACxE,MAAMd,QAAQ,GAAG,MAAMZ,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAE;MAC7CH,KAAK;MACLC;IACF,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACiB,IAAI;EACtB,CAAC;EAED;EACAE,UAAU,EAAE,MAAAA,CAAA,KAA2B;IACrC,MAAMnB,QAAQ,GAAG,MAAMZ,GAAG,CAACgC,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOpB,QAAQ,CAACiB,IAAI,CAACI,IAAI;EAC3B,CAAC;EAED;EACAC,aAAa,EAAE,MAAOC,QAAuB,IAAoB;IAC/D,MAAMvB,QAAQ,GAAG,MAAMZ,GAAG,CAACoC,GAAG,CAAC,eAAe,EAAED,QAAQ,CAAC;IACzD,OAAOvB,QAAQ,CAACiB,IAAI,CAACI,IAAI;EAC3B,CAAC;EAED;EACAI,cAAc,EAAE,MAAAA,CAAOC,eAAuB,EAAEC,WAAmB,KAAoB;IACrF,MAAMvC,GAAG,CAACoC,GAAG,CAAC,uBAAuB,EAAE;MACrCE,eAAe;MACfC;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,MAAM,EAAE,MAAAA,CAAA,KAA2B;IACjC,IAAI;MACF,MAAMxC,GAAG,CAAC4B,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd;MACA4B,OAAO,CAAC5B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED;EACA6B,UAAU,EAAE,MAAOjB,KAAa,IAAuB;IACrD,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMZ,GAAG,CAAC4B,IAAI,CAAC,mBAAmB,EAAE;QAAEH;MAAM,CAAC,CAAC;MAC/D,OAAOb,QAAQ,CAACiB,IAAI,CAACc,MAAM;IAC7B,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;AACF,CAAC;AAED,eAAeb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}