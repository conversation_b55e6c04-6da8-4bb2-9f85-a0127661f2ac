{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\UserStatsContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\nimport { useUserStats } from '../hooks/useUserStats';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserStatsContext = /*#__PURE__*/createContext(undefined);\nexport const UserStatsProvider = ({\n  children\n}) => {\n  _s();\n  const userStatsHook = useUserStats();\n  return /*#__PURE__*/_jsxDEV(UserStatsContext.Provider, {\n    value: userStatsHook,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(UserStatsProvider, \"EEUMaK0mc9MPWiww4PWwoIJVLMU=\", false, function () {\n  return [useUserStats];\n});\n_c = UserStatsProvider;\nexport const useUserStatsContext = () => {\n  _s2();\n  const context = useContext(UserStatsContext);\n  if (context === undefined) {\n    throw new Error('useUserStatsContext must be used within a UserStatsProvider');\n  }\n  return context;\n};\n_s2(useUserStatsContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default UserStatsContext;\nvar _c;\n$RefreshReg$(_c, \"UserStatsProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useUserStats", "jsxDEV", "_jsxDEV", "UserStatsContext", "undefined", "UserStatsProvider", "children", "_s", "userStatsHook", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useUserStatsContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/UserStatsContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, ReactNode } from 'react';\nimport { useUserStats, UserStats } from '../hooks/useUserStats';\n\ninterface UserStatsContextType {\n  stats: UserStats;\n  loading: boolean;\n  error: string | null;\n  refreshStats: () => void;\n  updateStat: (statName: keyof UserStats, value: number) => void;\n  incrementStat: (statName: keyof UserStats, increment?: number) => void;\n}\n\nconst UserStatsContext = createContext<UserStatsContextType | undefined>(undefined);\n\ninterface UserStatsProviderProps {\n  children: ReactNode;\n}\n\nexport const UserStatsProvider: React.FC<UserStatsProviderProps> = ({ children }) => {\n  const userStatsHook = useUserStats();\n\n  return (\n    <UserStatsContext.Provider value={userStatsHook}>\n      {children}\n    </UserStatsContext.Provider>\n  );\n};\n\nexport const useUserStatsContext = () => {\n  const context = useContext(UserStatsContext);\n  if (context === undefined) {\n    throw new Error('useUserStatsContext must be used within a UserStatsProvider');\n  }\n  return context;\n};\n\nexport default UserStatsContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAmB,OAAO;AACnE,SAASC,YAAY,QAAmB,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWhE,MAAMC,gBAAgB,gBAAGL,aAAa,CAAmCM,SAAS,CAAC;AAMnF,OAAO,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAMC,aAAa,GAAGR,YAAY,CAAC,CAAC;EAEpC,oBACEE,OAAA,CAACC,gBAAgB,CAACM,QAAQ;IAACC,KAAK,EAAEF,aAAc;IAAAF,QAAA,EAC7CA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAACP,EAAA,CARWF,iBAAmD;EAAA,QACxCL,YAAY;AAAA;AAAAe,EAAA,GADvBV,iBAAmD;AAUhE,OAAO,MAAMW,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvC,MAAMC,OAAO,GAAGnB,UAAU,CAACI,gBAAgB,CAAC;EAC5C,IAAIe,OAAO,KAAKd,SAAS,EAAE;IACzB,MAAM,IAAIe,KAAK,CAAC,6DAA6D,CAAC;EAChF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,mBAAmB;AAQhC,eAAeb,gBAAgB;AAAC,IAAAY,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}