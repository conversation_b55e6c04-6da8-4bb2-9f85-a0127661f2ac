{"ast": null, "code": "// Currency conversion and formatting utilities\n\n// Approximate USD to INR conversion rate (you can make this dynamic later)\nconst USD_TO_INR_RATE = 83.5;\n\n/**\n * Convert USD price to INR\n */\nexport const convertUsdToInr = usdPrice => {\n  return usdPrice * USD_TO_INR_RATE;\n};\n\n/**\n * Format price in Indian Rupees\n */\nexport const formatPriceInr = price => {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  }).format(price);\n};\n\n/**\n * Format price with Indian number system (lakhs, crores)\n */\nexport const formatPriceIndian = price => {\n  const formatter = new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  });\n  return formatter.format(price);\n};", "map": {"version": 3, "names": ["USD_TO_INR_RATE", "convertUsdToInr", "usdPrice", "formatPriceInr", "price", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatPriceIndian", "formatter"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/utils/currency.ts"], "sourcesContent": ["// Currency conversion and formatting utilities\n\n// Approximate USD to INR conversion rate (you can make this dynamic later)\nconst USD_TO_INR_RATE = 83.5;\n\n/**\n * Convert USD price to INR\n */\nexport const convertUsdToInr = (usdPrice: number): number => {\n  return usdPrice * USD_TO_INR_RATE;\n};\n\n/**\n * Format price in Indian Rupees\n */\nexport const formatPriceInr = (price: number): string => {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(price);\n};\n\n/**\n * Format price with Indian number system (lakhs, crores)\n */\nexport const formatPriceIndian = (price: number): string => {\n  const formatter = new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  });\n  \n  return formatter.format(price);\n};\n"], "mappings": "AAAA;;AAEA;AACA,MAAMA,eAAe,GAAG,IAAI;;AAE5B;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAIC,QAAgB,IAAa;EAC3D,OAAOA,QAAQ,GAAGF,eAAe;AACnC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,cAAc,GAAIC,KAAa,IAAa;EACvD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC;AAClB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMQ,iBAAiB,GAAIR,KAAa,IAAa;EAC1D,MAAMS,SAAS,GAAG,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAC/CC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC;EAEF,OAAOG,SAAS,CAACF,MAAM,CAACP,KAAK,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}