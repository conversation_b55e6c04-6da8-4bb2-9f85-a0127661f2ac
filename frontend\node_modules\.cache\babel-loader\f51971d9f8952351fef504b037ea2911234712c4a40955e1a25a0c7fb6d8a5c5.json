{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    stats,\n    loading: statsLoading,\n    refreshStats\n  } = useUserStats();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSave = async () => {\n    try {\n      // Update profile using the API service\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Please sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: \"You need to be signed in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: \"My Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), !isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsEditing(true),\n              className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(PencilIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), \"Edit Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2 flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-12 w-12 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-400\",\n                  children: [\"Member since \", new Date().getFullYear()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), \"Full Name\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.name || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(EnvelopeIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), \"Email Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.email || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), \"Phone Number\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.phone || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), \"Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.address || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 dark:border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n              children: \"Account Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: refreshStats,\n              disabled: statsLoading,\n              className: \"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\",\n              title: \"Refresh statistics\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowPathIcon, {\n                className: `h-4 w-4 mr-1 ${statsLoading ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), \"Refresh\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), statsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center py-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Spent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Wishlist Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.wishlistCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Reviews Written\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.reviewsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"giIoYYuWBdomvbuE7e10yK06LZw=\", true, function () {\n  return [useAuth];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "userService", "UserIcon", "EnvelopeIcon", "PhoneIcon", "MapPinIcon", "PencilIcon", "ArrowPathIcon", "formatPriceIndian", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "user", "isAuthenticated", "stats", "loading", "statsLoading", "refreshStats", "useUserStats", "isEditing", "setIsEditing", "formData", "setFormData", "name", "email", "phone", "address", "handleInputChange", "e", "value", "target", "prev", "handleSave", "updateProfile", "alert", "error", "console", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "Date", "getFullYear", "type", "onChange", "rows", "disabled", "title", "totalOrders", "totalSpent", "wishlistCount", "reviewsCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProfilePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { useUserStatsContext } from '../contexts/UserStatsContext';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\n\nconst ProfilePage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { stats, loading: statsLoading, refreshStats } = useUserStats();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSave = async () => {\n    try {\n      // Update profile using the API service\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Please sign in</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            You need to be signed in to view your profile.\n          </p>\n          <div className=\"mt-6\">\n            <a\n              href=\"/login\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Sign in\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n          {/* Header */}\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">My Profile</h1>\n              {!isEditing && (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <PencilIcon className=\"h-4 w-4 mr-2\" />\n                  Edit Profile\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Profile Content */}\n          <div className=\"px-6 py-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Profile Picture */}\n              <div className=\"md:col-span-2 flex items-center space-x-6\">\n                <div className=\"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                  <UserIcon className=\"h-12 w-12 text-gray-500 dark:text-gray-400\" />\n                </div>\n                <div>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                    {user?.name || 'User'}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Member since {new Date().getFullYear()}</p>\n                </div>\n              </div>\n\n              {/* Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <UserIcon className=\"h-4 w-4 inline mr-2\" />\n                  Full Name\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.name || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Email */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <EnvelopeIcon className=\"h-4 w-4 inline mr-2\" />\n                  Email Address\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.email || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Phone */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <PhoneIcon className=\"h-4 w-4 inline mr-2\" />\n                  Phone Number\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.phone || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Address */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 inline mr-2\" />\n                  Address\n                </label>\n                {isEditing ? (\n                  <textarea\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.address || 'Not provided'}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {isEditing && (\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  Save Changes\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Account Statistics */}\n          <div className=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Account Statistics</h3>\n              <button\n                onClick={refreshStats}\n                disabled={statsLoading}\n                className=\"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\"\n                title=\"Refresh statistics\"\n              >\n                <ArrowPathIcon className={`h-4 w-4 mr-1 ${statsLoading ? 'animate-spin' : ''}`} />\n                Refresh\n              </button>\n            </div>\n            {statsLoading ? (\n              <div className=\"flex justify-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"></div>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Orders</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.totalOrders}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Spent</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                    {stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'}\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Wishlist Items</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.wishlistCount}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Reviews Written</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.reviewsCount}</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,QAAQ,6BAA6B;AACtH,SAASC,iBAAiB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEe,KAAK;IAAEC,OAAO,EAAEC,YAAY;IAAEC;EAAa,CAAC,GAAGC,YAAY,CAAC,CAAC;EACrE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF5B,SAAS,CAAC,MAAM;IACd,IAAIc,IAAI,EAAE;MACRU,WAAW,CAAC;QACVC,IAAI,EAAEX,IAAI,CAACW,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAEd,IAAI,CAACc,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;EAEV,MAAMe,iBAAiB,GAAIC,CAA4D,IAAK;IAC1F,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAGM;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAMhC,WAAW,CAACiC,aAAa,CAACZ,QAAQ,CAAC;MACzCD,YAAY,CAAC,KAAK,CAAC;MACnB;MACAc,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CD,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIzB,IAAI,EAAE;MACRU,WAAW,CAAC;QACVC,IAAI,EAAEX,IAAI,CAACW,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAEd,IAAI,CAACc,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACP,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAK6B,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxF9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA,CAACR,QAAQ;UAACqC,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDlC,OAAA;UAAI6B,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7FlC,OAAA;UAAG6B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlC,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB9B,OAAA;YACEmC,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,EAC3J;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzE9B,OAAA;MAAK6B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D9B,OAAA;QAAK6B,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1D9B,OAAA;UAAK6B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtE9B,OAAA;YAAK6B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9B,OAAA;cAAI6B,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClF,CAACxB,SAAS,iBACTV,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,IAAI,CAAE;cAClCkB,SAAS,EAAC,8OAA8O;cAAAC,QAAA,gBAExP9B,OAAA,CAACJ,UAAU;gBAACiC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9B,OAAA;YAAK6B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpD9B,OAAA;cAAK6B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD9B,OAAA;gBAAK6B,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,eACnG9B,OAAA,CAACR,QAAQ;kBAACqC,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI6B,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,KAAI;gBAAM;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACLlC,OAAA;kBAAG6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,eAAa,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF9B,OAAA,CAACR,QAAQ;kBAACqC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRV,OAAA;gBACEuC,IAAI,EAAC,MAAM;gBACXzB,IAAI,EAAC,MAAM;gBACXM,KAAK,EAAER,QAAQ,CAACE,IAAK;gBACrB0B,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFlC,OAAA;gBAAG6B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACE,IAAI,IAAI;cAAc;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF9B,OAAA,CAACP,YAAY;kBAACoC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRV,OAAA;gBACEuC,IAAI,EAAC,OAAO;gBACZzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACG,KAAM;gBACtByB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFlC,OAAA;gBAAG6B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACG,KAAK,IAAI;cAAc;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF9B,OAAA,CAACN,SAAS;kBAACmC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRV,OAAA;gBACEuC,IAAI,EAAC,KAAK;gBACVzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACI,KAAM;gBACtBwB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFlC,OAAA;gBAAG6B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACI,KAAK,IAAI;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF9B,OAAA,CAACL,UAAU;kBAACkC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRV,OAAA;gBACEc,IAAI,EAAC,SAAS;gBACdM,KAAK,EAAER,QAAQ,CAACK,OAAQ;gBACxBuB,QAAQ,EAAEtB,iBAAkB;gBAC5BuB,IAAI,EAAE,CAAE;gBACRZ,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFlC,OAAA;gBAAG6B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACK,OAAO,IAAI;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC7F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxB,SAAS,iBACRV,OAAA;YAAK6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C9B,OAAA;cACEoC,OAAO,EAAER,YAAa;cACtBC,SAAS,EAAC,qNAAqN;cAAAC,QAAA,EAChO;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlC,OAAA;cACEoC,OAAO,EAAEb,UAAW;cACpBM,SAAS,EAAC,yIAAyI;cAAAC,QAAA,EACpJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtE9B,OAAA;YAAK6B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9B,OAAA;cAAI6B,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5FlC,OAAA;cACEoC,OAAO,EAAE5B,YAAa;cACtBkC,QAAQ,EAAEnC,YAAa;cACvBsB,SAAS,EAAC,kQAAkQ;cAC5Qc,KAAK,EAAC,oBAAoB;cAAAb,QAAA,gBAE1B9B,OAAA,CAACH,aAAa;gBAACgC,SAAS,EAAE,gBAAgBtB,YAAY,GAAG,cAAc,GAAG,EAAE;cAAG;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEpF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL3B,YAAY,gBACXP,OAAA;YAAK6B,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvC9B,OAAA;cAAK6B,SAAS,EAAC;YAAmF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,gBAENlC,OAAA;YAAK6B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD9B,OAAA;cAAK6B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9B,OAAA;gBAAG6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxElC,OAAA;gBAAG6B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEzB,KAAK,CAACuC;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9B,OAAA;gBAAG6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvElC,OAAA;gBAAG6B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAC/DzB,KAAK,CAACwC,UAAU,GAAG,CAAC,GAAG/C,iBAAiB,CAACO,KAAK,CAACwC,UAAU,CAAC,GAAG;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9B,OAAA;gBAAG6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1ElC,OAAA;gBAAG6B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEzB,KAAK,CAACyC;cAAa;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9B,OAAA;gBAAG6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3ElC,OAAA;gBAAG6B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEzB,KAAK,CAAC0C;cAAY;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3PID,WAAqB;EAAA,QACSX,OAAO;AAAA;AAAA0D,EAAA,GADrC/C,WAAqB;AA6P3B,eAAeA,WAAW;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}