{"ast": null, "code": "// Mock data for development\nconst mockProducts = [{\n  _id: '1',\n  name: 'Sony WH-1000XM4 Wireless Headphones',\n  description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg', 'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 29058,\n    productUrl: 'https://amazon.in'\n  }, {\n    storeName: 'Flipkart',\n    price: 29224,\n    productUrl: 'https://flipkart.com'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'John Doe',\n    rating: 5,\n    comment: 'Best headphones I have ever owned!',\n    createdAt: new Date('2025-04-14')\n  }, {\n    userName: '<PERSON><PERSON>',\n    rating: 5,\n    comment: 'Excellent sound quality and battery life. Worth every rupee!',\n    createdAt: new Date('2025-04-12')\n  }, {\n    userName: '<PERSON><PERSON>',\n    rating: 4,\n    comment: 'Great for work from home. Noise cancellation is amazing.',\n    createdAt: new Date('2025-04-10')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '2',\n  name: 'Samsung 55\" 4K Smart TV',\n  description: 'Crystal clear display with smart features',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 41582,\n    productUrl: 'https://amazon.in'\n  }, {\n    storeName: 'Flipkart',\n    price: 41749,\n    productUrl: 'https://flipkart.com'\n  }],\n  rating: 4.5,\n  reviews: [{\n    userName: 'Amit Kumar',\n    rating: 5,\n    comment: 'Amazing laptop! Perfect for gaming and work.',\n    createdAt: new Date('2025-04-13')\n  }, {\n    userName: 'Sneha Patel',\n    rating: 4,\n    comment: 'Good performance but gets a bit warm during heavy usage.',\n    createdAt: new Date('2025-04-11')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '5',\n  name: 'Nike Air Max 270',\n  description: 'Comfortable athletic shoes with Air cushioning',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n  prices: [{\n    storeName: 'Nike',\n    price: 12525,\n    productUrl: 'https://www.nike.com/in/t/air-max-270-mens-shoes-KkLcGR/AH8050-002'\n  }, {\n    storeName: 'Amazon',\n    price: 12524,\n    productUrl: 'https://amazon.in'\n  }],\n  rating: 4.6,\n  reviews: [{\n    userName: 'Ravi Singh',\n    rating: 5,\n    comment: 'Super comfortable shoes! Great for running and daily wear.',\n    createdAt: new Date('2025-04-09')\n  }, {\n    userName: 'Neha Gupta',\n    rating: 4,\n    comment: 'Good quality but sizing runs a bit small.',\n    createdAt: new Date('2025-04-08')\n  }, {\n    userName: 'Karan Joshi',\n    rating: 5,\n    comment: 'Love the design and comfort. Highly recommended!',\n    createdAt: new Date('2025-04-07')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '6',\n  name: 'Levi\\'s 501 Original Fit Jeans',\n  description: 'Classic straight leg jeans',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 5009,\n    productUrl: 'https://amazon.in'\n  }, {\n    storeName: 'Myntra',\n    price: 5803,\n    productUrl: 'https://myntra.com'\n  }],\n  rating: 4.5,\n  reviews: [{\n    userName: 'Deepika Rao',\n    rating: 4,\n    comment: 'Good quality jeans. Fit is perfect and comfortable.',\n    createdAt: new Date('2025-04-06')\n  }, {\n    userName: 'Suresh Kumar',\n    rating: 5,\n    comment: 'Classic Levis quality. These jeans last for years!',\n    createdAt: new Date('2025-04-05')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '7',\n  name: 'Ray-Ban Aviator Sunglasses',\n  description: 'Classic aviator style with UV protection',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1572635196237-14b3f281503f?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 154.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Sunglass Hut',\n    price: 169.00,\n    productUrl: 'https://www.sunglasshut.com/us/ray-ban-aviator-classic-gold/8052896020578'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Arjun Malhotra',\n    rating: 5,\n    comment: 'Classic aviators! Great quality and style.',\n    createdAt: new Date('2025-04-04')\n  }, {\n    userName: 'Pooja Agarwal',\n    rating: 4,\n    comment: 'Good sunglasses but a bit pricey. UV protection is excellent.',\n    createdAt: new Date('2025-04-03')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '8',\n  name: 'Casio G-Shock Watch',\n  description: 'Durable digital watch with multiple features',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1533139502658-0198f920d8e8?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 99.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Casio',\n    price: 110.00,\n    productUrl: 'https://www.casio.com/us/watches/gshock/product.GA2100-1A/'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Vikash Sharma',\n    rating: 5,\n    comment: 'Tough watch! Perfect for outdoor activities.',\n    createdAt: new Date('2025-04-02')\n  }, {\n    userName: 'Manisha Jain',\n    rating: 5,\n    comment: 'Bought for my husband. He loves all the features!',\n    createdAt: new Date('2025-04-01')\n  }, {\n    userName: 'Rohit Verma',\n    rating: 4,\n    comment: 'Good watch but takes time to learn all functions.',\n    createdAt: new Date('2025-03-30')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '17',\n  name: 'Adidas Ultraboost 21',\n  description: 'Premium running shoes with responsive cushioning',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Adidas',\n    price: 180.00,\n    productUrl: 'https://www.adidas.com/us/ultraboost-21-shoes/FY0377.html'\n  }, {\n    storeName: 'Amazon',\n    price: 169.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '18',\n  name: 'Michael Kors Leather Crossbody Bag',\n  description: 'Elegant leather bag with adjustable strap',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1548036328-c9fa89d128fa?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Michael Kors',\n    price: 298.00,\n    productUrl: 'https://www.michaelkors.com/leather-crossbody-bag/_/R-30F2G7CM2L'\n  }, {\n    storeName: 'Amazon',\n    price: 278.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n},\n// Home & Garden Category\n{\n  _id: '19',\n  name: 'Philips Hue Smart LED Bulb Set',\n  description: 'Color-changing smart LED bulbs with wireless control',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1563461660947-507ef49e9c47?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Philips',\n    price: 199.99,\n    productUrl: 'https://www.philips-hue.com/en-us/p/hue-white-and-color-ambiance-starter-kit-e26/046677555334'\n  }, {\n    storeName: 'Amazon',\n    price: 189.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 194.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '20',\n  name: 'KitchenAid Stand Mixer',\n  description: 'Professional 5-quart stand mixer with multiple attachments',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1544441893-675973e31985?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'KitchenAid',\n    price: 399.99,\n    productUrl: 'https://www.kitchenaid.com/countertop-appliances/stand-mixers/tilt-head-stand-mixers/p.artisan-series-5-quart-tilt-head-stand-mixer.ksm150psob.html'\n  }, {\n    storeName: 'Amazon',\n    price: 379.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Williams Sonoma',\n    price: 429.99,\n    productUrl: 'https://www.williams-sonoma.com/products/kitchenaid-artisan-stand-mixer/'\n  }],\n  rating: 4.9,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '21',\n  name: 'DEWALT Power Tool Set',\n  description: '20V MAX Cordless Drill Combo Kit with 2 batteries',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1581244277943-fe4a9c777189?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Home Depot',\n    price: 299.00,\n    productUrl: 'https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-Charger-and-Bag-DCK240C2/204373168'\n  }, {\n    storeName: 'Amazon',\n    price: 289.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Lowes',\n    price: 294.99,\n    productUrl: 'https://www.lowes.com/pd/DEWALT-20-Volt-Max-2-Tool-Combo-Kit/1000191169'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '22',\n  name: 'Dyson V15 Detect Vacuum',\n  description: 'Cordless vacuum with laser dust detection',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1558317374-067fb5f30001?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Dyson',\n    price: 699.99,\n    productUrl: 'https://www.dyson.com/vacuum-cleaners/stick-vacuum-cleaners/dyson-v15-detect-nickel-yellow'\n  }, {\n    storeName: 'Amazon',\n    price: 679.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 689.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n},\n// Sports Category\n{\n  _id: '23',\n  name: 'NordicTrack Commercial 1750 Treadmill',\n  description: 'Smart treadmill with 14-inch HD touchscreen and iFit integration',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1595078475328-1ab05d0a6a0e?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'NordicTrack',\n    price: 1899.00,\n    productUrl: 'https://www.nordictrack.com/treadmills/commercial-1750-treadmill'\n  }, {\n    storeName: 'Amazon',\n    price: 1799.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Dick\\'s Sporting Goods',\n    price: 1849.99,\n    productUrl: 'https://dickssportinggoods.com'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '24',\n  name: 'Wilson Evolution Basketball',\n  description: 'Official size indoor game basketball with moisture-wicking technology',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1519861531473-9200262188bf?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Wilson',\n    price: 59.99,\n    productUrl: 'https://www.wilson.com/en-us/product/evolution-basketball-WTB0516'\n  }, {\n    storeName: 'Amazon',\n    price: 54.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Dick\\'s Sporting Goods',\n    price: 57.99,\n    productUrl: 'https://dickssportinggoods.com'\n  }],\n  rating: 4.9,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '25',\n  name: 'Garmin Forerunner 945 GPS Watch',\n  description: 'Advanced running watch with full-color mapping and training metrics',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Garmin',\n    price: 599.99,\n    productUrl: 'https://garmin.com'\n  }, {\n    storeName: 'Amazon',\n    price: 579.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'REI',\n    price: 589.99,\n    productUrl: 'https://rei.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '26',\n  name: 'Yeti Tundra 45 Cooler',\n  description: 'Premium hard cooler for outdoor activities and camping',\n  category: 'Sports',\n  images: ['https://picsum.photos/id/26/800/800'],\n  prices: [{\n    storeName: 'Yeti',\n    price: 325.00,\n    productUrl: 'https://www.yeti.com/en_US/coolers/hard-coolers/tundra-45-cooler/YT45.html'\n  }, {\n    storeName: 'Amazon',\n    price: 299.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'REI',\n    price: 319.99,\n    productUrl: 'https://rei.com'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}];\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async filters => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let filteredProducts = [...mockProducts];\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower));\n      }\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => {\n          var _filters$category;\n          return p.category.toLowerCase() === ((_filters$category = filters.category) === null || _filters$category === void 0 ? void 0 : _filters$category.toLowerCase());\n        });\n      }\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0));\n      }\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity));\n      }\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price)));\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price)));\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n    return filteredProducts;\n  },\n  // Get single product by ID\n  getProduct: async id => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n  // Add a review to a product\n  addReview: async (productId, review) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n    product.reviews.push(newReview);\n\n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n    return product;\n  },\n  // Search products\n  searchProducts: async query => {\n    return productService.getProducts({\n      search: query\n    });\n  },\n  // Get products by category\n  getProductsByCategory: async category => {\n    return productService.getProducts({\n      category\n    });\n  },\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};", "map": {"version": 3, "names": ["mockProducts", "_id", "name", "description", "category", "images", "prices", "storeName", "price", "productUrl", "rating", "reviews", "userName", "comment", "createdAt", "Date", "onSale", "productService", "getProducts", "filters", "Promise", "resolve", "setTimeout", "filteredProducts", "search", "searchLower", "toLowerCase", "filter", "p", "includes", "_filters$category", "minPrice", "Math", "min", "map", "maxPrice", "max", "Infinity", "sort", "a", "b", "getProduct", "id", "product", "find", "Error", "add<PERSON>eview", "productId", "review", "newReview", "push", "reduce", "sum", "r", "length", "searchProducts", "query", "getProductsByCategory", "getTodaysDeals"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/api.ts"], "sourcesContent": ["import { Product, ProductFilters, Review } from '../types';\n\n// Mock data for development\nconst mockProducts: Product[] = [\n  {\n    _id: '1',\n    name: 'Sony WH-1000XM4 Wireless Headphones',\n    description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg',\n      'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 29058,\n        productUrl: 'https://amazon.in'\n      },\n      {\n        storeName: 'Flipkart',\n        price: 29224,\n        productUrl: 'https://flipkart.com'\n      }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'John Doe',\n        rating: 5,\n        comment: 'Best headphones I have ever owned!',\n        createdAt: new Date('2025-04-14')\n      },\n      {\n        userName: '<PERSON><PERSON>',\n        rating: 5,\n        comment: 'Excellent sound quality and battery life. Worth every rupee!',\n        createdAt: new Date('2025-04-12')\n      },\n      {\n        userName: '<PERSON><PERSON>',\n        rating: 4,\n        comment: 'Great for work from home. Noise cancellation is amazing.',\n        createdAt: new Date('2025-04-10')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '2',\n    name: 'Samsung 55\" 4K Smart TV',\n    description: 'Crystal clear display with smart features',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 41582,\n        productUrl: 'https://amazon.in'\n      },\n      {\n        storeName: 'Flipkart',\n        price: 41749,\n        productUrl: 'https://flipkart.com'\n      }\n    ],\n    rating: 4.5,\n    reviews: [\n      {\n        userName: 'Amit Kumar',\n        rating: 5,\n        comment: 'Amazing laptop! Perfect for gaming and work.',\n        createdAt: new Date('2025-04-13')\n      },\n      {\n        userName: 'Sneha Patel',\n        rating: 4,\n        comment: 'Good performance but gets a bit warm during heavy usage.',\n        createdAt: new Date('2025-04-11')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '5',\n    name: 'Nike Air Max 270',\n    description: 'Comfortable athletic shoes with Air cushioning',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n    prices: [\n      { storeName: 'Nike', price: 12525, productUrl: 'https://www.nike.com/in/t/air-max-270-mens-shoes-KkLcGR/AH8050-002' },\n      { storeName: 'Amazon', price: 12524, productUrl: 'https://amazon.in' }\n    ],\n    rating: 4.6,\n    reviews: [\n      {\n        userName: 'Ravi Singh',\n        rating: 5,\n        comment: 'Super comfortable shoes! Great for running and daily wear.',\n        createdAt: new Date('2025-04-09')\n      },\n      {\n        userName: 'Neha Gupta',\n        rating: 4,\n        comment: 'Good quality but sizing runs a bit small.',\n        createdAt: new Date('2025-04-08')\n      },\n      {\n        userName: 'Karan Joshi',\n        rating: 5,\n        comment: 'Love the design and comfort. Highly recommended!',\n        createdAt: new Date('2025-04-07')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '6',\n    name: 'Levi\\'s 501 Original Fit Jeans',\n    description: 'Classic straight leg jeans',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 5009, productUrl: 'https://amazon.in' },\n      { storeName: 'Myntra', price: 5803, productUrl: 'https://myntra.com' }\n    ],\n    rating: 4.5,\n    reviews: [\n      {\n        userName: 'Deepika Rao',\n        rating: 4,\n        comment: 'Good quality jeans. Fit is perfect and comfortable.',\n        createdAt: new Date('2025-04-06')\n      },\n      {\n        userName: 'Suresh Kumar',\n        rating: 5,\n        comment: 'Classic Levis quality. These jeans last for years!',\n        createdAt: new Date('2025-04-05')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '7',\n    name: 'Ray-Ban Aviator Sunglasses',\n    description: 'Classic aviator style with UV protection',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1572635196237-14b3f281503f?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 154.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Sunglass Hut', price: 169.00, productUrl: 'https://www.sunglasshut.com/us/ray-ban-aviator-classic-gold/8052896020578' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Arjun Malhotra',\n        rating: 5,\n        comment: 'Classic aviators! Great quality and style.',\n        createdAt: new Date('2025-04-04')\n      },\n      {\n        userName: 'Pooja Agarwal',\n        rating: 4,\n        comment: 'Good sunglasses but a bit pricey. UV protection is excellent.',\n        createdAt: new Date('2025-04-03')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '8',\n    name: 'Casio G-Shock Watch',\n    description: 'Durable digital watch with multiple features',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1533139502658-0198f920d8e8?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 99.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Casio', price: 110.00, productUrl: 'https://www.casio.com/us/watches/gshock/product.GA2100-1A/' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Vikash Sharma',\n        rating: 5,\n        comment: 'Tough watch! Perfect for outdoor activities.',\n        createdAt: new Date('2025-04-02')\n      },\n      {\n        userName: 'Manisha Jain',\n        rating: 5,\n        comment: 'Bought for my husband. He loves all the features!',\n        createdAt: new Date('2025-04-01')\n      },\n      {\n        userName: 'Rohit Verma',\n        rating: 4,\n        comment: 'Good watch but takes time to learn all functions.',\n        createdAt: new Date('2025-03-30')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '17',\n    name: 'Adidas Ultraboost 21',\n    description: 'Premium running shoes with responsive cushioning',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Adidas', price: 180.00, productUrl: 'https://www.adidas.com/us/ultraboost-21-shoes/FY0377.html' },\n      { storeName: 'Amazon', price: 169.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '18',\n    name: 'Michael Kors Leather Crossbody Bag',\n    description: 'Elegant leather bag with adjustable strap',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1548036328-c9fa89d128fa?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Michael Kors', price: 298.00, productUrl: 'https://www.michaelkors.com/leather-crossbody-bag/_/R-30F2G7CM2L' },\n      { storeName: 'Amazon', price: 278.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  // Home & Garden Category\n  {\n    _id: '19',\n    name: 'Philips Hue Smart LED Bulb Set',\n    description: 'Color-changing smart LED bulbs with wireless control',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1563461660947-507ef49e9c47?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Philips', price: 199.99, productUrl: 'https://www.philips-hue.com/en-us/p/hue-white-and-color-ambiance-starter-kit-e26/046677555334' },\n      { storeName: 'Amazon', price: 189.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Best Buy', price: 194.99, productUrl: 'https://bestbuy.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '20',\n    name: 'KitchenAid Stand Mixer',\n    description: 'Professional 5-quart stand mixer with multiple attachments',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1544441893-675973e31985?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'KitchenAid', price: 399.99, productUrl: 'https://www.kitchenaid.com/countertop-appliances/stand-mixers/tilt-head-stand-mixers/p.artisan-series-5-quart-tilt-head-stand-mixer.ksm150psob.html' },\n      { storeName: 'Amazon', price: 379.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Williams Sonoma', price: 429.99, productUrl: 'https://www.williams-sonoma.com/products/kitchenaid-artisan-stand-mixer/' }\n    ],\n    rating: 4.9,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '21',\n    name: 'DEWALT Power Tool Set',\n    description: '20V MAX Cordless Drill Combo Kit with 2 batteries',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1581244277943-fe4a9c777189?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Home Depot', price: 299.00, productUrl: 'https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-Charger-and-Bag-DCK240C2/204373168' },\n      { storeName: 'Amazon', price: 289.99, productUrl: 'https://amazon.com' },\n      { storeName: 'Lowes', price: 294.99, productUrl: 'https://www.lowes.com/pd/DEWALT-20-Volt-Max-2-Tool-Combo-Kit/1000191169' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '22',\n    name: 'Dyson V15 Detect Vacuum',\n    description: 'Cordless vacuum with laser dust detection',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1558317374-067fb5f30001?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Dyson', price: 699.99, productUrl: 'https://www.dyson.com/vacuum-cleaners/stick-vacuum-cleaners/dyson-v15-detect-nickel-yellow' },\n      { storeName: 'Amazon', price: 679.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Best Buy', price: 689.99, productUrl: 'https://bestbuy.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  // Sports Category\n  {\n    _id: '23',\n    name: 'NordicTrack Commercial 1750 Treadmill',\n    description: 'Smart treadmill with 14-inch HD touchscreen and iFit integration',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1595078475328-1ab05d0a6a0e?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'NordicTrack', price: 1899.00, productUrl: 'https://www.nordictrack.com/treadmills/commercial-1750-treadmill' },\n      { storeName: 'Amazon', price: 1799.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Dick\\'s Sporting Goods', price: 1849.99, productUrl: 'https://dickssportinggoods.com' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '24',\n    name: 'Wilson Evolution Basketball',\n    description: 'Official size indoor game basketball with moisture-wicking technology',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1519861531473-9200262188bf?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Wilson', price: 59.99, productUrl: 'https://www.wilson.com/en-us/product/evolution-basketball-WTB0516' },\n      { storeName: 'Amazon', price: 54.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Dick\\'s Sporting Goods', price: 57.99, productUrl: 'https://dickssportinggoods.com' }\n    ],\n    rating: 4.9,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '25',\n    name: 'Garmin Forerunner 945 GPS Watch',\n    description: 'Advanced running watch with full-color mapping and training metrics',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Garmin', price: 599.99, productUrl: 'https://garmin.com' },\n      { storeName: 'Amazon', price: 579.95, productUrl: 'https://amazon.com' },\n      { storeName: 'REI', price: 589.99, productUrl: 'https://rei.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '26',\n    name: 'Yeti Tundra 45 Cooler',\n    description: 'Premium hard cooler for outdoor activities and camping',\n    category: 'Sports',\n    images: ['https://picsum.photos/id/26/800/800'],\n    prices: [\n      { storeName: 'Yeti', price: 325.00, productUrl: 'https://www.yeti.com/en_US/coolers/hard-coolers/tundra-45-cooler/YT45.html' },\n      { storeName: 'Amazon', price: 299.99, productUrl: 'https://amazon.com' },\n      { storeName: 'REI', price: 319.99, productUrl: 'https://rei.com' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  }\n];\n\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async (filters?: ProductFilters) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    let filteredProducts = [...mockProducts];\n\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => \n          p.name.toLowerCase().includes(searchLower) ||\n          p.description.toLowerCase().includes(searchLower)\n        );\n      }\n\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => \n          p.category.toLowerCase() === filters.category?.toLowerCase()\n        );\n      }\n\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0)\n        );\n      }\n\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity)\n        );\n      }\n\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => \n              Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price))\n            );\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => \n              Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price))\n            );\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n\n    return filteredProducts;\n  },\n\n  // Get single product by ID\n  getProduct: async (id: string) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n\n  // Add a review to a product\n  addReview: async (productId: string, review: Omit<Review, 'createdAt'>) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n\n    product.reviews.push(newReview);\n    \n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n\n    return product;\n  },\n\n  // Search products\n  searchProducts: async (query: string) => {\n    return productService.getProducts({ search: query });\n  },\n\n  // Get products by category\n  getProductsByCategory: async (category: string) => {\n    return productService.getProducts({ category });\n  },\n\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};\n"], "mappings": "AAEA;AACA,MAAMA,YAAuB,GAAG,CAC9B;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qCAAqC;EAC3CC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,EACjE,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,EACD;IACEF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,CACF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,UAAU;IACpBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oCAAoC;IAC7CC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8DAA8D;IACvEC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,0DAA0D;IACnEC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,EACD;IACEF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,CACF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8CAA8C;IACvDC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,0DAA0D;IACnEC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,gDAAgD;EAC7DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,MAAM;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqE,CAAC,EACrH;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAoB,CAAC,CACvE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4DAA4D;IACrEC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,4BAA4B;EACzCC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAoB,CAAC,EACrE;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACvE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oDAAoD;IAC7DC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,4BAA4B;EAClCC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA4E,CAAC,CACtI;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,+DAA+D;IACxEC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvE;IAAEF,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6D,CAAC,CAChH;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8CAA8C;IACvDC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEH,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,sBAAsB;EAC5BC,WAAW,EAAE,kDAAkD;EAC/DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA4D,CAAC,EAC/G;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,oCAAoC;EAC1CC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAmE,CAAC,EAC5H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC;AACD;AACA;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,sDAAsD;EACnEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,SAAS;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAgG,CAAC,EACpJ;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAC5E;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,YAAY;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsJ,CAAC,EAC7M;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,iBAAiB;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA2E,CAAC,CACxI;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,YAAY;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqI,CAAC,EAC5L;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA0E,CAAC,CAC7H;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6F,CAAC,EAC/I;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAC5E;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC;AACD;AACA;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uCAAuC;EAC7CC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAmE,CAAC,EAC5H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACzE;IAAEF,SAAS,EAAE,wBAAwB;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAiC,CAAC,CACtG;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,6BAA6B;EACnCC,WAAW,EAAE,uEAAuE;EACpFC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAoE,CAAC,EACtH;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvE;IAAEF,SAAS,EAAE,wBAAwB;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAiC,CAAC,CACpG;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,iCAAiC;EACvCC,WAAW,EAAE,qEAAqE;EAClFC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAkB,CAAC,CACnE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,wDAAwD;EACrEC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,qCAAqC,CAAC;EAC/CC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6E,CAAC,EAC9H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAkB,CAAC,CACnE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAOC,OAAwB,IAAK;IAC/C;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIE,gBAAgB,GAAG,CAAC,GAAGvB,YAAY,CAAC;IAExC,IAAImB,OAAO,EAAE;MACX,IAAIA,OAAO,CAACK,MAAM,EAAE;QAClB,MAAMC,WAAW,GAAGN,OAAO,CAACK,MAAM,CAACE,WAAW,CAAC,CAAC;QAChDH,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CA,CAAC,CAAC1B,IAAI,CAACwB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC1CG,CAAC,CAACzB,WAAW,CAACuB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAClD,CAAC;MACH;MAEA,IAAIN,OAAO,CAACf,QAAQ,EAAE;QACpBmB,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC;UAAA,IAAAE,iBAAA;UAAA,OAC1CF,CAAC,CAACxB,QAAQ,CAACsB,WAAW,CAAC,CAAC,OAAAI,iBAAA,GAAKX,OAAO,CAACf,QAAQ,cAAA0B,iBAAA,uBAAhBA,iBAAA,CAAkBJ,WAAW,CAAC,CAAC;QAAA,CAC9D,CAAC;MACH;MAEA,IAAIP,OAAO,CAACY,QAAQ,EAAE;QACpBR,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACC,GAAG,CAAC,GAAGL,CAAC,CAACtB,MAAM,CAAC4B,GAAG,CAAC1B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKW,OAAO,CAACY,QAAQ,IAAI,CAAC,CAC3E,CAAC;MACH;MAEA,IAAIZ,OAAO,CAACgB,QAAQ,EAAE;QACpBZ,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACI,GAAG,CAAC,GAAGR,CAAC,CAACtB,MAAM,CAAC4B,GAAG,CAAC1B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKW,OAAO,CAACgB,QAAQ,IAAIE,QAAQ,CAClF,CAAC;MACH;MAEA,IAAIlB,OAAO,CAACmB,IAAI,EAAE;QAChB,QAAQnB,OAAO,CAACmB,IAAI;UAClB,KAAK,WAAW;YACdf,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACjC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAAC,GAAGwB,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAAClC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,YAAY;YACfe,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAAClC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAAC,GAAGwB,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACjC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,QAAQ;YACXe,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9B,MAAM,GAAG6B,CAAC,CAAC7B,MAAM,CAAC;YACpD;QACJ;MACF;IACF;IAEA,OAAOa,gBAAgB;EACzB,CAAC;EAED;EACAkB,UAAU,EAAE,MAAOC,EAAU,IAAK;IAChC;IACA,MAAM,IAAItB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG3C,YAAY,CAAC4C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAKyC,EAAE,CAAC;IACpD,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAClD,OAAOF,OAAO;EAChB,CAAC;EAED;EACAG,SAAS,EAAE,MAAAA,CAAOC,SAAiB,EAAEC,MAAiC,KAAK;IACzE;IACA,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG3C,YAAY,CAAC4C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAK8C,SAAS,CAAC;IAC3D,IAAI,CAACJ,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAElD,MAAMI,SAAS,GAAG;MAChB,GAAGD,MAAM;MACTlC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAED4B,OAAO,CAAChC,OAAO,CAACuC,IAAI,CAACD,SAAS,CAAC;;IAE/B;IACAN,OAAO,CAACjC,MAAM,GAAGiC,OAAO,CAAChC,OAAO,CAACwC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC3C,MAAM,EAAE,CAAC,CAAC,GAAGiC,OAAO,CAAChC,OAAO,CAAC2C,MAAM;IAE/F,OAAOX,OAAO;EAChB,CAAC;EAED;EACAY,cAAc,EAAE,MAAOC,KAAa,IAAK;IACvC,OAAOvC,cAAc,CAACC,WAAW,CAAC;MAAEM,MAAM,EAAEgC;IAAM,CAAC,CAAC;EACtD,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAOrD,QAAgB,IAAK;IACjD,OAAOa,cAAc,CAACC,WAAW,CAAC;MAAEd;IAAS,CAAC,CAAC;EACjD,CAAC;EAED;EACAsD,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B;IACA,MAAM,IAAItC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOrB,YAAY,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,MAAM,CAAC;EAC3C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}