{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSave = async () => {\n    try {\n      // TODO: Implement profile update API call\n      console.log('Saving profile:', formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Please sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: \"You need to be signed in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: \"My Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), !isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsEditing(true),\n              className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(PencilIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), \"Edit Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2 flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-12 w-12 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-400\",\n                  children: [\"Member since \", new Date().getFullYear()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), \"Full Name\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.name || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(EnvelopeIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), \"Email Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.email || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), \"Phone Number\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.phone || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), \"Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.address || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 dark:border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\",\n            children: \"Account Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Wishlist Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: (user === null || user === void 0 ? void 0 : user.wishlistCount) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Reviews Written\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"m9hqKBQQ7ULNNQhAdtYM70H6cdw=\", false, function () {\n  return [useAuth];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "UserIcon", "EnvelopeIcon", "PhoneIcon", "MapPinIcon", "PencilIcon", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "user", "isAuthenticated", "isEditing", "setIsEditing", "formData", "setFormData", "name", "email", "phone", "address", "handleInputChange", "e", "value", "target", "prev", "handleSave", "console", "log", "alert", "error", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "Date", "getFullYear", "type", "onChange", "rows", "wishlistCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProfilePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon } from '@heroicons/react/24/outline';\n\nconst ProfilePage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSave = async () => {\n    try {\n      // TODO: Implement profile update API call\n      console.log('Saving profile:', formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Please sign in</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            You need to be signed in to view your profile.\n          </p>\n          <div className=\"mt-6\">\n            <a\n              href=\"/login\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Sign in\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n          {/* Header */}\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">My Profile</h1>\n              {!isEditing && (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <PencilIcon className=\"h-4 w-4 mr-2\" />\n                  Edit Profile\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Profile Content */}\n          <div className=\"px-6 py-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Profile Picture */}\n              <div className=\"md:col-span-2 flex items-center space-x-6\">\n                <div className=\"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                  <UserIcon className=\"h-12 w-12 text-gray-500 dark:text-gray-400\" />\n                </div>\n                <div>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                    {user?.name || 'User'}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Member since {new Date().getFullYear()}</p>\n                </div>\n              </div>\n\n              {/* Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <UserIcon className=\"h-4 w-4 inline mr-2\" />\n                  Full Name\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.name || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Email */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <EnvelopeIcon className=\"h-4 w-4 inline mr-2\" />\n                  Email Address\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.email || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Phone */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <PhoneIcon className=\"h-4 w-4 inline mr-2\" />\n                  Phone Number\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.phone || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Address */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 inline mr-2\" />\n                  Address\n                </label>\n                {isEditing ? (\n                  <textarea\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.address || 'Not provided'}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {isEditing && (\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  Save Changes\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Account Statistics */}\n          <div className=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Account Statistics</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Orders</p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">0</p>\n              </div>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Wishlist Items</p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{user?.wishlistCount || 0}</p>\n              </div>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Reviews Written</p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">0</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExG,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAET,IAAI,CAACS,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACT,IAAI,CAAC,CAAC;EAEV,MAAMU,iBAAiB,GAAIC,CAA4D,IAAK;IAC1F,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAGM;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEb,QAAQ,CAAC;MACxCD,YAAY,CAAC,KAAK,CAAC;MACnB;MACAe,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CD,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIpB,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAET,IAAI,CAACS,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACF,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKwB,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxFzB,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA,CAACN,QAAQ;UAAC8B,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxD7B,OAAA;UAAIwB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7F7B,OAAA;UAAGwB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7B,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzB,OAAA;YACE8B,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,EAC3J;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzEzB,OAAA;MAAKwB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DzB,OAAA;QAAKwB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DzB,OAAA;UAAKwB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtEzB,OAAA;YAAKwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzB,OAAA;cAAIwB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClF,CAACxB,SAAS,iBACTL,OAAA;cACE+B,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,IAAI,CAAE;cAClCkB,SAAS,EAAC,8OAA8O;cAAAC,QAAA,gBAExPzB,OAAA,CAACF,UAAU;gBAAC0B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzB,OAAA;YAAKwB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDzB,OAAA;cAAKwB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDzB,OAAA;gBAAKwB,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,eACnGzB,OAAA,CAACN,QAAQ;kBAAC8B,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN7B,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,KAAI;gBAAM;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACL7B,OAAA;kBAAGwB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,eAAa,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7B,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAOwB,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFzB,OAAA,CAACN,QAAQ;kBAAC8B,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRL,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACXzB,IAAI,EAAC,MAAM;gBACXM,KAAK,EAAER,QAAQ,CAACE,IAAK;gBACrB0B,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEF7B,OAAA;gBAAGwB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACE,IAAI,IAAI;cAAc;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7B,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAOwB,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFzB,OAAA,CAACL,YAAY;kBAAC6B,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRL,OAAA;gBACEkC,IAAI,EAAC,OAAO;gBACZzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACG,KAAM;gBACtByB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEF7B,OAAA;gBAAGwB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACG,KAAK,IAAI;cAAc;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7B,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAOwB,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFzB,OAAA,CAACJ,SAAS;kBAAC4B,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRL,OAAA;gBACEkC,IAAI,EAAC,KAAK;gBACVzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACI,KAAM;gBACtBwB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEF7B,OAAA;gBAAGwB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACI,KAAK,IAAI;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7B,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAOwB,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFzB,OAAA,CAACH,UAAU;kBAAC2B,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRL,OAAA;gBACES,IAAI,EAAC,SAAS;gBACdM,KAAK,EAAER,QAAQ,CAACK,OAAQ;gBACxBuB,QAAQ,EAAEtB,iBAAkB;gBAC5BuB,IAAI,EAAE,CAAE;gBACRZ,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEF7B,OAAA;gBAAGwB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACK,OAAO,IAAI;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC7F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxB,SAAS,iBACRL,OAAA;YAAKwB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzB,OAAA;cACE+B,OAAO,EAAER,YAAa;cACtBC,SAAS,EAAC,qNAAqN;cAAAC,QAAA,EAChO;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cACE+B,OAAO,EAAEb,UAAW;cACpBM,SAAS,EAAC,yIAAyI;cAAAC,QAAA,EACpJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7B,OAAA;UAAKwB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEzB,OAAA;YAAIwB,SAAS,EAAC,2DAA2D;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjG7B,OAAA;YAAKwB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDzB,OAAA;cAAKwB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDzB,OAAA;gBAAGwB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxE7B,OAAA;gBAAGwB,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDzB,OAAA;gBAAGwB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1E7B,OAAA;gBAAGwB,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,aAAa,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDzB,OAAA;gBAAGwB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3E7B,OAAA;gBAAGwB,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAnOID,WAAqB;EAAA,QACSR,OAAO;AAAA;AAAA6C,EAAA,GADrCrC,WAAqB;AAqO3B,eAAeA,WAAW;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}