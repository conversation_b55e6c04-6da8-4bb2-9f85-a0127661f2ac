{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { MagnifyingGlassIcon, HeartIcon, UserIcon, ShoppingBagIcon, ChevronDownIcon, TagIcon, SparklesIcon } from '@heroicons/react/24/outline';\nimport { useAuth } from '../contexts/AuthContext';\nimport DarkModeToggle from './DarkModeToggle';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst categories = [{\n  name: 'Electronics',\n  icon: '🔌',\n  color: 'text-blue-600'\n}, {\n  name: 'Fashion',\n  icon: '👕',\n  color: 'text-pink-600'\n}, {\n  name: 'Home & Garden',\n  icon: '🏡',\n  color: 'text-green-600'\n}, {\n  name: 'Sports & Fitness',\n  icon: '⚽',\n  color: 'text-orange-600'\n}, {\n  name: 'Books & Media',\n  icon: '📚',\n  color: 'text-purple-600'\n}];\nconst Navbar = () => {\n  _s();\n  var _user$wishlistCount, _user$wishlistCount2, _user$name;\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const [showCategoriesDropdown, setShowCategoriesDropdown] = useState(false);\n  const navigate = useNavigate();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const userMenuRef = useRef(null);\n  const categoriesRef = useRef(null);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setShowUserMenu(false);\n      }\n      if (categoriesRef.current && !categoriesRef.current.contains(event.target)) {\n        setShowCategoriesDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white dark:bg-gray-800 shadow-lg transition-colors sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-2 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(SparklesIcon, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n              children: \"Chexkart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center\",\n          ref: categoriesRef,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCategoriesDropdown(!showCategoriesDropdown),\n              className: \"flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                className: `h-4 w-4 transition-transform ${showCategoriesDropdown ? 'rotate-180' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), showCategoriesDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl border dark:border-gray-700 py-2 z-50\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n                to: `/category/${encodeURIComponent(category.name)}`,\n                className: \"flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                onClick: () => setShowCategoriesDropdown(false),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: category.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 23\n                }, this)]\n              }, category.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"flex-1 max-w-2xl mx-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              placeholder: \"Search for products, brands, categories...\",\n              className: \"w-full pl-12 pr-16 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"absolute left-4 top-3.5 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"absolute right-2 top-2 bg-blue-600 hover:bg-blue-700 text-white p-1.5 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(DarkModeToggle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/wishlist\",\n            className: \"relative text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 p-2 transition-colors\",\n            title: \"Wishlist\",\n            children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), ((_user$wishlistCount = user === null || user === void 0 ? void 0 : user.wishlistCount) !== null && _user$wishlistCount !== void 0 ? _user$wishlistCount : 0) > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n              children: (_user$wishlistCount2 = user === null || user === void 0 ? void 0 : user.wishlistCount) !== null && _user$wishlistCount2 !== void 0 ? _user$wishlistCount2 : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/deals\",\n            className: \"hidden md:flex items-center space-x-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(TagIcon, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Deals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            ref: userMenuRef,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUserMenu(!showUserMenu),\n              className: \"flex items-center space-x-2 p-2 rounded-lg text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden lg:block font-medium\",\n                children: user === null || user === void 0 ? void 0 : user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                className: `h-4 w-4 transition-transform ${showUserMenu ? 'rotate-180' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-xl py-2 z-50 border dark:border-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 border-b dark:border-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 dark:text-gray-400\",\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/profile\",\n                className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                onClick: () => setShowUserMenu(false),\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"My Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/orders\",\n                className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                onClick: () => setShowUserMenu(false),\n                children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"My Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/wishlist\",\n                className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                onClick: () => setShowUserMenu(false),\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"My Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t dark:border-gray-700 mt-2 pt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    logout();\n                    setShowUserMenu(false);\n                  },\n                  className: \"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-4 w-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"hidden md:block text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-md\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), showMobileMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden border-t dark:border-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-4 space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSearch,\n            className: \"md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                placeholder: \"Search products...\",\n                className: \"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"absolute left-3 top-2.5 h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold text-gray-900 dark:text-gray-100 px-2\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n              to: `/category/${encodeURIComponent(category.name)}`,\n              className: \"flex items-center space-x-3 px-2 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n              onClick: () => setShowMobileMenu(false),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this)]\n            }, category.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-4 border-t dark:border-gray-700 space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"block w-full text-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\",\n              onClick: () => setShowMobileMenu(false),\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"block w-full text-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200\",\n              onClick: () => setShowMobileMenu(false),\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"Pc/zQwbiDI2m1r7Q7ub2NsPqBXY=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useNavigate", "MagnifyingGlassIcon", "HeartIcon", "UserIcon", "ShoppingBagIcon", "ChevronDownIcon", "TagIcon", "SparklesIcon", "useAuth", "DarkModeToggle", "jsxDEV", "_jsxDEV", "categories", "name", "icon", "color", "<PERSON><PERSON><PERSON>", "_s", "_user$wishlistCount", "_user$wishlistCount2", "_user$name", "searchQuery", "setSearch<PERSON>uery", "showUserMenu", "setShowUserMenu", "showMobileMenu", "setShowMobileMenu", "showCategoriesDropdown", "setShowCategoriesDropdown", "navigate", "user", "isAuthenticated", "logout", "userMenuRef", "categoriesRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleSearch", "e", "preventDefault", "trim", "encodeURIComponent", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "map", "category", "onSubmit", "type", "value", "onChange", "placeholder", "title", "wishlistCount", "char<PERSON>t", "toUpperCase", "email", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport {\n  MagnifyingGlassIcon,\n  HeartIcon,\n  UserIcon,\n  ShoppingBagIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChevronDownIcon,\n  TagIcon,\n  SparklesIcon\n} from '@heroicons/react/24/outline';\nimport { useAuth } from '../contexts/AuthContext';\nimport DarkModeToggle from './DarkModeToggle';\n\nconst categories = [\n  { name: 'Electronics', icon: '🔌', color: 'text-blue-600' },\n  { name: 'Fashion', icon: '👕', color: 'text-pink-600' },\n  { name: 'Home & Garden', icon: '🏡', color: 'text-green-600' },\n  { name: 'Sports & Fitness', icon: '⚽', color: 'text-orange-600' },\n  { name: 'Books & Media', icon: '📚', color: 'text-purple-600' }\n];\n\nconst Navbar = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const [showCategoriesDropdown, setShowCategoriesDropdown] = useState(false);\n  const navigate = useNavigate();\n  const { user, isAuthenticated, logout } = useAuth();\n  const userMenuRef = useRef<HTMLDivElement>(null);\n  const categoriesRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {\n        setShowUserMenu(false);\n      }\n      if (categoriesRef.current && !categoriesRef.current.contains(event.target as Node)) {\n        setShowCategoriesDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-lg transition-colors sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-2 rounded-lg\">\n                <SparklesIcon className=\"h-6 w-6\" />\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                Chexkart\n              </span>\n            </Link>\n          </div>\n\n          {/* Categories Dropdown - Desktop */}\n          <div className=\"hidden lg:flex items-center\" ref={categoriesRef}>\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowCategoriesDropdown(!showCategoriesDropdown)}\n                className=\"flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                <span>Categories</span>\n                <ChevronDownIcon className={`h-4 w-4 transition-transform ${showCategoriesDropdown ? 'rotate-180' : ''}`} />\n              </button>\n\n              {showCategoriesDropdown && (\n                <div className=\"absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl border dark:border-gray-700 py-2 z-50\">\n                  {categories.map((category) => (\n                    <Link\n                      key={category.name}\n                      to={`/category/${encodeURIComponent(category.name)}`}\n                      className=\"flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      onClick={() => setShowCategoriesDropdown(false)}\n                    >\n                      <span className=\"text-lg\">{category.icon}</span>\n                      <span className=\"font-medium\">{category.name}</span>\n                    </Link>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"flex-1 max-w-2xl mx-6\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                placeholder=\"Search for products, brands, categories...\"\n                className=\"w-full pl-12 pr-16 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 text-sm\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-4 top-3.5 h-5 w-5 text-gray-400\" />\n              <button\n                type=\"submit\"\n                className=\"absolute right-2 top-2 bg-blue-600 hover:bg-blue-700 text-white p-1.5 rounded-lg transition-colors\"\n              >\n                <MagnifyingGlassIcon className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </form>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Dark Mode Toggle */}\n            <DarkModeToggle />\n\n            {/* Wishlist */}\n            {isAuthenticated && (\n              <Link\n                to=\"/wishlist\"\n                className=\"relative text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 p-2 transition-colors\"\n                title=\"Wishlist\"\n              >\n                <HeartIcon className=\"h-6 w-6\" />\n                {(user?.wishlistCount ?? 0) > 0 && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                    {user?.wishlistCount ?? 0}\n                  </span>\n                )}\n              </Link>\n            )}\n\n            {/* Today's Deals */}\n            <Link\n              to=\"/deals\"\n              className=\"hidden md:flex items-center space-x-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md\"\n            >\n              <TagIcon className=\"h-4 w-4\" />\n              <span>Deals</span>\n            </Link>\n\n            {/* User Menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\" ref={userMenuRef}>\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 p-2 rounded-lg text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n                    {user?.name?.charAt(0).toUpperCase() || 'U'}\n                  </div>\n                  <span className=\"hidden lg:block font-medium\">{user?.name}</span>\n                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${showUserMenu ? 'rotate-180' : ''}`} />\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-xl py-2 z-50 border dark:border-gray-700\">\n                    <div className=\"px-4 py-3 border-b dark:border-gray-700\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{user?.name}</p>\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">{user?.email}</p>\n                    </div>\n\n                    <Link\n                      to=\"/profile\"\n                      className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <UserIcon className=\"h-4 w-4\" />\n                      <span>My Profile</span>\n                    </Link>\n\n                    <Link\n                      to=\"/orders\"\n                      className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <ShoppingBagIcon className=\"h-4 w-4\" />\n                      <span>My Orders</span>\n                    </Link>\n\n                    <Link\n                      to=\"/wishlist\"\n                      className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <HeartIcon className=\"h-4 w-4\" />\n                      <span>My Wishlist</span>\n                    </Link>\n\n                    <div className=\"border-t dark:border-gray-700 mt-2 pt-2\">\n                      <button\n                        onClick={() => {\n                          logout();\n                          setShowUserMenu(false);\n                        }}\n                        className=\"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\n                      >\n                        <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        <span>Sign Out</span>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link\n                  to=\"/login\"\n                  className=\"hidden md:block text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors\"\n                >\n                  Login\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-md\"\n                >\n                  Sign Up\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {showMobileMenu && (\n          <div className=\"lg:hidden border-t dark:border-gray-700\">\n            <div className=\"px-4 py-4 space-y-3\">\n              {/* Mobile Search */}\n              <form onSubmit={handleSearch} className=\"md:hidden\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    placeholder=\"Search products...\"\n                    className=\"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors\"\n                  />\n                  <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </form>\n\n              {/* Mobile Categories */}\n              <div className=\"space-y-2\">\n                <h3 className=\"text-sm font-semibold text-gray-900 dark:text-gray-100 px-2\">Categories</h3>\n                {categories.map((category) => (\n                  <Link\n                    key={category.name}\n                    to={`/category/${encodeURIComponent(category.name)}`}\n                    className=\"flex items-center space-x-3 px-2 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                    onClick={() => setShowMobileMenu(false)}\n                  >\n                    <span className=\"text-lg\">{category.icon}</span>\n                    <span>{category.name}</span>\n                  </Link>\n                ))}\n              </div>\n\n              {/* Mobile Auth Links */}\n              {!isAuthenticated && (\n                <div className=\"pt-4 border-t dark:border-gray-700 space-y-2\">\n                  <Link\n                    to=\"/login\"\n                    className=\"block w-full text-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                    onClick={() => setShowMobileMenu(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block w-full text-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200\"\n                    onClick={() => setShowMobileMenu(false)}\n                  >\n                    Sign Up\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,mBAAmB,EACnBC,SAAS,EACTC,QAAQ,EACRC,eAAe,EAGfC,eAAe,EACfC,OAAO,EACPC,YAAY,QACP,6BAA6B;AACpC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAgB,CAAC,EAC3D;EAAEF,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACvD;EAAEF,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAiB,CAAC,EAC9D;EAAEF,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAkB,CAAC,EACjE;EAAEF,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAkB,CAAC,CAChE;AAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,UAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAMiC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGxB,OAAO,CAAC,CAAC;EACnD,MAAMyB,WAAW,GAAGpC,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMqC,aAAa,GAAGrC,MAAM,CAAiB,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqC,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC9Ef,eAAe,CAAC,KAAK,CAAC;MACxB;MACA,IAAIU,aAAa,CAACG,OAAO,IAAI,CAACH,aAAa,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAClFX,yBAAyB,CAAC,KAAK,CAAC;MAClC;IACF,CAAC;IAEDY,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIxB,WAAW,CAACyB,IAAI,CAAC,CAAC,EAAE;MACtBjB,QAAQ,CAAC,aAAakB,kBAAkB,CAAC1B,WAAW,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACjE;EACF,CAAC;EAED,oBACEnC,OAAA;IAAKqC,SAAS,EAAC,yEAAyE;IAAAC,QAAA,eACtFtC,OAAA;MAAKqC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDtC,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCtC,OAAA,CAACZ,IAAI;YAACmD,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAClDtC,OAAA;cAAKqC,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrFtC,OAAA,CAACJ,YAAY;gBAACyC,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN3C,OAAA;cAAMqC,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAAC;YAEhH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN3C,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAACO,GAAG,EAAErB,aAAc;UAAAe,QAAA,eAC9DtC,OAAA;YAAKqC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtC,OAAA;cACE6C,OAAO,EAAEA,CAAA,KAAM5B,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;cAClEqB,SAAS,EAAC,sKAAsK;cAAAC,QAAA,gBAEhLtC,OAAA;gBAAAsC,QAAA,EAAM;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvB3C,OAAA,CAACN,eAAe;gBAAC2C,SAAS,EAAE,gCAAgCrB,sBAAsB,GAAG,YAAY,GAAG,EAAE;cAAG;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,EAER3B,sBAAsB,iBACrBhB,OAAA;cAAKqC,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EACrIrC,UAAU,CAAC6C,GAAG,CAAEC,QAAQ,iBACvB/C,OAAA,CAACZ,IAAI;gBAEHmD,EAAE,EAAE,aAAaH,kBAAkB,CAACW,QAAQ,CAAC7C,IAAI,CAAC,EAAG;gBACrDmC,SAAS,EAAC,kIAAkI;gBAC5IQ,OAAO,EAAEA,CAAA,KAAM5B,yBAAyB,CAAC,KAAK,CAAE;gBAAAqB,QAAA,gBAEhDtC,OAAA;kBAAMqC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAES,QAAQ,CAAC5C;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChD3C,OAAA;kBAAMqC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAES,QAAQ,CAAC7C;gBAAI;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAN/CI,QAAQ,CAAC7C,IAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOd,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3C,OAAA;UAAMgD,QAAQ,EAAEhB,YAAa;UAACK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAC7DtC,OAAA;YAAKqC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtC,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExC,WAAY;cACnByC,QAAQ,EAAGlB,CAAC,IAAKtB,cAAc,CAACsB,CAAC,CAACL,MAAM,CAACsB,KAAK,CAAE;cAChDE,WAAW,EAAC,4CAA4C;cACxDf,SAAS,EAAC;YAAoS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/S,CAAC,eACF3C,OAAA,CAACV,mBAAmB;cAAC+C,SAAS,EAAC;YAA+C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjF3C,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbZ,SAAS,EAAC,oGAAoG;cAAAC,QAAA,eAE9GtC,OAAA,CAACV,mBAAmB;gBAAC+C,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGP3C,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1CtC,OAAA,CAACF,cAAc;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGjBvB,eAAe,iBACdpB,OAAA,CAACZ,IAAI;YACHmD,EAAE,EAAC,WAAW;YACdF,SAAS,EAAC,8GAA8G;YACxHgB,KAAK,EAAC,UAAU;YAAAf,QAAA,gBAEhBtC,OAAA,CAACT,SAAS;cAAC8C,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChC,EAAApC,mBAAA,GAACY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,aAAa,cAAA/C,mBAAA,cAAAA,mBAAA,GAAI,CAAC,IAAI,CAAC,iBAC7BP,OAAA;cAAMqC,SAAS,EAAC,8GAA8G;cAAAC,QAAA,GAAA9B,oBAAA,GAC3HW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,aAAa,cAAA9C,oBAAA,cAAAA,oBAAA,GAAI;YAAC;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACP,eAGD3C,OAAA,CAACZ,IAAI;YACHmD,EAAE,EAAC,QAAQ;YACXF,SAAS,EAAC,gNAAgN;YAAAC,QAAA,gBAE1NtC,OAAA,CAACL,OAAO;cAAC0C,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B3C,OAAA;cAAAsC,QAAA,EAAM;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,EAGNvB,eAAe,gBACdpB,OAAA;YAAKqC,SAAS,EAAC,UAAU;YAACO,GAAG,EAAEtB,WAAY;YAAAgB,QAAA,gBACzCtC,OAAA;cACE6C,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CyB,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LtC,OAAA;gBAAKqC,SAAS,EAAC,mIAAmI;gBAAAC,QAAA,EAC/I,CAAAnB,IAAI,aAAJA,IAAI,wBAAAV,UAAA,GAAJU,IAAI,CAAEjB,IAAI,cAAAO,UAAA,uBAAVA,UAAA,CAAY8C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACN3C,OAAA;gBAAMqC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjB;cAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE3C,OAAA,CAACN,eAAe;gBAAC2C,SAAS,EAAE,gCAAgCzB,YAAY,GAAG,YAAY,GAAG,EAAE;cAAG;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,EAER/B,YAAY,iBACXZ,OAAA;cAAKqC,SAAS,EAAC,iHAAiH;cAAAC,QAAA,gBAC9HtC,OAAA;gBAAKqC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACtDtC,OAAA;kBAAGqC,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjB;gBAAI;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpF3C,OAAA;kBAAGqC,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eAEN3C,OAAA,CAACZ,IAAI;gBACHmD,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAC,0IAA0I;gBACpJQ,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,KAAK,CAAE;gBAAAyB,QAAA,gBAEtCtC,OAAA,CAACR,QAAQ;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC3C,OAAA;kBAAAsC,QAAA,EAAM;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eAEP3C,OAAA,CAACZ,IAAI;gBACHmD,EAAE,EAAC,SAAS;gBACZF,SAAS,EAAC,0IAA0I;gBACpJQ,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,KAAK,CAAE;gBAAAyB,QAAA,gBAEtCtC,OAAA,CAACP,eAAe;kBAAC4C,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC3C,OAAA;kBAAAsC,QAAA,EAAM;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAEP3C,OAAA,CAACZ,IAAI;gBACHmD,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAC,0IAA0I;gBACpJQ,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,KAAK,CAAE;gBAAAyB,QAAA,gBAEtCtC,OAAA,CAACT,SAAS;kBAAC8C,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC3C,OAAA;kBAAAsC,QAAA,EAAM;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAEP3C,OAAA;gBAAKqC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,eACtDtC,OAAA;kBACE6C,OAAO,EAAEA,CAAA,KAAM;oBACbxB,MAAM,CAAC,CAAC;oBACRR,eAAe,CAAC,KAAK,CAAC;kBACxB,CAAE;kBACFwB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,gBAE1JtC,OAAA;oBAAKqC,SAAS,EAAC,SAAS;oBAACqB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAtB,QAAA,eAC5EtC,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA2F;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK,CAAC,eACN3C,OAAA;oBAAAsC,QAAA,EAAM;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN3C,OAAA;YAAKqC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CtC,OAAA,CAACZ,IAAI;cACHmD,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,0JAA0J;cAAAC,QAAA,EACrK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3C,OAAA,CAACZ,IAAI;cACHmD,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,gLAAgL;cAAAC,QAAA,EAC3L;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL7B,cAAc,iBACbd,OAAA;QAAKqC,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACtDtC,OAAA;UAAKqC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAElCtC,OAAA;YAAMgD,QAAQ,EAAEhB,YAAa;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,eACjDtC,OAAA;cAAKqC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBtC,OAAA;gBACEiD,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAExC,WAAY;gBACnByC,QAAQ,EAAGlB,CAAC,IAAKtB,cAAc,CAACsB,CAAC,CAACL,MAAM,CAACsB,KAAK,CAAE;gBAChDE,WAAW,EAAC,oBAAoB;gBAChCf,SAAS,EAAC;cAAwR;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnS,CAAC,eACF3C,OAAA,CAACV,mBAAmB;gBAAC+C,SAAS,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP3C,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtC,OAAA;cAAIqC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC1F1C,UAAU,CAAC6C,GAAG,CAAEC,QAAQ,iBACvB/C,OAAA,CAACZ,IAAI;cAEHmD,EAAE,EAAE,aAAaH,kBAAkB,CAACW,QAAQ,CAAC7C,IAAI,CAAC,EAAG;cACrDmC,SAAS,EAAC,8IAA8I;cACxJQ,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;cAAAuB,QAAA,gBAExCtC,OAAA;gBAAMqC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAES,QAAQ,CAAC5C;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChD3C,OAAA;gBAAAsC,QAAA,EAAOS,QAAQ,CAAC7C;cAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GANvBI,QAAQ,CAAC7C,IAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGL,CAACvB,eAAe,iBACfpB,OAAA;YAAKqC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAC3DtC,OAAA,CAACZ,IAAI;cACHmD,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,2IAA2I;cACrJQ,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;cAAAuB,QAAA,EACzC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3C,OAAA,CAACZ,IAAI;cACHmD,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,2KAA2K;cACrLQ,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;cAAAuB,QAAA,EACzC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CA9QID,MAAM;EAAA,QAKOhB,WAAW,EACcQ,OAAO;AAAA;AAAAoE,EAAA,GAN7C5D,MAAM;AAgRZ,eAAeA,MAAM;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}