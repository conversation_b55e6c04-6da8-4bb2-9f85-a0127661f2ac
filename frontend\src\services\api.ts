import { Product, ProductFilters, Review } from '../types';

// Mock data for development
const mockProducts: Product[] = [
  {
    _id: '1',
    name: 'Sony WH-1000XM4 Wireless Headphones',
    description: 'Industry-leading noise canceling with Dual Noise Sensor technology',
    category: 'Electronics',
    images: [
      'https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg',
      'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'
    ],
    prices: [
      {
        storeName: 'Amazon',
        price: 29058,
        productUrl: 'https://amazon.in'
      },
      {
        storeName: 'Flipkart',
        price: 29224,
        productUrl: 'https://flipkart.com'
      }
    ],
    rating: 4.8,
    reviews: [
      {
        userName: 'John Doe',
        rating: 5,
        comment: 'Best headphones I have ever owned!',
        createdAt: new Date('2025-04-14')
      }
    ],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '2',
    name: 'Samsung 55" 4K Smart TV',
    description: 'Crystal clear display with smart features',
    category: 'Electronics',
    images: [
      'https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'
    ],
    prices: [
      {
        storeName: 'Amazon',
        price: 41582,
        productUrl: 'https://amazon.in'
      },
      {
        storeName: 'Flipkart',
        price: 41749,
        productUrl: 'https://flipkart.com'
      }
    ],
    rating: 4.5,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '5',
    name: 'Nike Air Max 270',
    description: 'Comfortable athletic shoes with Air cushioning',
    category: 'Fashion',
    images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],
    prices: [
      { storeName: 'Nike', price: 12525, productUrl: 'https://www.nike.com/in/t/air-max-270-mens-shoes-KkLcGR/AH8050-002' },
      { storeName: 'Amazon', price: 12524, productUrl: 'https://amazon.in' }
    ],
    rating: 4.6,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '6',
    name: 'Levi\'s 501 Original Fit Jeans',
    description: 'Classic straight leg jeans',
    category: 'Fashion',
    images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Amazon', price: 5009, productUrl: 'https://amazon.in' },
      { storeName: 'Myntra', price: 5803, productUrl: 'https://myntra.com' }
    ],
    rating: 4.5,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  },
  {
    _id: '7',
    name: 'Ray-Ban Aviator Sunglasses',
    description: 'Classic aviator style with UV protection',
    category: 'Fashion',
    images: ['https://images.unsplash.com/photo-1572635196237-14b3f281503f?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Amazon', price: 154.00, productUrl: 'https://amazon.com' },
      { storeName: 'Sunglass Hut', price: 169.00, productUrl: 'https://www.sunglasshut.com/us/ray-ban-aviator-classic-gold/8052896020578' }
    ],
    rating: 4.7,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '8',
    name: 'Casio G-Shock Watch',
    description: 'Durable digital watch with multiple features',
    category: 'Fashion',
    images: ['https://images.unsplash.com/photo-1533139502658-0198f920d8e8?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Amazon', price: 99.00, productUrl: 'https://amazon.com' },
      { storeName: 'Casio', price: 110.00, productUrl: 'https://www.casio.com/us/watches/gshock/product.GA2100-1A/' }
    ],
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  },
  {
    _id: '17',
    name: 'Adidas Ultraboost 21',
    description: 'Premium running shoes with responsive cushioning',
    category: 'Fashion',
    images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Adidas', price: 180.00, productUrl: 'https://www.adidas.com/us/ultraboost-21-shoes/FY0377.html' },
      { storeName: 'Amazon', price: 169.95, productUrl: 'https://amazon.com' }
    ],
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '18',
    name: 'Michael Kors Leather Crossbody Bag',
    description: 'Elegant leather bag with adjustable strap',
    category: 'Fashion',
    images: ['https://images.unsplash.com/photo-1548036328-c9fa89d128fa?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Michael Kors', price: 298.00, productUrl: 'https://www.michaelkors.com/leather-crossbody-bag/_/R-30F2G7CM2L' },
      { storeName: 'Amazon', price: 278.95, productUrl: 'https://amazon.com' }
    ],
    rating: 4.7,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  // Home & Garden Category
  {
    _id: '19',
    name: 'Philips Hue Smart LED Bulb Set',
    description: 'Color-changing smart LED bulbs with wireless control',
    category: 'Home & Garden',
    images: ['https://images.unsplash.com/photo-1563461660947-507ef49e9c47?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Philips', price: 199.99, productUrl: 'https://www.philips-hue.com/en-us/p/hue-white-and-color-ambiance-starter-kit-e26/046677555334' },
      { storeName: 'Amazon', price: 189.95, productUrl: 'https://amazon.com' },
      { storeName: 'Best Buy', price: 194.99, productUrl: 'https://bestbuy.com' }
    ],
    rating: 4.7,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '20',
    name: 'KitchenAid Stand Mixer',
    description: 'Professional 5-quart stand mixer with multiple attachments',
    category: 'Home & Garden',
    images: ['https://images.unsplash.com/photo-1544441893-675973e31985?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'KitchenAid', price: 399.99, productUrl: 'https://www.kitchenaid.com/countertop-appliances/stand-mixers/tilt-head-stand-mixers/p.artisan-series-5-quart-tilt-head-stand-mixer.ksm150psob.html' },
      { storeName: 'Amazon', price: 379.95, productUrl: 'https://amazon.com' },
      { storeName: 'Williams Sonoma', price: 429.99, productUrl: 'https://www.williams-sonoma.com/products/kitchenaid-artisan-stand-mixer/' }
    ],
    rating: 4.9,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  },
  {
    _id: '21',
    name: 'DEWALT Power Tool Set',
    description: '20V MAX Cordless Drill Combo Kit with 2 batteries',
    category: 'Home & Garden',
    images: ['https://images.unsplash.com/photo-1581244277943-fe4a9c777189?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Home Depot', price: 299.00, productUrl: 'https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-Charger-and-Bag-DCK240C2/204373168' },
      { storeName: 'Amazon', price: 289.99, productUrl: 'https://amazon.com' },
      { storeName: 'Lowes', price: 294.99, productUrl: 'https://www.lowes.com/pd/DEWALT-20-Volt-Max-2-Tool-Combo-Kit/1000191169' }
    ],
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '22',
    name: 'Dyson V15 Detect Vacuum',
    description: 'Cordless vacuum with laser dust detection',
    category: 'Home & Garden',
    images: ['https://images.unsplash.com/photo-1558317374-067fb5f30001?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Dyson', price: 699.99, productUrl: 'https://www.dyson.com/vacuum-cleaners/stick-vacuum-cleaners/dyson-v15-detect-nickel-yellow' },
      { storeName: 'Amazon', price: 679.95, productUrl: 'https://amazon.com' },
      { storeName: 'Best Buy', price: 689.99, productUrl: 'https://bestbuy.com' }
    ],
    rating: 4.7,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  },
  // Sports Category
  {
    _id: '23',
    name: 'NordicTrack Commercial 1750 Treadmill',
    description: 'Smart treadmill with 14-inch HD touchscreen and iFit integration',
    category: 'Sports',
    images: ['https://images.unsplash.com/photo-1595078475328-1ab05d0a6a0e?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'NordicTrack', price: 1899.00, productUrl: 'https://www.nordictrack.com/treadmills/commercial-1750-treadmill' },
      { storeName: 'Amazon', price: 1799.00, productUrl: 'https://amazon.com' },
      { storeName: 'Dick\'s Sporting Goods', price: 1849.99, productUrl: 'https://dickssportinggoods.com' }
    ],
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '24',
    name: 'Wilson Evolution Basketball',
    description: 'Official size indoor game basketball with moisture-wicking technology',
    category: 'Sports',
    images: ['https://images.unsplash.com/photo-1519861531473-9200262188bf?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Wilson', price: 59.99, productUrl: 'https://www.wilson.com/en-us/product/evolution-basketball-WTB0516' },
      { storeName: 'Amazon', price: 54.95, productUrl: 'https://amazon.com' },
      { storeName: 'Dick\'s Sporting Goods', price: 57.99, productUrl: 'https://dickssportinggoods.com' }
    ],
    rating: 4.9,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  },
  {
    _id: '25',
    name: 'Garmin Forerunner 945 GPS Watch',
    description: 'Advanced running watch with full-color mapping and training metrics',
    category: 'Sports',
    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?auto=format&fit=crop&w=800'],
    prices: [
      { storeName: 'Garmin', price: 599.99, productUrl: 'https://garmin.com' },
      { storeName: 'Amazon', price: 579.95, productUrl: 'https://amazon.com' },
      { storeName: 'REI', price: 589.99, productUrl: 'https://rei.com' }
    ],
    rating: 4.7,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: true
  },
  {
    _id: '26',
    name: 'Yeti Tundra 45 Cooler',
    description: 'Premium hard cooler for outdoor activities and camping',
    category: 'Sports',
    images: ['https://picsum.photos/id/26/800/800'],
    prices: [
      { storeName: 'Yeti', price: 325.00, productUrl: 'https://www.yeti.com/en_US/coolers/hard-coolers/tundra-45-cooler/YT45.html' },
      { storeName: 'Amazon', price: 299.99, productUrl: 'https://amazon.com' },
      { storeName: 'REI', price: 319.99, productUrl: 'https://rei.com' }
    ],
    rating: 4.8,
    reviews: [],
    createdAt: new Date('2025-04-01'),
    onSale: false
  }
];

export const productService = {
  // Get all products with optional filters
  getProducts: async (filters?: ProductFilters) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredProducts = [...mockProducts];

    if (filters) {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredProducts = filteredProducts.filter(p => 
          p.name.toLowerCase().includes(searchLower) ||
          p.description.toLowerCase().includes(searchLower)
        );
      }

      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => 
          p.category.toLowerCase() === filters.category?.toLowerCase()
        );
      }

      if (filters.minPrice) {
        filteredProducts = filteredProducts.filter(p => 
          Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0)
        );
      }

      if (filters.maxPrice) {
        filteredProducts = filteredProducts.filter(p => 
          Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity)
        );
      }

      if (filters.sort) {
        switch (filters.sort) {
          case 'price-asc':
            filteredProducts.sort((a, b) => 
              Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price))
            );
            break;
          case 'price-desc':
            filteredProducts.sort((a, b) => 
              Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price))
            );
            break;
          case 'rating':
            filteredProducts.sort((a, b) => b.rating - a.rating);
            break;
        }
      }
    }

    return filteredProducts;
  },

  // Get single product by ID
  getProduct: async (id: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const product = mockProducts.find(p => p._id === id);
    if (!product) throw new Error('Product not found');
    return product;
  },

  // Add a review to a product
  addReview: async (productId: string, review: Omit<Review, 'createdAt'>) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const product = mockProducts.find(p => p._id === productId);
    if (!product) throw new Error('Product not found');

    const newReview = {
      ...review,
      createdAt: new Date()
    };

    product.reviews.push(newReview);
    
    // Update product rating
    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;

    return product;
  },

  // Search products
  searchProducts: async (query: string) => {
    return productService.getProducts({ search: query });
  },

  // Get products by category
  getProductsByCategory: async (category: string) => {
    return productService.getProducts({ category });
  },

  // Get today's deals
  getTodaysDeals: async () => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockProducts.filter(p => p.onSale);
  }
};
