{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport './App.css';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport SearchResultsPage from './pages/SearchResultsPage';\nimport CategoryPage from './pages/CategoryPage';\nimport TodaysDealsPage from './pages/TodaysDealsPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport WishlistPage from './pages/WishlistPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mx-auto px-4 py-8\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/product/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProductDetailPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/search\",\n              element: /*#__PURE__*/_jsxDEV(SearchResultsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/category/:category\",\n              element: /*#__PURE__*/_jsxDEV(CategoryPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/deals\",\n              element: /*#__PURE__*/_jsxDEV(TodaysDealsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/wishlist\",\n              element: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "HomePage", "ProductDetailPage", "SearchResultsPage", "CategoryPage", "TodaysDealsPage", "LoginPage", "RegisterPage", "WishlistPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport './App.css';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport SearchResultsPage from './pages/SearchResultsPage';\nimport CategoryPage from './pages/CategoryPage';\nimport TodaysDealsPage from './pages/TodaysDealsPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport WishlistPage from './pages/WishlistPage';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"min-h-screen bg-gray-100\">\n          <Navbar />\n          <div className=\"container mx-auto px-4 py-8\">\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/product/:id\" element={<ProductDetailPage />} />\n              <Route path=\"/search\" element={<SearchResultsPage />} />\n              <Route path=\"/category/:category\" element={<CategoryPage />} />\n              <Route path=\"/deals\" element={<TodaysDealsPage />} />\n              <Route path=\"/login\" element={<LoginPage />} />\n              <Route path=\"/register\" element={<RegisterPage />} />\n              <Route path=\"/wishlist\" element={<WishlistPage />} />\n            </Routes>\n          </div>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAClB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AAErD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,YAAY;IAAAa,QAAA,eACXF,OAAA,CAACd,MAAM;MAAAgB,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,0BAA0B;QAAAD,QAAA,gBACvCF,OAAA,CAACV,MAAM;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAD,QAAA,eAC1CF,OAAA,CAACb,MAAM;YAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACT,QAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,cAAc;cAACC,OAAO,eAAET,OAAA,CAACR,iBAAiB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,SAAS;cAACC,OAAO,eAAET,OAAA,CAACP,iBAAiB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,qBAAqB;cAACC,OAAO,eAAET,OAAA,CAACN,YAAY;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACL,eAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACJ,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACH,YAAY;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDP,OAAA,CAACZ,KAAK;cAACoB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACF,YAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACG,EAAA,GAtBQT,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}