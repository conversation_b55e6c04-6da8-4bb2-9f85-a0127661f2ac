{"ast": null, "code": "import * as React from \"react\";\nfunction PauseIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(PauseIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "PauseIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "fillRule", "d", "clipRule", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/node_modules/@heroicons/react/24/solid/esm/PauseIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PauseIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PauseIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAASA,CAAC;EACjBC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEP,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEV;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDQ,QAAQ,EAAE,SAAS;IACnBC,CAAC,EAAE,wNAAwN;IAC3NC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcjB,KAAK,CAACkB,UAAU,CAACjB,SAAS,CAAC;AAC5D,eAAegB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}