{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * AuthProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (token) {\n          const userData = await authService.getCurrentUser();\n          setUser(userData);\n        }\n      } catch (error) {\n        console.error('Error initializing auth:', error);\n        localStorage.removeItem('token');\n      } finally {\n        setLoading(false);\n      }\n    };\n    initializeAuth();\n  }, []);\n\n  /**\n   * Login function\n   * @param {string} email\n   * @param {string} password\n   * @returns {Promise<Object>}\n   */\n  const login = async (email, password) => {\n    try {\n      const response = await authService.login(email, password);\n      setUser(response.user);\n      localStorage.setItem('token', response.token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  /**\n   * Register function\n   * @param {string} name\n   * @param {string} email\n   * @param {string} password\n   * @returns {Promise<Object>}\n   */\n  const register = async (name, email, password) => {\n    try {\n      const response = await authService.register(name, email, password);\n      setUser(response.user);\n      localStorage.setItem('token', response.token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  /**\n   * Logout function\n   */\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n  };\n\n  /**\n   * Update user function\n   * @param {Object} userData\n   */\n  const updateUser = userData => {\n    setUser(userData);\n  };\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 10\n  }, this);\n};\n\n/**\n * Hook to use auth context\n * @returns {Object} Auth context value\n */\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "initializeAuth", "token", "localStorage", "getItem", "userData", "getCurrentUser", "error", "console", "removeItem", "login", "email", "password", "response", "setItem", "register", "name", "logout", "updateUser", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/authService';\n\nconst AuthContext = createContext(undefined);\n\n/**\n * AuthProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (token) {\n          const userData = await authService.getCurrentUser();\n          setUser(userData);\n        }\n      } catch (error) {\n        console.error('Error initializing auth:', error);\n        localStorage.removeItem('token');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  /**\n   * Login function\n   * @param {string} email\n   * @param {string} password\n   * @returns {Promise<Object>}\n   */\n  const login = async (email, password) => {\n    try {\n      const response = await authService.login(email, password);\n      setUser(response.user);\n      localStorage.setItem('token', response.token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  /**\n   * Register function\n   * @param {string} name\n   * @param {string} email\n   * @param {string} password\n   * @returns {Promise<Object>}\n   */\n  const register = async (name, email, password) => {\n    try {\n      const response = await authService.register(name, email, password);\n      setUser(response.user);\n      localStorage.setItem('token', response.token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  /**\n   * Logout function\n   */\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n  };\n\n  /**\n   * Update user function\n   * @param {Object} userData\n   */\n  const updateUser = (userData) => {\n    setUser(userData);\n  };\n\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n/**\n * Hook to use auth context\n * @returns {Object} Auth context value\n */\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAW,gBAAGP,aAAa,CAACQ,SAAS,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACT,MAAMG,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,cAAc,CAAC,CAAC;UACnDR,OAAO,CAACO,QAAQ,CAAC;QACnB;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDJ,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;MAClC,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;AACA;AACA;AACA;EACE,MAAMS,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,WAAW,CAACqB,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MACzDd,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC;MACtBM,YAAY,CAACW,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACX,KAAK,CAAC;MAC7C,OAAOW,QAAQ;IACjB,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMQ,QAAQ,GAAG,MAAAA,CAAOC,IAAI,EAAEL,KAAK,EAAEC,QAAQ,KAAK;IAChD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,WAAW,CAAC0B,QAAQ,CAACC,IAAI,EAAEL,KAAK,EAAEC,QAAQ,CAAC;MAClEd,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC;MACtBM,YAAY,CAACW,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACX,KAAK,CAAC;MAC7C,OAAOW,QAAQ;IACjB,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMU,MAAM,GAAGA,CAAA,KAAM;IACnBnB,OAAO,CAAC,IAAI,CAAC;IACbK,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;EAClC,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMS,UAAU,GAAIb,QAAQ,IAAK;IAC/BP,OAAO,CAACO,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMc,KAAK,GAAG;IACZtB,IAAI;IACJuB,eAAe,EAAE,CAAC,CAACvB,IAAI;IACvBE,OAAO;IACPW,KAAK;IACLK,QAAQ;IACRE,MAAM;IACNC;EACF,CAAC;EAED,oBAAO3B,OAAA,CAACC,WAAW,CAAC6B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxB,QAAA,EAAEA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;;AAED;AACA;AACA;AACA;AAHA7B,EAAA,CAvFaF,YAAY;AAAAgC,EAAA,GAAZhC,YAAY;AA2FzB,OAAO,MAAMiC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG3C,UAAU,CAACM,WAAW,CAAC;EACvC,IAAIqC,OAAO,KAAKpC,SAAS,EAAE;IACzB,MAAM,IAAIqC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAenC,WAAW;AAAC,IAAAkC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}