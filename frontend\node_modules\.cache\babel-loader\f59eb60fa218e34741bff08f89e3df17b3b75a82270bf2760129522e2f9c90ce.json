{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\CategoryPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryPage = () => {\n  _s();\n  const {\n    category\n  } = useParams();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = await productService.getProductsByCategory(category);\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load category products');\n        console.error('Error fetching category products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (category) {\n      fetchProducts();\n    }\n  }, [category]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n          children: category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mt-2\",\n          children: [products.length, \" product\", products.length !== 1 ? 's' : '', \" found\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg\",\n          children: [\"No products found in \", category]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n          product: product\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryPage, \"zSrayaqN3jcRD+HYwMR0vErF5po=\", false, function () {\n  return [useParams];\n});\n_c = CategoryPage;\nexport default CategoryPage;\nvar _c;\n$RefreshReg$(_c, \"CategoryPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "ProductCard", "productService", "jsxDEV", "_jsxDEV", "CategoryPage", "_s", "category", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "results", "getProductsByCategory", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "window", "location", "reload", "map", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/CategoryPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\n\nconst CategoryPage = () => {\n  const { category } = useParams();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = await productService.getProductsByCategory(category);\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load category products');\n        console.error('Error fetching category products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (category) {\n      fetchProducts();\n    }\n  }, [category]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n            {category}\n          </h1>\n          {!loading && (\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {products.length} product{products.length !== 1 ? 's' : ''} found\n            </p>\n          )}\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-red-600 dark:text-red-400 mb-4\">{error}</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        ) : products.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-600 dark:text-gray-400 text-lg\">\n              No products found in {category}\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {products.map((product) => (\n              <ProductCard key={product._id} product={product} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGP,SAAS,CAAC,CAAC;EAChC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,MAAMe,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,OAAO,GAAG,MAAMb,cAAc,CAACc,qBAAqB,CAACT,QAAQ,CAAC;QACpEE,WAAW,CAACM,OAAO,CAACP,QAAQ,IAAIO,OAAO,CAAC;QACxCF,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZJ,QAAQ,CAAC,kCAAkC,CAAC;QAC5CK,OAAO,CAACN,KAAK,CAAC,mCAAmC,EAAEK,GAAG,CAAC;MACzD,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,QAAQ,EAAE;MACZO,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,oBACEH,OAAA;IAAKe,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5DhB,OAAA;MAAKe,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDhB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UAAIe,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAChEb;QAAQ;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EACJ,CAACd,OAAO,iBACPN,OAAA;UAAGe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GACjDZ,QAAQ,CAACiB,MAAM,EAAC,UAAQ,EAACjB,QAAQ,CAACiB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QAC7D;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELd,OAAO,gBACNN,OAAA;QAAKe,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDhB,OAAA;UAAKe,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,GACJZ,KAAK,gBACPR,OAAA;QAAKe,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChB,OAAA;UAAGe,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAER;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DpB,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCV,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhB,QAAQ,CAACiB,MAAM,KAAK,CAAC,gBACvBrB,OAAA;QAAKe,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChChB,OAAA;UAAGe,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GAAC,uBACjC,EAACb,QAAQ;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENpB,OAAA;QAAKe,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFZ,QAAQ,CAACsB,GAAG,CAAEC,OAAO,iBACpB3B,OAAA,CAACH,WAAW;UAAmB8B,OAAO,EAAEA;QAAQ,GAA9BA,OAAO,CAACC,GAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAtEID,YAAY;EAAA,QACKL,SAAS;AAAA;AAAAiC,EAAA,GAD1B5B,YAAY;AAwElB,eAAeA,YAAY;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}