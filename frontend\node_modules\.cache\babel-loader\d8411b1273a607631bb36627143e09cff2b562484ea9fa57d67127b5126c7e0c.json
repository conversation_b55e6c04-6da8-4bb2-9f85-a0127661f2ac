{"ast": null, "code": "import * as React from \"react\";\nfunction SignalIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M5.636 4.575a.75.75 0 0 1 0 1.061 9 9 0 0 0 0 12.728.75.75 0 1 1-1.06 1.06c-4.101-4.1-4.101-10.748 0-14.849a.75.75 0 0 1 1.06 0Zm12.728 0a.75.75 0 0 1 1.06 0c4.101 4.1 4.101 10.75 0 14.85a.75.75 0 1 1-1.06-1.061 9 9 0 0 0 0-12.728.75.75 0 0 1 0-1.06ZM7.757 6.697a.75.75 0 0 1 0 1.06 6 6 0 0 0 0 8.486.75.75 0 0 1-1.06 1.06 7.5 7.5 0 0 1 0-10.606.75.75 0 0 1 1.06 0Zm8.486 0a.75.75 0 0 1 1.06 0 7.5 7.5 0 0 1 0 10.606.75.75 0 0 1-1.06-1.06 6 6 0 0 0 0-8.486.75.75 0 0 1 0-1.06ZM9.879 8.818a.75.75 0 0 1 0 1.06 3 3 0 0 0 0 *********** 0 1 1-1.061 1.061 4.5 4.5 0 0 1 0-6.364.75.75 0 0 1 1.06 0Zm4.242 0a.75.75 0 0 1 1.061 0 4.5 4.5 0 0 1 0 6.364.75.75 0 0 1-1.06-1.06 3 3 0 0 0 0-*********** 0 0 1 0-1.061ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SignalIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "SignalIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "fillRule", "d", "clipRule", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/node_modules/@heroicons/react/24/solid/esm/SignalIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction SignalIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M5.636 4.575a.75.75 0 0 1 0 1.061 9 9 0 0 0 0 12.728.75.75 0 1 1-1.06 1.06c-4.101-4.1-4.101-10.748 0-14.849a.75.75 0 0 1 1.06 0Zm12.728 0a.75.75 0 0 1 1.06 0c4.101 4.1 4.101 10.75 0 14.85a.75.75 0 1 1-1.06-1.061 9 9 0 0 0 0-12.728.75.75 0 0 1 0-1.06ZM7.757 6.697a.75.75 0 0 1 0 1.06 6 6 0 0 0 0 8.486.75.75 0 0 1-1.06 1.06 7.5 7.5 0 0 1 0-10.606.75.75 0 0 1 1.06 0Zm8.486 0a.75.75 0 0 1 1.06 0 7.5 7.5 0 0 1 0 10.606.75.75 0 0 1-1.06-1.06 6 6 0 0 0 0-8.486.75.75 0 0 1 0-1.06ZM9.879 8.818a.75.75 0 0 1 0 1.06 3 3 0 0 0 0 *********** 0 1 1-1.061 1.061 4.5 4.5 0 0 1 0-6.364.75.75 0 0 1 1.06 0Zm4.242 0a.75.75 0 0 1 1.061 0 4.5 4.5 0 0 1 0 6.364.75.75 0 0 1-1.06-1.06 3 3 0 0 0 0-*********** 0 0 1 0-1.061ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(SignalIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAUA,CAAC;EAClBC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEP,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEV;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDQ,QAAQ,EAAE,SAAS;IACnBC,CAAC,EAAE,+vBAA+vB;IAClwBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcjB,KAAK,CAACkB,UAAU,CAACjB,UAAU,CAAC;AAC7D,eAAegB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}