{"ast": null, "code": "import api from './authService';\nexport const wishlistService = {\n  // Get user's wishlist\n  getWishlist: async () => {\n    const response = await api.get('/wishlist');\n    return response.data.wishlist;\n  },\n  // Add product to wishlist\n  addToWishlist: async (productId, priceAlert) => {\n    await api.post(`/wishlist/add/${productId}`, {\n      priceAlert\n    });\n  },\n  // Remove product from wishlist\n  removeFromWishlist: async productId => {\n    await api.delete(`/wishlist/remove/${productId}`);\n  },\n  // Update price alert for wishlist item\n  updatePriceAlert: async (productId, enabled, targetPrice) => {\n    await api.put(`/wishlist/price-alert/${productId}`, {\n      enabled,\n      targetPrice\n    });\n  },\n  // Check if product is in wishlist\n  checkWishlist: async productId => {\n    const response = await api.get(`/wishlist/check/${productId}`);\n    return response.data.isInWishlist;\n  },\n  // Clear entire wishlist\n  clearWishlist: async () => {\n    await api.delete('/wishlist/clear');\n  }\n};", "map": {"version": 3, "names": ["api", "wishlistService", "getWishlist", "response", "get", "data", "wishlist", "addToWishlist", "productId", "priceAlert", "post", "removeFromWishlist", "delete", "updatePriceAlert", "enabled", "targetPrice", "put", "checkWishlist", "isInWishlist", "clearWishlist"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/wishlistService.ts"], "sourcesContent": ["import api from './authService';\nimport { WishlistItem } from '../types';\n\nexport const wishlistService = {\n  // Get user's wishlist\n  getWishlist: async (): Promise<WishlistItem[]> => {\n    const response = await api.get('/wishlist');\n    return response.data.wishlist;\n  },\n\n  // Add product to wishlist\n  addToWishlist: async (productId: string, priceAlert?: { enabled: boolean; targetPrice?: number }): Promise<void> => {\n    await api.post(`/wishlist/add/${productId}`, { priceAlert });\n  },\n\n  // Remove product from wishlist\n  removeFromWishlist: async (productId: string): Promise<void> => {\n    await api.delete(`/wishlist/remove/${productId}`);\n  },\n\n  // Update price alert for wishlist item\n  updatePriceAlert: async (productId: string, enabled: boolean, targetPrice?: number): Promise<void> => {\n    await api.put(`/wishlist/price-alert/${productId}`, {\n      enabled,\n      targetPrice\n    });\n  },\n\n  // Check if product is in wishlist\n  checkWishlist: async (productId: string): Promise<boolean> => {\n    const response = await api.get(`/wishlist/check/${productId}`);\n    return response.data.isInWishlist;\n  },\n\n  // Clear entire wishlist\n  clearWishlist: async (): Promise<void> => {\n    await api.delete('/wishlist/clear');\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,eAAe;AAG/B,OAAO,MAAMC,eAAe,GAAG;EAC7B;EACAC,WAAW,EAAE,MAAAA,CAAA,KAAqC;IAChD,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,WAAW,CAAC;IAC3C,OAAOD,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B,CAAC;EAED;EACAC,aAAa,EAAE,MAAAA,CAAOC,SAAiB,EAAEC,UAAuD,KAAoB;IAClH,MAAMT,GAAG,CAACU,IAAI,CAAC,iBAAiBF,SAAS,EAAE,EAAE;MAAEC;IAAW,CAAC,CAAC;EAC9D,CAAC;EAED;EACAE,kBAAkB,EAAE,MAAOH,SAAiB,IAAoB;IAC9D,MAAMR,GAAG,CAACY,MAAM,CAAC,oBAAoBJ,SAAS,EAAE,CAAC;EACnD,CAAC;EAED;EACAK,gBAAgB,EAAE,MAAAA,CAAOL,SAAiB,EAAEM,OAAgB,EAAEC,WAAoB,KAAoB;IACpG,MAAMf,GAAG,CAACgB,GAAG,CAAC,yBAAyBR,SAAS,EAAE,EAAE;MAClDM,OAAO;MACPC;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,aAAa,EAAE,MAAOT,SAAiB,IAAuB;IAC5D,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,mBAAmBI,SAAS,EAAE,CAAC;IAC9D,OAAOL,QAAQ,CAACE,IAAI,CAACa,YAAY;EACnC,CAAC;EAED;EACAC,aAAa,EAAE,MAAAA,CAAA,KAA2B;IACxC,MAAMnB,GAAG,CAACY,MAAM,CAAC,iBAAiB,CAAC;EACrC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}