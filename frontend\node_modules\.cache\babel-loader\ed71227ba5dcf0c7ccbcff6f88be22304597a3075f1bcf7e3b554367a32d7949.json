{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProfilePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { useUserStatsContext } from '../contexts/UserStatsContext';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    stats,\n    loading: statsLoading,\n    refreshStats\n  } = useUserStatsContext();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSave = async () => {\n    try {\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Please sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: \"You need to be signed in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: \"My Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), !isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsEditing(true),\n              className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(PencilIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this), \"Edit Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2 flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-12 w-12 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-400\",\n                  children: [\"Member since \", new Date().getFullYear()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), \"Full Name\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.name || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(EnvelopeIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), \"Email Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.email || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), \"Phone Number\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.phone || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), \"Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.address || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 dark:border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n              children: \"Account Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: refreshStats,\n              disabled: statsLoading,\n              className: \"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\",\n              title: \"Refresh statistics\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowPathIcon, {\n                className: `h-4 w-4 mr-1 ${statsLoading ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), \"Refresh\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), statsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center py-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Spent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Wishlist Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.wishlistCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Reviews Written\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.reviewsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"ffZvQHOBl76on5oZTNLRbd8nIts=\", false, function () {\n  return [useAuth, useUserStatsContext];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "userService", "useUserStatsContext", "UserIcon", "EnvelopeIcon", "PhoneIcon", "MapPinIcon", "PencilIcon", "ArrowPathIcon", "formatPriceIndian", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "user", "isAuthenticated", "stats", "loading", "statsLoading", "refreshStats", "isEditing", "setIsEditing", "formData", "setFormData", "name", "email", "phone", "address", "handleInputChange", "e", "value", "target", "prev", "handleSave", "updateProfile", "alert", "error", "console", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "Date", "getFullYear", "type", "onChange", "rows", "disabled", "title", "totalOrders", "totalSpent", "wishlistCount", "reviewsCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProfilePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { useUserStatsContext } from '../contexts/UserStatsContext';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\n\nconst ProfilePage = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { stats, loading: statsLoading, refreshStats } = useUserStatsContext();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSave = async () => {\n    try {\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Please sign in</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            You need to be signed in to view your profile.\n          </p>\n          <div className=\"mt-6\">\n            <a\n              href=\"/login\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Sign in\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n          {/* Header */}\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">My Profile</h1>\n              {!isEditing && (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <PencilIcon className=\"h-4 w-4 mr-2\" />\n                  Edit Profile\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Profile Content */}\n          <div className=\"px-6 py-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Profile Picture */}\n              <div className=\"md:col-span-2 flex items-center space-x-6\">\n                <div className=\"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                  <UserIcon className=\"h-12 w-12 text-gray-500 dark:text-gray-400\" />\n                </div>\n                <div>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                    {user?.name || 'User'}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Member since {new Date().getFullYear()}</p>\n                </div>\n              </div>\n\n              {/* Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <UserIcon className=\"h-4 w-4 inline mr-2\" />\n                  Full Name\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.name || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Email */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <EnvelopeIcon className=\"h-4 w-4 inline mr-2\" />\n                  Email Address\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.email || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Phone */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <PhoneIcon className=\"h-4 w-4 inline mr-2\" />\n                  Phone Number\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.phone || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Address */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 inline mr-2\" />\n                  Address\n                </label>\n                {isEditing ? (\n                  <textarea\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.address || 'Not provided'}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {isEditing && (\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  Save Changes\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Account Statistics */}\n          <div className=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Account Statistics</h3>\n              <button\n                onClick={refreshStats}\n                disabled={statsLoading}\n                className=\"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\"\n                title=\"Refresh statistics\"\n              >\n                <ArrowPathIcon className={`h-4 w-4 mr-1 ${statsLoading ? 'animate-spin' : ''}`} />\n                Refresh\n              </button>\n            </div>\n            {statsLoading ? (\n              <div className=\"flex justify-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"></div>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Orders</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.totalOrders}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Spent</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                    {stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'}\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Wishlist Items</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.wishlistCount}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Reviews Written</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.reviewsCount}</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,QAAQ,6BAA6B;AACtH,SAASC,iBAAiB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,KAAK;IAAEC,OAAO,EAAEC,YAAY;IAAEC;EAAa,CAAC,GAAGjB,mBAAmB,CAAC,CAAC;EAC5E,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF5B,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,EAAE;MACRS,WAAW,CAAC;QACVC,IAAI,EAAEV,IAAI,CAACU,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAEb,IAAI,CAACa,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,IAAI,CAAC,CAAC;EAEV,MAAMc,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAGM;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMhC,WAAW,CAACiC,aAAa,CAACZ,QAAQ,CAAC;MACzCD,YAAY,CAAC,KAAK,CAAC;MACnBc,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CD,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxB,IAAI,EAAE;MACRS,WAAW,CAAC;QACVC,IAAI,EAAEV,IAAI,CAACU,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAEb,IAAI,CAACa,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACN,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAK4B,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxF7B,OAAA;QAAK4B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7B,OAAA,CAACR,QAAQ;UAACoC,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDjC,OAAA;UAAI4B,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7FjC,OAAA;UAAG4B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjC,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7B,OAAA;YACEkC,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,EAC3J;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAK4B,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzE7B,OAAA;MAAK4B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D7B,OAAA;QAAK4B,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1D7B,OAAA;UAAK4B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtE7B,OAAA;YAAK4B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD7B,OAAA;cAAI4B,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClF,CAACxB,SAAS,iBACTT,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,IAAI,CAAE;cAClCkB,SAAS,EAAC,8OAA8O;cAAAC,QAAA,gBAExP7B,OAAA,CAACJ,UAAU;gBAACgC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7B,OAAA;YAAK4B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpD7B,OAAA;cAAK4B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD7B,OAAA;gBAAK4B,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,eACnG7B,OAAA,CAACR,QAAQ;kBAACoC,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNjC,OAAA;gBAAA6B,QAAA,gBACE7B,OAAA;kBAAI4B,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,KAAI;gBAAM;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACLjC,OAAA;kBAAG4B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,eAAa,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAO4B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF7B,OAAA,CAACR,QAAQ;kBAACoC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRT,OAAA;gBACEsC,IAAI,EAAC,MAAM;gBACXzB,IAAI,EAAC,MAAM;gBACXM,KAAK,EAAER,QAAQ,CAACE,IAAK;gBACrB0B,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFjC,OAAA;gBAAG4B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACE,IAAI,IAAI;cAAc;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAO4B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF7B,OAAA,CAACP,YAAY;kBAACmC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRT,OAAA;gBACEsC,IAAI,EAAC,OAAO;gBACZzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACG,KAAM;gBACtByB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFjC,OAAA;gBAAG4B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACG,KAAK,IAAI;cAAc;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAO4B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF7B,OAAA,CAACN,SAAS;kBAACkC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRT,OAAA;gBACEsC,IAAI,EAAC,KAAK;gBACVzB,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAER,QAAQ,CAACI,KAAM;gBACtBwB,QAAQ,EAAEtB,iBAAkB;gBAC5BW,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFjC,OAAA;gBAAG4B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACI,KAAK,IAAI;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAO4B,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChF7B,OAAA,CAACL,UAAU;kBAACiC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPxB,SAAS,gBACRT,OAAA;gBACEa,IAAI,EAAC,SAAS;gBACdM,KAAK,EAAER,QAAQ,CAACK,OAAQ;gBACxBuB,QAAQ,EAAEtB,iBAAkB;gBAC5BuB,IAAI,EAAE,CAAE;gBACRZ,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFjC,OAAA;gBAAG4B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElB,QAAQ,CAACK,OAAO,IAAI;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC7F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxB,SAAS,iBACRT,OAAA;YAAK4B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C7B,OAAA;cACEmC,OAAO,EAAER,YAAa;cACtBC,SAAS,EAAC,qNAAqN;cAAAC,QAAA,EAChO;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjC,OAAA;cACEmC,OAAO,EAAEb,UAAW;cACpBM,SAAS,EAAC,yIAAyI;cAAAC,QAAA,EACpJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtE7B,OAAA;YAAK4B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD7B,OAAA;cAAI4B,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5FjC,OAAA;cACEmC,OAAO,EAAE3B,YAAa;cACtBiC,QAAQ,EAAElC,YAAa;cACvBqB,SAAS,EAAC,kQAAkQ;cAC5Qc,KAAK,EAAC,oBAAoB;cAAAb,QAAA,gBAE1B7B,OAAA,CAACH,aAAa;gBAAC+B,SAAS,EAAE,gBAAgBrB,YAAY,GAAG,cAAc,GAAG,EAAE;cAAG;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEpF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL1B,YAAY,gBACXP,OAAA;YAAK4B,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvC7B,OAAA;cAAK4B,SAAS,EAAC;YAAmF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,gBAENjC,OAAA;YAAK4B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD7B,OAAA;cAAK4B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD7B,OAAA;gBAAG4B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxEjC,OAAA;gBAAG4B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAExB,KAAK,CAACsC;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACNjC,OAAA;cAAK4B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD7B,OAAA;gBAAG4B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvEjC,OAAA;gBAAG4B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAC/DxB,KAAK,CAACuC,UAAU,GAAG,CAAC,GAAG9C,iBAAiB,CAACO,KAAK,CAACuC,UAAU,CAAC,GAAG;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAK4B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD7B,OAAA;gBAAG4B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1EjC,OAAA;gBAAG4B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAExB,KAAK,CAACwC;cAAa;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACNjC,OAAA;cAAK4B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD7B,OAAA;gBAAG4B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3EjC,OAAA;gBAAG4B,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAExB,KAAK,CAACyC;cAAY;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAzPID,WAAW;EAAA,QACmBZ,OAAO,EACcE,mBAAmB;AAAA;AAAAwD,EAAA,GAFtE9C,WAAW;AA2PjB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}