{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FEATURED_CATEGORIES = [{\n  name: 'Electronics',\n  icon: '🔌'\n}, {\n  name: 'Fashion',\n  icon: '👕'\n}, {\n  name: 'Home & Garden',\n  icon: '🏡'\n}, {\n  name: 'Sports & Fitness',\n  icon: '⚽'\n}, {\n  name: 'Books & Media',\n  icon: '📚'\n}];\nconst HomePage = () => {\n  _s();\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const products = await productService.getFeaturedProducts(8);\n        setFeaturedProducts(products);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products. Please try again later.');\n        console.error('Error fetching products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 dark:from-blue-700 dark:via-indigo-600 dark:to-purple-700 text-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-20 bg-[url('https://images.unsplash.com/photo-1515168833906-d2a3b82b1e1b?auto=format&fit=crop&w=1200&q=80')] bg-cover bg-center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-4\",\n          children: \"Find the Best Deals Across Multiple Stores\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto\",\n          children: \"Compare prices, save money, and make smart shopping decisions with Chexkart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: \"inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg\",\n          children: \"Start Comparing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12\",\n        children: \"Shop by Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\",\n        children: FEATURED_CATEGORIES.map(category => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/category/${encodeURIComponent(category.name)}`,\n          className: \"group bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 text-center border border-gray-100 dark:border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\",\n            children: category.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)]\n        }, category.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12\",\n        children: \"Featured Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n          product: product\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"ch8T1B2fkbttm/11lJNIqQh3Jds=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "ProductCard", "productService", "jsxDEV", "_jsxDEV", "FEATURED_CATEGORIES", "name", "icon", "HomePage", "_s", "featuredProducts", "setFeaturedProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "products", "getFeaturedProducts", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "category", "encodeURIComponent", "onClick", "window", "location", "reload", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\n\nconst FEATURED_CATEGORIES = [\n  { name: 'Electronics', icon: '🔌' },\n  { name: 'Fashion', icon: '👕' },\n  { name: 'Home & Garden', icon: '🏡' },\n  { name: 'Sports & Fitness', icon: '⚽' },\n  { name: 'Books & Media', icon: '📚' },\n];\n\nconst HomePage = () => {\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const products = await productService.getFeaturedProducts(8);\n        setFeaturedProducts(products);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products. Please try again later.');\n        console.error('Error fetching products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 dark:from-blue-700 dark:via-indigo-600 dark:to-purple-700 text-white relative overflow-hidden\">\n        <div className=\"absolute inset-0 opacity-20 bg-[url('https://images.unsplash.com/photo-1515168833906-d2a3b82b1e1b?auto=format&fit=crop&w=1200&q=80')] bg-cover bg-center\"></div>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">\n            Find the Best Deals Across Multiple Stores\n          </h1>\n          <p className=\"text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto\">\n            Compare prices, save money, and make smart shopping decisions with\n            Chexkart\n          </p>\n          <Link\n            to=\"/search\"\n            className=\"inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg\"\n          >\n            Start Comparing\n          </Link>\n        </div>\n      </div>\n\n      {/* Categories Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <h2 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12\">\n          Shop by Category\n        </h2>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\">\n          {FEATURED_CATEGORIES.map((category) => (\n            <Link\n              key={category.name}\n              to={`/category/${encodeURIComponent(category.name)}`}\n              className=\"group bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 text-center border border-gray-100 dark:border-gray-700\"\n            >\n              <div className=\"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\">\n                {category.icon}\n              </div>\n              <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\">\n                {category.name}\n              </h3>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Featured Products Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <h2 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12\">\n          Featured Products\n        </h2>\n        \n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-red-600 dark:text-red-400 mb-4\">{error}</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {featuredProducts.map((product) => (\n              <ProductCard key={product._id} product={product} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,mBAAmB,GAAG,CAC1B;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE;AAAK,CAAC,EACnC;EAAED,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAK,CAAC,EAC/B;EAAED,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE;AAAK,CAAC,EACrC;EAAED,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE;AAAI,CAAC,EACvC;EAAED,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE;AAAK,CAAC,CACtC;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,MAAMkB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMf,cAAc,CAACgB,mBAAmB,CAAC,CAAC,CAAC;QAC5DP,mBAAmB,CAACM,QAAQ,CAAC;QAC7BF,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZJ,QAAQ,CAAC,kDAAkD,CAAC;QAC5DK,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEK,GAAG,CAAC;MAChD,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA;IAAKiB,SAAS,EAAC,4DAA4D;IAAAC,QAAA,gBAEzElB,OAAA;MAAKiB,SAAS,EAAC,2JAA2J;MAAAC,QAAA,gBACxKlB,OAAA;QAAKiB,SAAS,EAAC;MAA0J;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChLtB,OAAA;QAAKiB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvElB,OAAA;UAAIiB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAGiB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAC;QAG/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtB,OAAA,CAACJ,IAAI;UACH2B,EAAE,EAAC,SAAS;UACZN,SAAS,EAAC,sHAAsH;UAAAC,QAAA,EACjI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DlB,OAAA;QAAIiB,SAAS,EAAC,uEAAuE;QAAAC,QAAA,EAAC;MAEtF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtB,OAAA;QAAKiB,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEjB,mBAAmB,CAACuB,GAAG,CAAEC,QAAQ,iBAChCzB,OAAA,CAACJ,IAAI;UAEH2B,EAAE,EAAE,aAAaG,kBAAkB,CAACD,QAAQ,CAACvB,IAAI,CAAC,EAAG;UACrDe,SAAS,EAAC,+JAA+J;UAAAC,QAAA,gBAEzKlB,OAAA;YAAKiB,SAAS,EAAC,uEAAuE;YAAAC,QAAA,EACnFO,QAAQ,CAACtB;UAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNtB,OAAA;YAAIiB,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EACtIO,QAAQ,CAACvB;UAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA,GATAG,QAAQ,CAACvB,IAAI;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUd,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DlB,OAAA;QAAIiB,SAAS,EAAC,uEAAuE;QAAAC,QAAA,EAAC;MAEtF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJd,OAAO,gBACNR,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDlB,OAAA;UAAKiB,SAAS,EAAC;QAAqF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CAAC,GACJZ,KAAK,gBACPV,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClB,OAAA;UAAGiB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAER;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtB,OAAA;UACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCb,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENtB,OAAA;QAAKiB,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFZ,gBAAgB,CAACkB,GAAG,CAAEO,OAAO,iBAC5B/B,OAAA,CAACH,WAAW;UAAmBkC,OAAO,EAAEA;QAAQ,GAA9BA,OAAO,CAACC,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAjGID,QAAQ;AAAA6B,EAAA,GAAR7B,QAAQ;AAmGd,eAAeA,QAAQ;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}