{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Order interface\n\nexport const orderService = {\n  // Get user orders\n  getUserOrders: async () => {\n    try {\n      const response = await api.get('/orders');\n      return response.data;\n    } catch (error) {\n      // Return mock data for now since backend orders API might not be implemented\n      console.log('Using mock order data');\n      return getMockOrders();\n    }\n  },\n  // Get specific order\n  getOrder: async orderId => {\n    const response = await api.get(`/orders/${orderId}`);\n    return response.data;\n  },\n  // Cancel order\n  cancelOrder: async orderId => {\n    await api.put(`/orders/${orderId}/cancel`);\n  },\n  // Track order\n  trackOrder: async orderId => {\n    const response = await api.get(`/orders/${orderId}/tracking`);\n    return response.data;\n  }\n};\n\n// Mock orders data for demonstration\nconst getMockOrders = () => [{\n  _id: '1',\n  orderNumber: 'ORD-2024-001',\n  date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n  status: 'delivered',\n  total: 22990,\n  items: [{\n    productId: '1',\n    productName: 'Sony WH-1000XM4 Wireless Headphones',\n    productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n    quantity: 1,\n    price: 22990,\n    storeName: 'Amazon'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  },\n  trackingNumber: 'TRK123456789'\n}, {\n  _id: '2',\n  orderNumber: 'ORD-2024-002',\n  date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n  status: 'shipped',\n  total: 18999,\n  items: [{\n    productId: '2',\n    productName: 'Adidas Ultraboost 23',\n    productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n    quantity: 1,\n    price: 18999,\n    storeName: 'Adidas'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  },\n  trackingNumber: 'TRK987654321'\n}, {\n  _id: '3',\n  orderNumber: 'ORD-2024-003',\n  date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n  status: 'processing',\n  total: 154999,\n  items: [{\n    productId: '3',\n    productName: 'iPhone 15 Pro Max',\n    productImage: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',\n    quantity: 1,\n    price: 154999,\n    storeName: 'Apple Store'\n  }],\n  shippingAddress: {\n    name: 'John Doe',\n    address: '123 Main Street',\n    city: 'Mumbai',\n    state: 'Maharashtra',\n    pincode: '400001'\n  }\n}];\nexport default orderService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "orderService", "getUserOrders", "response", "get", "data", "error", "console", "log", "getMockOrders", "getOrder", "orderId", "cancelOrder", "put", "trackOrder", "_id", "orderNumber", "date", "Date", "now", "status", "total", "items", "productId", "productName", "productImage", "quantity", "price", "storeName", "shippingAddress", "name", "address", "city", "state", "pincode", "trackingNumber"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/orderService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Order interface\nexport interface Order {\n  _id: string;\n  orderNumber: string;\n  date: Date;\n  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';\n  total: number;\n  items: Array<{\n    productId: string;\n    productName: string;\n    productImage: string;\n    quantity: number;\n    price: number;\n    storeName: string;\n  }>;\n  shippingAddress: {\n    name: string;\n    address: string;\n    city: string;\n    state: string;\n    pincode: string;\n  };\n  trackingNumber?: string;\n}\n\nexport const orderService = {\n  // Get user orders\n  getUserOrders: async (): Promise<Order[]> => {\n    try {\n      const response = await api.get('/orders');\n      return response.data;\n    } catch (error) {\n      // Return mock data for now since backend orders API might not be implemented\n      console.log('Using mock order data');\n      return getMockOrders();\n    }\n  },\n\n  // Get specific order\n  getOrder: async (orderId: string): Promise<Order> => {\n    const response = await api.get(`/orders/${orderId}`);\n    return response.data;\n  },\n\n  // Cancel order\n  cancelOrder: async (orderId: string): Promise<void> => {\n    await api.put(`/orders/${orderId}/cancel`);\n  },\n\n  // Track order\n  trackOrder: async (orderId: string): Promise<{\n    status: string;\n    trackingNumber: string;\n    trackingUrl?: string;\n    updates: Array<{\n      date: Date;\n      status: string;\n      location: string;\n      description: string;\n    }>;\n  }> => {\n    const response = await api.get(`/orders/${orderId}/tracking`);\n    return response.data;\n  },\n};\n\n// Mock orders data for demonstration\nconst getMockOrders = (): Order[] => [\n  {\n    _id: '1',\n    orderNumber: 'ORD-2024-001',\n    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n    status: 'delivered',\n    total: 22990,\n    items: [\n      {\n        productId: '1',\n        productName: 'Sony WH-1000XM4 Wireless Headphones',\n        productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n        quantity: 1,\n        price: 22990,\n        storeName: 'Amazon'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK123456789'\n  },\n  {\n    _id: '2',\n    orderNumber: 'ORD-2024-002',\n    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n    status: 'shipped',\n    total: 18999,\n    items: [\n      {\n        productId: '2',\n        productName: 'Adidas Ultraboost 23',\n        productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n        quantity: 1,\n        price: 18999,\n        storeName: 'Adidas'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK987654321'\n  },\n  {\n    _id: '3',\n    orderNumber: 'ORD-2024-003',\n    date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n    status: 'processing',\n    total: 154999,\n    items: [\n      {\n        productId: '3',\n        productName: 'iPhone 15 Pro Max',\n        productImage: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',\n        quantity: 1,\n        price: 154999,\n        storeName: 'Apple Store'\n      }\n    ],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    }\n  }\n];\n\nexport default orderService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;;AAyBA,OAAO,MAAMK,YAAY,GAAG;EAC1B;EACAC,aAAa,EAAE,MAAAA,CAAA,KAA8B;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,SAAS,CAAC;MACzC,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,OAAOC,aAAa,CAAC,CAAC;IACxB;EACF,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOC,OAAe,IAAqB;IACnD,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAWO,OAAO,EAAE,CAAC;IACpD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,WAAW,EAAE,MAAOD,OAAe,IAAoB;IACrD,MAAMtB,GAAG,CAACwB,GAAG,CAAC,WAAWF,OAAO,SAAS,CAAC;EAC5C,CAAC;EAED;EACAG,UAAU,EAAE,MAAOH,OAAe,IAU5B;IACJ,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAWO,OAAO,WAAW,CAAC;IAC7D,OAAOR,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,MAAMI,aAAa,GAAGA,CAAA,KAAe,CACnC;EACEM,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,qCAAqC;IAClDC,YAAY,EAAE,oEAAoE;IAClFC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC,EACD;EACEpB,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,sBAAsB;IACnCC,YAAY,EAAE,iEAAiE;IAC/EC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC,EACD;EACEpB,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpDC,MAAM,EAAE,YAAY;EACpBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,CACL;IACEC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,mBAAmB;IAChCC,YAAY,EAAE,oEAAoE;IAClFC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;EACX;AACF,CAAC,CACF;AAED,eAAejC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}