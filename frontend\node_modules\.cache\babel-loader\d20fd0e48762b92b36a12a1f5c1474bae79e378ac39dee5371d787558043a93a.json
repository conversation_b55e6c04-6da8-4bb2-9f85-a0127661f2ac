{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\NotFoundPage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { HomeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFoundPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-9xl font-bold text-blue-600 dark:text-blue-400\",\n          children: \"404\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mt-4\",\n          children: \"Page Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mt-2\",\n          children: \"Sorry, we couldn't find the page you're looking for.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), \"Go Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 dark:text-gray-400\",\n            children: \"or\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: \"inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), \"Search Products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 dark:text-gray-400\",\n          children: [\"If you think this is an error, please\", ' ', /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"mailto:<EMAIL>\",\n            className: \"text-blue-600 dark:text-blue-400 hover:underline\",\n            children: \"contact support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = NotFoundPage;\nexport default NotFoundPage;\nvar _c;\n$RefreshReg$(_c, \"NotFoundPage\");", "map": {"version": 3, "names": ["React", "Link", "HomeIcon", "MagnifyingGlassIcon", "jsxDEV", "_jsxDEV", "NotFoundPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/NotFoundPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { HomeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\n\nconst NotFoundPage = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-blue-600 dark:text-blue-400\">404</h1>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mt-4\">\n            Page Not Found\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n            Sorry, we couldn't find the page you're looking for.\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <Link\n            to=\"/\"\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors\"\n          >\n            <HomeIcon className=\"h-5 w-5 mr-2\" />\n            Go Home\n          </Link>\n          \n          <div className=\"text-center\">\n            <span className=\"text-gray-500 dark:text-gray-400\">or</span>\n          </div>\n\n          <Link\n            to=\"/search\"\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <MagnifyingGlassIcon className=\"h-5 w-5 mr-2\" />\n            Search Products\n          </Link>\n        </div>\n\n        <div className=\"mt-8\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            If you think this is an error, please{' '}\n            <a href=\"mailto:<EMAIL>\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">\n              contact support\n            </a>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFoundPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,EAAEC,mBAAmB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,oBACED,OAAA;IAAKE,SAAS,EAAC,gGAAgG;IAAAC,QAAA,eAC7GH,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CH,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAIE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EP,OAAA;UAAIE,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA,CAACJ,IAAI;UACHY,EAAE,EAAC,GAAG;UACNN,SAAS,EAAC,kMAAkM;UAAAC,QAAA,gBAE5MH,OAAA,CAACH,QAAQ;YAACK,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEPP,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BH,OAAA;YAAME,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAENP,OAAA,CAACJ,IAAI;UACHY,EAAE,EAAC,SAAS;UACZN,SAAS,EAAC,sOAAsO;UAAAC,QAAA,gBAEhPH,OAAA,CAACF,mBAAmB;YAACI,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAElD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBH,OAAA;UAAGE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GAAC,uCACjB,EAAC,GAAG,eACzCH,OAAA;YAAGS,IAAI,EAAC,6BAA6B;YAACP,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEnG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA/CIT,YAAY;AAiDlB,eAAeA,YAAY;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}