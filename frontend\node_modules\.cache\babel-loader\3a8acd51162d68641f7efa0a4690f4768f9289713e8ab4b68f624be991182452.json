{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\TodaysDealsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TodaysDealsPage = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = await productService.getSaleProducts();\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load deals');\n        console.error('Error fetching deals:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n          children: \"Today's Deals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mt-2\",\n          children: \"Best discounts and offers available today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg\",\n          children: \"No deals available at the moment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n          product: product\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(TodaysDealsPage, \"y7+sQ4EOFUb33DGpdrz/8zFmpYg=\");\n_c = TodaysDealsPage;\nexport default TodaysDealsPage;\nvar _c;\n$RefreshReg$(_c, \"TodaysDealsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ProductCard", "productService", "jsxDEV", "_jsxDEV", "TodaysDealsPage", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "results", "getSaleProducts", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "length", "map", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/TodaysDealsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\n\nconst TodaysDealsPage = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = await productService.getSaleProducts();\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load deals');\n        console.error('Error fetching deals:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n            Today's Deals\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n            Best discounts and offers available today\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-red-600 dark:text-red-400 mb-4\">{error}</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        ) : products.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-600 dark:text-gray-400 text-lg\">\n              No deals available at the moment\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {products.map((product) => (\n              <ProductCard key={product._id} product={product} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TodaysDealsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,OAAO,GAAG,MAAMZ,cAAc,CAACa,eAAe,CAAC,CAAC;QACtDP,WAAW,CAACM,OAAO,CAACP,QAAQ,IAAIO,OAAO,CAAC;QACxCF,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZJ,QAAQ,CAAC,sBAAsB,CAAC;QAChCK,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEK,GAAG,CAAC;MAC7C,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACET,OAAA;IAAKc,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5Df,OAAA;MAAKc,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDf,OAAA;QAAKc,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBf,OAAA;UAAIc,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnB,OAAA;UAAGc,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAELd,OAAO,gBACNL,OAAA;QAAKc,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDf,OAAA;UAAKc,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,GACJZ,KAAK,gBACPP,OAAA;QAAKc,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCf,OAAA;UAAGc,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAER;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DnB,OAAA;UACEoB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCT,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhB,QAAQ,CAACqB,MAAM,KAAK,CAAC,gBACvBxB,OAAA;QAAKc,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCf,OAAA;UAAGc,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENnB,OAAA;QAAKc,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFZ,QAAQ,CAACsB,GAAG,CAAEC,OAAO,iBACpB1B,OAAA,CAACH,WAAW;UAAmB6B,OAAO,EAAEA;QAAQ,GAA9BA,OAAO,CAACC,GAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAjEID,eAAe;AAAA2B,EAAA,GAAf3B,eAAe;AAmErB,eAAeA,eAAe;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}