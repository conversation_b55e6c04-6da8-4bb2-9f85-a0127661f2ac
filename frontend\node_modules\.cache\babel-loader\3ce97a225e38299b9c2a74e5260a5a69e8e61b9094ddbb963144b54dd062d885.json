{"ast": null, "code": "/**\n * Format price in Indian Rupees\n * @param {number} price - Price in paise (smallest unit)\n * @returns {string} Formatted price string\n */\nexport const formatPriceIndian = price => {\n  if (typeof price !== 'number' || isNaN(price)) {\n    return '₹0';\n  }\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  }).format(price);\n};\n\n/**\n * Get image URL with fallback\n * @param {Object} image - Image object\n * @returns {string} Image URL\n */\nexport const getImageUrl = image => {\n  if (!image || !image.url) {\n    return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop';\n  }\n  return image.url;\n};\n\n/**\n * Get image alt text with fallback\n * @param {Object} image - Image object\n * @param {string} fallback - Fallback alt text\n * @returns {string} Alt text\n */\nexport const getImageAlt = (image, fallback = 'Product image') => {\n  if (!image || !image.alt) {\n    return fallback;\n  }\n  return image.alt;\n};\n\n/**\n * Calculate discount percentage\n * @param {number} originalPrice - Original price\n * @param {number} salePrice - Sale price\n * @returns {number} Discount percentage\n */\nexport const calculateDiscount = (originalPrice, salePrice) => {\n  if (!originalPrice || !salePrice || originalPrice <= salePrice) {\n    return 0;\n  }\n  return Math.round((originalPrice - salePrice) / originalPrice * 100);\n};\n\n/**\n * Format discount percentage\n * @param {number} discount - Discount percentage\n * @returns {string} Formatted discount string\n */\nexport const formatDiscount = discount => {\n  if (!discount || discount <= 0) {\n    return '';\n  }\n  return `${discount}% OFF`;\n};\n\n/**\n * Get the lowest price from an array of prices\n * @param {Array} prices - Array of price objects\n * @returns {Object|null} Lowest price object\n */\nexport const getLowestPrice = prices => {\n  if (!prices || !Array.isArray(prices) || prices.length === 0) {\n    return null;\n  }\n  return prices.reduce((lowest, current) => {\n    return current.price < lowest.price ? current : lowest;\n  });\n};\n\n/**\n * Get the highest price from an array of prices\n * @param {Array} prices - Array of price objects\n * @returns {Object|null} Highest price object\n */\nexport const getHighestPrice = prices => {\n  if (!prices || !Array.isArray(prices) || prices.length === 0) {\n    return null;\n  }\n  return prices.reduce((highest, current) => {\n    return current.price > highest.price ? current : highest;\n  });\n};\n\n/**\n * Calculate savings between highest and lowest price\n * @param {Array} prices - Array of price objects\n * @returns {number} Savings amount\n */\nexport const calculateSavings = prices => {\n  const lowest = getLowestPrice(prices);\n  const highest = getHighestPrice(prices);\n  if (!lowest || !highest) {\n    return 0;\n  }\n  return highest.price - lowest.price;\n};", "map": {"version": 3, "names": ["formatPriceIndian", "price", "isNaN", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getImageUrl", "image", "url", "getImageAlt", "fallback", "alt", "calculateDiscount", "originalPrice", "salePrice", "Math", "round", "formatDiscount", "discount", "getLowestPrice", "prices", "Array", "isArray", "length", "reduce", "lowest", "current", "getHighestPrice", "highest", "calculateSavings"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/utils/currency.js"], "sourcesContent": ["/**\n * Format price in Indian Rupees\n * @param {number} price - Price in paise (smallest unit)\n * @returns {string} Formatted price string\n */\nexport const formatPriceIndian = (price) => {\n  if (typeof price !== 'number' || isNaN(price)) {\n    return '₹0';\n  }\n  \n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(price);\n};\n\n/**\n * Get image URL with fallback\n * @param {Object} image - Image object\n * @returns {string} Image URL\n */\nexport const getImageUrl = (image) => {\n  if (!image || !image.url) {\n    return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop';\n  }\n  return image.url;\n};\n\n/**\n * Get image alt text with fallback\n * @param {Object} image - Image object\n * @param {string} fallback - Fallback alt text\n * @returns {string} Alt text\n */\nexport const getImageAlt = (image, fallback = 'Product image') => {\n  if (!image || !image.alt) {\n    return fallback;\n  }\n  return image.alt;\n};\n\n/**\n * Calculate discount percentage\n * @param {number} originalPrice - Original price\n * @param {number} salePrice - Sale price\n * @returns {number} Discount percentage\n */\nexport const calculateDiscount = (originalPrice, salePrice) => {\n  if (!originalPrice || !salePrice || originalPrice <= salePrice) {\n    return 0;\n  }\n  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);\n};\n\n/**\n * Format discount percentage\n * @param {number} discount - Discount percentage\n * @returns {string} Formatted discount string\n */\nexport const formatDiscount = (discount) => {\n  if (!discount || discount <= 0) {\n    return '';\n  }\n  return `${discount}% OFF`;\n};\n\n/**\n * Get the lowest price from an array of prices\n * @param {Array} prices - Array of price objects\n * @returns {Object|null} Lowest price object\n */\nexport const getLowestPrice = (prices) => {\n  if (!prices || !Array.isArray(prices) || prices.length === 0) {\n    return null;\n  }\n  \n  return prices.reduce((lowest, current) => {\n    return current.price < lowest.price ? current : lowest;\n  });\n};\n\n/**\n * Get the highest price from an array of prices\n * @param {Array} prices - Array of price objects\n * @returns {Object|null} Highest price object\n */\nexport const getHighestPrice = (prices) => {\n  if (!prices || !Array.isArray(prices) || prices.length === 0) {\n    return null;\n  }\n  \n  return prices.reduce((highest, current) => {\n    return current.price > highest.price ? current : highest;\n  });\n};\n\n/**\n * Calculate savings between highest and lowest price\n * @param {Array} prices - Array of price objects\n * @returns {number} Savings amount\n */\nexport const calculateSavings = (prices) => {\n  const lowest = getLowestPrice(prices);\n  const highest = getHighestPrice(prices);\n  \n  if (!lowest || !highest) {\n    return 0;\n  }\n  \n  return highest.price - lowest.price;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,GAAIC,KAAK,IAAK;EAC1C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,KAAK,CAACD,KAAK,CAAC,EAAE;IAC7C,OAAO,IAAI;EACb;EAEA,OAAO,IAAIE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACR,KAAK,CAAC;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACC,GAAG,EAAE;IACxB,OAAO,gFAAgF;EACzF;EACA,OAAOD,KAAK,CAACC,GAAG;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAGA,CAACF,KAAK,EAAEG,QAAQ,GAAG,eAAe,KAAK;EAChE,IAAI,CAACH,KAAK,IAAI,CAACA,KAAK,CAACI,GAAG,EAAE;IACxB,OAAOD,QAAQ;EACjB;EACA,OAAOH,KAAK,CAACI,GAAG;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,SAAS,KAAK;EAC7D,IAAI,CAACD,aAAa,IAAI,CAACC,SAAS,IAAID,aAAa,IAAIC,SAAS,EAAE;IAC9D,OAAO,CAAC;EACV;EACA,OAAOC,IAAI,CAACC,KAAK,CAAE,CAACH,aAAa,GAAGC,SAAS,IAAID,aAAa,GAAI,GAAG,CAAC;AACxE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAIC,QAAQ,IAAK;EAC1C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,IAAI,CAAC,EAAE;IAC9B,OAAO,EAAE;EACX;EACA,OAAO,GAAGA,QAAQ,OAAO;AAC3B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAI,CAACA,MAAM,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACb;EAEA,OAAOH,MAAM,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEC,OAAO,KAAK;IACxC,OAAOA,OAAO,CAAC7B,KAAK,GAAG4B,MAAM,CAAC5B,KAAK,GAAG6B,OAAO,GAAGD,MAAM;EACxD,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,eAAe,GAAIP,MAAM,IAAK;EACzC,IAAI,CAACA,MAAM,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACb;EAEA,OAAOH,MAAM,CAACI,MAAM,CAAC,CAACI,OAAO,EAAEF,OAAO,KAAK;IACzC,OAAOA,OAAO,CAAC7B,KAAK,GAAG+B,OAAO,CAAC/B,KAAK,GAAG6B,OAAO,GAAGE,OAAO;EAC1D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAIT,MAAM,IAAK;EAC1C,MAAMK,MAAM,GAAGN,cAAc,CAACC,MAAM,CAAC;EACrC,MAAMQ,OAAO,GAAGD,eAAe,CAACP,MAAM,CAAC;EAEvC,IAAI,CAACK,MAAM,IAAI,CAACG,OAAO,EAAE;IACvB,OAAO,CAAC;EACV;EAEA,OAAOA,OAAO,CAAC/B,KAAK,GAAG4B,MAAM,CAAC5B,KAAK;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}