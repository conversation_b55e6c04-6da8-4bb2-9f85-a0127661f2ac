{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegisterPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const checkPasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (name === 'password') {\n      setPasswordStrength(checkPasswordStrength(value));\n    }\n  };\n  const getPasswordStrengthColor = () => {\n    if (passwordStrength <= 2) return 'bg-red-500';\n    if (passwordStrength <= 3) return 'bg-yellow-500';\n    return 'bg-green-500';\n  };\n  const getPasswordStrengthText = () => {\n    if (passwordStrength <= 2) return 'Weak';\n    if (passwordStrength <= 3) return 'Medium';\n    return 'Strong';\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n    setLoading(true);\n    try {\n      await register(formData.name, formData.email, formData.password);\n      navigate('/', {\n        replace: true\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-blue-600 hover:text-blue-500\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"name\",\n              name: \"name\",\n              type: \"text\",\n              autoComplete: \"name\",\n              required: true,\n              value: formData.name,\n              onChange: handleInputChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n              placeholder: \"Enter your full name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              autoComplete: \"email\",\n              required: true,\n              value: formData.email,\n              onChange: handleInputChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Phone Number (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"phone\",\n              name: \"phone\",\n              type: \"tel\",\n              autoComplete: \"tel\",\n              value: formData.phone,\n              onChange: handleInputChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n              placeholder: \"Enter your phone number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: showPassword ? 'text' : 'password',\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleInputChange,\n                className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                placeholder: \"Create a password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`,\n                    style: {\n                      width: `${passwordStrength / 5 * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-600\",\n                  children: getPasswordStrengthText()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: showConfirmPassword ? 'text' : 'password',\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.confirmPassword,\n                onChange: handleInputChange,\n                className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                placeholder: \"Confirm your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"terms\",\n            name: \"terms\",\n            type: \"checkbox\",\n            required: true,\n            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"terms\",\n            className: \"ml-2 block text-sm text-gray-900\",\n            children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/terms\",\n              className: \"text-blue-600 hover:text-blue-500\",\n              children: \"Terms and Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/privacy\",\n              className: \"text-blue-600 hover:text-blue-500\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), \"Creating account...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this) : 'Create account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"bvUNuaz3wpp83FXTiOscd4K+06s=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "EyeIcon", "EyeSlashIcon", "jsxDEV", "_jsxDEV", "RegisterPage", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "passwordStrength", "setPasswordStrength", "register", "navigate", "checkPasswordStrength", "strength", "length", "test", "handleInputChange", "e", "value", "target", "prev", "getPasswordStrengthColor", "getPasswordStrengthText", "handleSubmit", "preventDefault", "replace", "_error$response", "_error$response$data", "response", "data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "id", "type", "autoComplete", "required", "onChange", "placeholder", "onClick", "style", "width", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\nconst RegisterPage: React.FC = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [passwordStrength, setPasswordStrength] = useState(0);\n\n  const { register } = useAuth();\n  const navigate = useNavigate();\n\n  const checkPasswordStrength = (password: string) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    if (name === 'password') {\n      setPasswordStrength(checkPasswordStrength(value));\n    }\n  };\n\n  const getPasswordStrengthColor = () => {\n    if (passwordStrength <= 2) return 'bg-red-500';\n    if (passwordStrength <= 3) return 'bg-yellow-500';\n    return 'bg-green-500';\n  };\n\n  const getPasswordStrengthText = () => {\n    if (passwordStrength <= 2) return 'Weak';\n    if (passwordStrength <= 3) return 'Medium';\n    return 'Strong';\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      await register(formData.name, formData.email, formData.password);\n      navigate('/', { replace: true });\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                autoComplete=\"name\"\n                required\n                value={formData.name}\n                onChange={handleInputChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={formData.email}\n                onChange={handleInputChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Phone Number (Optional)\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                autoComplete=\"tel\"\n                value={formData.phone}\n                onChange={handleInputChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your phone number\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Create a password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n              \n              {formData.password && (\n                <div className=\"mt-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}\n                        style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-xs text-gray-600\">{getPasswordStrengthText()}</span>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleInputChange}\n                  className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Confirm your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                >\n                  {showConfirmPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center\">\n            <input\n              id=\"terms\"\n              name=\"terms\"\n              type=\"checkbox\"\n              required\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"terms\" className=\"ml-2 block text-sm text-gray-900\">\n              I agree to the{' '}\n              <Link to=\"/terms\" className=\"text-blue-600 hover:text-blue-500\">\n                Terms and Conditions\n              </Link>{' '}\n              and{' '}\n              <Link to=\"/privacy\" className=\"text-blue-600 hover:text-blue-500\">\n                Privacy Policy\n              </Link>\n            </label>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Creating account...\n                </div>\n              ) : (\n                'Create account'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,EAAEC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAM;IAAE2B;EAAS,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,qBAAqB,GAAIf,QAAgB,IAAK;IAClD,IAAIgB,QAAQ,GAAG,CAAC;IAChB,IAAIhB,QAAQ,CAACiB,MAAM,IAAI,CAAC,EAAED,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,EAAE;IACtC,IAAI,cAAc,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,EAAE;IAC7C,OAAOA,QAAQ;EACjB,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEtB,IAAI;MAAEuB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCzB,WAAW,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACzB,IAAI,GAAGuB;IAAM,CAAC,CAAC,CAAC;IAEjD,IAAIvB,IAAI,KAAK,UAAU,EAAE;MACvBc,mBAAmB,CAACG,qBAAqB,CAACM,KAAK,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIb,gBAAgB,IAAI,CAAC,EAAE,OAAO,YAAY;IAC9C,IAAIA,gBAAgB,IAAI,CAAC,EAAE,OAAO,eAAe;IACjD,OAAO,cAAc;EACvB,CAAC;EAED,MAAMc,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAId,gBAAgB,IAAI,CAAC,EAAE,OAAO,MAAM;IACxC,IAAIA,gBAAgB,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC1C,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMe,YAAY,GAAG,MAAON,CAAkB,IAAK;IACjDA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBjB,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAId,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDS,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAId,QAAQ,CAACI,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAE;MAChCP,QAAQ,CAAC,6CAA6C,CAAC;MACvD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMK,QAAQ,CAACjB,QAAQ,CAACE,IAAI,EAAEF,QAAQ,CAACG,KAAK,EAAEH,QAAQ,CAACI,QAAQ,CAAC;MAChEc,QAAQ,CAAC,GAAG,EAAE;QAAEc,OAAO,EAAE;MAAK,CAAC,CAAC;IAClC,CAAC,CAAC,OAAOnB,KAAU,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACnBpB,QAAQ,CAAC,EAAAmB,eAAA,GAAApB,KAAK,CAACsB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,wCAAwC,CAAC;IACrF,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEf,OAAA;IAAKyC,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClG1C,OAAA;MAAKyC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC1C,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAIyC,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9C,OAAA;UAAGyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACN1C,OAAA,CAACN,IAAI;YACHqD,EAAE,EAAC,QAAQ;YACXN,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9C,OAAA;QAAMyC,SAAS,EAAC,gBAAgB;QAACO,QAAQ,EAAEf,YAAa;QAAAS,QAAA,GACrD1B,KAAK,iBACJhB,OAAA;UAAKyC,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAC/E1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOiD,OAAO,EAAC,MAAM;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEkD,EAAE,EAAC,MAAM;cACT7C,IAAI,EAAC,MAAM;cACX8C,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,MAAM;cACnBC,QAAQ;cACRzB,KAAK,EAAEzB,QAAQ,CAACE,IAAK;cACrBiD,QAAQ,EAAE5B,iBAAkB;cAC5Be,SAAS,EAAC,8MAA8M;cACxNc,WAAW,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOiD,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEkD,EAAE,EAAC,OAAO;cACV7C,IAAI,EAAC,OAAO;cACZ8C,IAAI,EAAC,OAAO;cACZC,YAAY,EAAC,OAAO;cACpBC,QAAQ;cACRzB,KAAK,EAAEzB,QAAQ,CAACG,KAAM;cACtBgD,QAAQ,EAAE5B,iBAAkB;cAC5Be,SAAS,EAAC,8MAA8M;cACxNc,WAAW,EAAC;YAAkB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOiD,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEkD,EAAE,EAAC,OAAO;cACV7C,IAAI,EAAC,OAAO;cACZ8C,IAAI,EAAC,KAAK;cACVC,YAAY,EAAC,KAAK;cAClBxB,KAAK,EAAEzB,QAAQ,CAACM,KAAM;cACtB6C,QAAQ,EAAE5B,iBAAkB;cAC5Be,SAAS,EAAC,8MAA8M;cACxNc,WAAW,EAAC;YAAyB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOiD,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1C,OAAA;gBACEkD,EAAE,EAAC,UAAU;gBACb7C,IAAI,EAAC,UAAU;gBACf8C,IAAI,EAAEzC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC0C,YAAY,EAAC,cAAc;gBAC3BC,QAAQ;gBACRzB,KAAK,EAAEzB,QAAQ,CAACI,QAAS;gBACzB+C,QAAQ,EAAE5B,iBAAkB;gBAC5Be,SAAS,EAAC,+MAA+M;gBACzNc,WAAW,EAAC;cAAmB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACF9C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,mDAAmD;gBAC7De,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAgC,QAAA,EAE7ChC,YAAY,gBACXV,OAAA,CAACF,YAAY;kBAAC2C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAElD9C,OAAA,CAACH,OAAO;kBAAC4C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC7C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEL3C,QAAQ,CAACI,QAAQ,iBAChBP,OAAA;cAAKyC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB1C,OAAA;gBAAKyC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C1C,OAAA;kBAAKyC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClD1C,OAAA;oBACEyC,SAAS,EAAE,gDAAgDV,wBAAwB,CAAC,CAAC,EAAG;oBACxF0B,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAIxC,gBAAgB,GAAG,CAAC,GAAI,GAAG;oBAAI;kBAAE;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN9C,OAAA;kBAAMyC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEV,uBAAuB,CAAC;gBAAC;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOiD,OAAO,EAAC,iBAAiB;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1C,OAAA;gBACEkD,EAAE,EAAC,iBAAiB;gBACpB7C,IAAI,EAAC,iBAAiB;gBACtB8C,IAAI,EAAEvC,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChDwC,YAAY,EAAC,cAAc;gBAC3BC,QAAQ;gBACRzB,KAAK,EAAEzB,QAAQ,CAACK,eAAgB;gBAChC8C,QAAQ,EAAE5B,iBAAkB;gBAC5Be,SAAS,EAAC,+MAA+M;gBACzNc,WAAW,EAAC;cAAuB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACF9C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,mDAAmD;gBAC7De,OAAO,EAAEA,CAAA,KAAM3C,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAAA8B,QAAA,EAE3D9B,mBAAmB,gBAClBZ,OAAA,CAACF,YAAY;kBAAC2C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAElD9C,OAAA,CAACH,OAAO;kBAAC4C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC7C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YACEkD,EAAE,EAAC,OAAO;YACV7C,IAAI,EAAC,OAAO;YACZ8C,IAAI,EAAC,UAAU;YACfE,QAAQ;YACRZ,SAAS,EAAC;UAAmE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACF9C,OAAA;YAAOiD,OAAO,EAAC,OAAO;YAACR,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,gBACpD,EAAC,GAAG,eAClB1C,OAAA,CAACN,IAAI;cAACqD,EAAE,EAAC,QAAQ;cAACN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACP9C,OAAA,CAACN,IAAI;cAACqD,EAAE,EAAC,UAAU;cAACN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN9C,OAAA;UAAA0C,QAAA,eACE1C,OAAA;YACEmD,IAAI,EAAC,QAAQ;YACbQ,QAAQ,EAAE7C,OAAQ;YAClB2B,SAAS,EAAC,+QAA+Q;YAAAC,QAAA,EAExR5B,OAAO,gBACNd,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,uBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA1QID,YAAsB;EAAA,QAcLL,OAAO,EACXD,WAAW;AAAA;AAAAiE,EAAA,GAfxB3D,YAAsB;AA4Q5B,eAAeA,YAAY;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}