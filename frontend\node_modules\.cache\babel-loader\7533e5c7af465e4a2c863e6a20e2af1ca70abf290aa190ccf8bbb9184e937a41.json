{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\DarkModeToggle.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkModeToggle = () => {\n  _s();\n  const {\n    isDarkMode,\n    toggleDarkMode\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: toggleDarkMode,\n    className: \"p-2 rounded-md text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors\",\n    title: isDarkMode ? 'Switch to light mode' : 'Switch to dark mode',\n    children: isDarkMode ? /*#__PURE__*/_jsxDEV(SunIcon, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(MoonIcon, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkModeToggle, \"Xis6DAqJpZpOAs/vGxfgALZmYzA=\", false, function () {\n  return [useTheme];\n});\n_c = DarkModeToggle;\nexport default DarkModeToggle;\nvar _c;\n$RefreshReg$(_c, \"DarkModeToggle\");", "map": {"version": 3, "names": ["React", "SunIcon", "MoonIcon", "useTheme", "jsxDEV", "_jsxDEV", "DarkModeToggle", "_s", "isDarkMode", "toggleDarkMode", "onClick", "className", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/DarkModeToggle.tsx"], "sourcesContent": ["import React from 'react';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst DarkModeToggle: React.FC = () => {\n  const { isDarkMode, toggleDarkMode } = useTheme();\n\n  return (\n    <button\n      onClick={toggleDarkMode}\n      className=\"p-2 rounded-md text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors\"\n      title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      {isDarkMode ? (\n        <SunIcon className=\"h-5 w-5\" />\n      ) : (\n        <MoonIcon className=\"h-5 w-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default DarkModeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,QAAQ,QAAQ,6BAA6B;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC,UAAU;IAAEC;EAAe,CAAC,GAAGN,QAAQ,CAAC,CAAC;EAEjD,oBACEE,OAAA;IACEK,OAAO,EAAED,cAAe;IACxBE,SAAS,EAAC,gHAAgH;IAC1HC,KAAK,EAAEJ,UAAU,GAAG,sBAAsB,GAAG,qBAAsB;IAAAK,QAAA,EAElEL,UAAU,gBACTH,OAAA,CAACJ,OAAO;MAACU,SAAS,EAAC;IAAS;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAE/BZ,OAAA,CAACH,QAAQ;MAACS,SAAS,EAAC;IAAS;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAChC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACV,EAAA,CAhBID,cAAwB;EAAA,QACWH,QAAQ;AAAA;AAAAe,EAAA,GAD3CZ,cAAwB;AAkB9B,eAAeA,cAAc;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}