{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FEATURED_CATEGORIES = [{\n  name: 'Electronics',\n  icon: '🔌'\n}, {\n  name: 'Fashion',\n  icon: '👕'\n}, {\n  name: 'Home & Garden',\n  icon: '🏡'\n}, {\n  name: 'Sports',\n  icon: '⚽'\n}];\nconst HomePage = () => {\n  _s();\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const products = await productService.getProducts({\n          sort: 'rating'\n        });\n        setFeaturedProducts(products.slice(0, 8));\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products. Please try again later.');\n        console.error('Error fetching products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 dark:from-blue-700 dark:via-indigo-600 dark:to-purple-700 text-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-20 bg-[url('https://images.unsplash.com/photo-1515168833906-d2a3b82b1e1b?auto=format&fit=crop&w=1200&q=80')] bg-cover bg-center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-4\",\n          children: \"Find the Best Deals Across Multiple Stores\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto\",\n          children: \"Compare prices, save money, and make smart shopping decisions with PriceCompare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: \"inline-block bg-white dark:bg-gray-100 text-blue-600 dark:text-blue-700 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 dark:hover:bg-gray-200 transition-colors\",\n          children: \"Start Comparing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100\",\n        children: \"Popular Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: FEATURED_CATEGORIES.map(category => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/search?category=${encodeURIComponent(category.name)}`,\n          className: \"card text-center hover:shadow-lg transition-shadow group\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-4xl mb-2 block group-hover:scale-110 transition-transform\",\n            children: category.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)]\n        }, category.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold mb-6\",\n        children: \"Featured Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading products...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n          product: product\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"ch8T1B2fkbttm/11lJNIqQh3Jds=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "ProductCard", "productService", "jsxDEV", "_jsxDEV", "FEATURED_CATEGORIES", "name", "icon", "HomePage", "_s", "featuredProducts", "setFeaturedProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "products", "getProducts", "sort", "slice", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "category", "encodeURIComponent", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { Product } from '../types';\nimport { productService } from '../services/api';\n\nconst FEATURED_CATEGORIES = [\n  { name: 'Electronics', icon: '🔌' },\n  { name: 'Fashion', icon: '👕' },\n  { name: 'Home & Garden', icon: '🏡' },\n  { name: 'Sports', icon: '⚽' },\n];\n\nconst HomePage: React.FC = () => {\n  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const products = await productService.getProducts({ sort: 'rating' });\n        setFeaturedProducts(products.slice(0, 8));\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products. Please try again later.');\n        console.error('Error fetching products:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 dark:from-blue-700 dark:via-indigo-600 dark:to-purple-700 text-white relative overflow-hidden\">\n  <div className=\"absolute inset-0 opacity-20 bg-[url('https://images.unsplash.com/photo-1515168833906-d2a3b82b1e1b?auto=format&fit=crop&w=1200&q=80')] bg-cover bg-center\"></div>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">\n            Find the Best Deals Across Multiple Stores\n          </h1>\n          <p className=\"text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto\">\n            Compare prices, save money, and make smart shopping decisions with\n            PriceCompare\n          </p>\n          <Link\n            to=\"/search\"\n            className=\"inline-block bg-white dark:bg-gray-100 text-blue-600 dark:text-blue-700 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 dark:hover:bg-gray-200 transition-colors\"\n          >\n            Start Comparing\n          </Link>\n        </div>\n      </div>\n\n      {/* Categories Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <h2 className=\"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100\">Popular Categories</h2>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          {FEATURED_CATEGORIES.map((category) => (\n            <Link\n              key={category.name}\n              to={`/search?category=${encodeURIComponent(category.name)}`}\n              className=\"card text-center hover:shadow-lg transition-shadow group\"\n            >\n              <span className=\"text-4xl mb-2 block group-hover:scale-110 transition-transform\">\n                {category.icon}\n              </span>\n              <h3 className=\"text-lg font-medium text-gray-900\">\n                {category.name}\n              </h3>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Featured Products Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <h2 className=\"text-2xl font-bold mb-6\">Featured Products</h2>\n        {loading ? (\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading products...</p>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {featuredProducts.map((product) => (\n              <ProductCard key={product._id} product={product} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,SAASC,cAAc,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,mBAAmB,GAAG,CAC1B;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE;AAAK,CAAC,EACnC;EAAED,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAK,CAAC,EAC/B;EAAED,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE;AAAK,CAAC,EACrC;EAAED,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAI,CAAC,CAC9B;AAED,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAY,EAAE,CAAC;EACvE,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,MAAMkB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMf,cAAc,CAACgB,WAAW,CAAC;UAAEC,IAAI,EAAE;QAAS,CAAC,CAAC;QACrER,mBAAmB,CAACM,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzCL,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZN,QAAQ,CAAC,kDAAkD,CAAC;QAC5DO,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;MAChD,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA;IAAKmB,SAAS,EAAC,4DAA4D;IAAAC,QAAA,gBAEzEpB,OAAA;MAAKmB,SAAS,EAAC,2JAA2J;MAAAC,QAAA,gBAC9KpB,OAAA;QAAKmB,SAAS,EAAC;MAA0J;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1KxB,OAAA;QAAKmB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEpB,OAAA;UAAImB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxB,OAAA;UAAGmB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAC;QAG/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxB,OAAA,CAACJ,IAAI;UACH6B,EAAE,EAAC,SAAS;UACZN,SAAS,EAAC,sKAAsK;UAAAC,QAAA,EACjL;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DpB,OAAA;QAAImB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChGxB,OAAA;QAAKmB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDnB,mBAAmB,CAACyB,GAAG,CAAEC,QAAQ,iBAChC3B,OAAA,CAACJ,IAAI;UAEH6B,EAAE,EAAE,oBAAoBG,kBAAkB,CAACD,QAAQ,CAACzB,IAAI,CAAC,EAAG;UAC5DiB,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBAEpEpB,OAAA;YAAMmB,SAAS,EAAC,gEAAgE;YAAAC,QAAA,EAC7EO,QAAQ,CAACxB;UAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPxB,OAAA;YAAImB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9CO,QAAQ,CAACzB;UAAI;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA,GATAG,QAAQ,CAACzB,IAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUd,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DpB,OAAA;QAAImB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC7DhB,OAAO,gBACNR,OAAA;QAAKmB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpB,OAAA;UAAKmB,SAAS,EAAC;QAA4F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClHxB,OAAA;UAAGmB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,GACJd,KAAK,gBACPV,OAAA;QAAKmB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCpB,OAAA;UAAGmB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEV;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,gBAENxB,OAAA;QAAKmB,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEd,gBAAgB,CAACoB,GAAG,CAAEG,OAAO,iBAC5B7B,OAAA,CAACH,WAAW;UAAmBgC,OAAO,EAAEA;QAAQ,GAA9BA,OAAO,CAACC,GAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAvFID,QAAkB;AAAA2B,EAAA,GAAlB3B,QAAkB;AAyFxB,eAAeA,QAAQ;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}