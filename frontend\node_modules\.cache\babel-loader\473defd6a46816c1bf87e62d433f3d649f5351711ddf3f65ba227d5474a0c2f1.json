{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-center items-center h-64\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = () => {\n  return (\n    <div className=\"flex justify-center items-center h-64\">\n      <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400\"></div>\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAAKE,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eACpDH,OAAA;MAAKE,SAAS,EAAC;IAAqF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxG,CAAC;AAEV,CAAC;AAACC,EAAA,GANIP,cAAc;AAQpB,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}