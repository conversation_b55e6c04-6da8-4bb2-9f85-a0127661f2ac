{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\ProductCard.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { formatPriceIndian } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = (highestPrice - lowestPrice) / highestPrice * 100;\n  return /*#__PURE__*/_jsxDEV(Link, {\n    to: `/product/${product._id}`,\n    className: \"block group\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card group-hover:shadow-2xl transition-shadow duration-300 rounded-2xl bg-white/80 backdrop-blur-lg border border-gray-100 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: typeof product.images[0] === 'string' ? product.images[0] : product.images[0].url,\n          alt: typeof product.images[0] === 'string' ? product.name : product.images[0].alt || product.name,\n          className: \"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 line-clamp-2\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n            className: \"h-5 w-5 text-yellow-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: [product.rating.toFixed(1), \" (\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-baseline justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: formatPriceIndian(lowestPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500 ml-1\",\n              children: [\"from \", product.prices.length, \" stores\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), savings >= 15 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\",\n          children: \"Best Deal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), savings >= 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 mt-2\",\n          children: [\"Save up to \", savings.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200 rounded-full h-2 mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\",\n            style: {\n              width: `${100 - savings}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Link", "StarIcon", "formatPriceIndian", "jsxDEV", "_jsxDEV", "ProductCard", "product", "lowestPrice", "Math", "min", "prices", "map", "p", "price", "highestPrice", "max", "savings", "to", "_id", "className", "children", "src", "images", "url", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rating", "toFixed", "reviews", "length", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { Product } from '../types';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const lowestPrice = Math.min(...product.prices.map((p) => p.price));\n  const highestPrice = Math.max(...product.prices.map((p) => p.price));\n  const savings = ((highestPrice - lowestPrice) / highestPrice) * 100;\n\n  return (\n    <Link to={`/product/${product._id}`} className=\"block group\">\n      <div className=\"card group-hover:shadow-2xl transition-shadow duration-300 rounded-2xl bg-white/80 backdrop-blur-lg border border-gray-100 relative overflow-hidden\">\n        <div className=\"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center\">\n          <img\n            src={typeof product.images[0] === 'string'\n              ? product.images[0] as string\n              : (product.images[0] as { url: string; alt?: string; isPrimary: boolean }).url}\n            alt={typeof product.images[0] === 'string'\n              ? product.name\n              : (product.images[0] as { url: string; alt?: string; isPrimary: boolean }).alt || product.name}\n            className=\"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n          />\n        </div>\n\n        <div className=\"mt-4 space-y-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 line-clamp-2\">\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center gap-1\">\n            <StarIcon className=\"h-5 w-5 text-yellow-400\" />\n            <span className=\"text-sm text-gray-600\">\n              {product.rating.toFixed(1)} ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          <div className=\"flex items-baseline justify-between\">\n            <div>\n              <span className=\"text-2xl font-bold text-gray-900\">\n                {formatPriceIndian(lowestPrice)}\n              </span>\n              <span className=\"text-sm text-gray-500 ml-1\">\n                from {product.prices.length} stores\n              </span>\n            </div>\n          </div>\n\n          {savings >= 15 && (\n            <div className=\"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\">\n              Best Deal\n            </div>\n          )}\n          {savings >= 5 && (\n            <div className=\"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 mt-2\">\n              Save up to {savings.toFixed(0)}%\n            </div>\n          )}\n          {/* Price Comparison Bar */}\n          <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n            <div className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\" style={{ width: `${100 - savings}%` }}></div>\n          </div>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,2BAA2B;AAEpD,SAASC,iBAAiB,QAAkC,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhF,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC/D,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACnE,MAAMC,YAAY,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAGT,OAAO,CAACI,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMG,OAAO,GAAI,CAACF,YAAY,GAAGP,WAAW,IAAIO,YAAY,GAAI,GAAG;EAEnE,oBACEV,OAAA,CAACJ,IAAI;IAACiB,EAAE,EAAE,YAAYX,OAAO,CAACY,GAAG,EAAG;IAACC,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1DhB,OAAA;MAAKe,SAAS,EAAC,qJAAqJ;MAAAC,QAAA,gBAClKhB,OAAA;QAAKe,SAAS,EAAC,mIAAmI;QAAAC,QAAA,eAChJhB,OAAA;UACEiB,GAAG,EAAE,OAAOf,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,GACtChB,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,GAChBhB,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAuDC,GAAI;UACjFC,GAAG,EAAE,OAAOlB,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,GACtChB,OAAO,CAACmB,IAAI,GACXnB,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAuDE,GAAG,IAAIlB,OAAO,CAACmB,IAAK;UACjGN,SAAS,EAAC;QAAiH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAIe,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAC3Dd,OAAO,CAACmB;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAELzB,OAAA;UAAKe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtChB,OAAA,CAACH,QAAQ;YAACkB,SAAS,EAAC;UAAyB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDzB,OAAA;YAAMe,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCd,OAAO,CAACwB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACzB,OAAO,CAAC0B,OAAO,CAACC,MAAM,EAAC,WACvD;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENzB,OAAA;UAAKe,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDhB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cAAMe,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC/ClB,iBAAiB,CAACK,WAAW;YAAC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACPzB,OAAA;cAAMe,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,OACtC,EAACd,OAAO,CAACI,MAAM,CAACuB,MAAM,EAAC,SAC9B;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELb,OAAO,IAAI,EAAE,iBACZZ,OAAA;UAAKe,SAAS,EAAC,sJAAsJ;UAAAC,QAAA,EAAC;QAEtK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACAb,OAAO,IAAI,CAAC,iBACXZ,OAAA;UAAKe,SAAS,EAAC,0GAA0G;UAAAC,QAAA,GAAC,aAC7G,EAACJ,OAAO,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAEDzB,OAAA;UAAKe,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDhB,OAAA;YAAKe,SAAS,EAAC,8DAA8D;YAACe,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAG,GAAG,GAAGnB,OAAO;YAAI;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACO,EAAA,GA7DI/B,WAAuC;AA+D7C,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}