{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\TodaysDealsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { productService } from '../services/productService';\nimport ProductCard from '../components/ProductCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TodaysDealsPage = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchDeals = async () => {\n      try {\n        setLoading(true);\n        const data = await productService.getTodaysDeals();\n        setProducts(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load deals');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDeals();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500 text-center\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"Today's Deals\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-center text-gray-500\",\n      children: \"No deals available at the moment.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        product: product\n      }, product._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(TodaysDealsPage, \"3+N/VFIgZOBgubN9oS5aTzm2qqY=\");\n_c = TodaysDealsPage;\nexport default TodaysDealsPage;\nvar _c;\n$RefreshReg$(_c, \"TodaysDealsPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "productService", "ProductCard", "LoadingSpinner", "jsxDEV", "_jsxDEV", "TodaysDealsPage", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchDeals", "data", "getTodaysDeals", "err", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "length", "map", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/TodaysDealsPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Product } from '../types';\nimport { productService } from '../services/productService';\nimport ProductCard from '../components/ProductCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst TodaysDealsPage = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDeals = async () => {\n      try {\n        setLoading(true);\n        const data = await productService.getTodaysDeals();\n        setProducts(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load deals');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDeals();\n  }, []);\n\n  if (loading) return <LoadingSpinner />;\n  if (error) return <div className=\"text-red-500 text-center\">{error}</div>;\n\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold mb-8\">Today's Deals</h1>\n      {products.length === 0 ? (\n        <p className=\"text-center text-gray-500\">No deals available at the moment.</p>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <ProductCard key={product._id} product={product} />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TodaysDealsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAElD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAgB,IAAI,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,MAAMe,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,IAAI,GAAG,MAAMd,cAAc,CAACe,cAAc,CAAC,CAAC;QAClDP,WAAW,CAACM,IAAI,CAAC;QACjBF,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZJ,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE,oBAAOL,OAAA,CAACF,cAAc;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtC,IAAIT,KAAK,EAAE,oBAAOP,OAAA;IAAKiB,SAAS,EAAC,0BAA0B;IAAAC,QAAA,EAAEX;EAAK;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAEzE,oBACEhB,OAAA;IAAAkB,QAAA,gBACElB,OAAA;MAAIiB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAa;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACzDb,QAAQ,CAACgB,MAAM,KAAK,CAAC,gBACpBnB,OAAA;MAAGiB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAiC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAE9EhB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFf,QAAQ,CAACiB,GAAG,CAAEC,OAAO,iBACpBrB,OAAA,CAACH,WAAW;QAAmBwB,OAAO,EAAEA;MAAQ,GAA9BA,OAAO,CAACC,GAAG;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACd,EAAA,CAvCID,eAAe;AAAAsB,EAAA,GAAftB,eAAe;AAyCrB,eAAeA,eAAe;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}