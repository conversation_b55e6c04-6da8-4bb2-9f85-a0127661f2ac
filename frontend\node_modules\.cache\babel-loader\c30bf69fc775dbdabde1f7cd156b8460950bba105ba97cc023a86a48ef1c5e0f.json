{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst categories = ['Electronics', 'Fashion', 'Home & Garden', 'Sports'];\nconst Navbar = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const navigate = useNavigate();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-xl font-bold text-blue-600\",\n          children: \"PriceCompare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex space-x-4\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/category/${encodeURIComponent(category)}`,\n            className: \"text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\",\n            children: category\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"flex-1 max-w-lg mx-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              placeholder: \"Search products...\",\n              className: \"w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/deals\",\n          className: \"bg-red-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-600\",\n          children: \"Today's Deals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden overflow-x-auto whitespace-nowrap py-2\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/category/${encodeURIComponent(category)}`,\n          className: \"inline-block text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium\",\n          children: category\n        }, category, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"Apc5g47SI7DyA0xqHgUxjkyfYYE=\", false, function () {\n  return [useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "categories", "<PERSON><PERSON><PERSON>", "_s", "searchQuery", "setSearch<PERSON>uery", "navigate", "handleSearch", "e", "preventDefault", "trim", "encodeURIComponent", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "category", "onSubmit", "type", "value", "onChange", "target", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst categories = [\n  'Electronics',\n  'Fashion',\n  'Home & Garden',\n  'Sports'\n];\n\nconst Navbar = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const navigate = useNavigate();\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link to=\"/\" className=\"text-xl font-bold text-blue-600\">\n            PriceCompare\n          </Link>\n\n          {/* Categories */}\n          <div className=\"hidden md:flex space-x-4\">\n            {categories.map((category) => (\n              <Link\n                key={category}\n                to={`/category/${encodeURIComponent(category)}`}\n                className=\"text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {category}\n              </Link>\n            ))}\n          </div>\n\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"flex-1 max-w-lg mx-4\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                placeholder=\"Search products...\"\n                className=\"w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n              <button\n                type=\"submit\"\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500\"\n              >\n                🔍\n              </button>\n            </div>\n          </form>\n\n          {/* Today's Deals */}\n          <Link\n            to=\"/deals\"\n            className=\"bg-red-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-600\"\n          >\n            Today's Deals\n          </Link>\n        </div>\n\n        {/* Mobile Categories */}\n        <div className=\"md:hidden overflow-x-auto whitespace-nowrap py-2\">\n          {categories.map((category) => (\n            <Link\n              key={category}\n              to={`/category/${encodeURIComponent(category)}`}\n              className=\"inline-block text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium\"\n            >\n              {category}\n            </Link>\n          ))}\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,UAAU,GAAG,CACjB,aAAa,EACb,SAAS,EACT,eAAe,EACf,QAAQ,CACT;AAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMU,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIL,WAAW,CAACM,IAAI,CAAC,CAAC,EAAE;MACtBJ,QAAQ,CAAC,aAAaK,kBAAkB,CAACP,WAAW,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACjE;EACF,CAAC;EAED,oBACEV,OAAA;IAAKY,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCb,OAAA;MAAKY,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCb,OAAA;QAAKY,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDb,OAAA,CAACH,IAAI;UAACiB,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGPlB,OAAA;UAAKY,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCZ,UAAU,CAACkB,GAAG,CAAEC,QAAQ,iBACvBpB,OAAA,CAACH,IAAI;YAEHiB,EAAE,EAAE,aAAaH,kBAAkB,CAACS,QAAQ,CAAC,EAAG;YAChDR,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAErFO;UAAQ,GAJJA,QAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKT,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlB,OAAA;UAAMqB,QAAQ,EAAEd,YAAa;UAACK,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eAC5Db,OAAA;YAAKY,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBb,OAAA;cACEsB,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnB,WAAY;cACnBoB,QAAQ,EAAGhB,CAAC,IAAKH,cAAc,CAACG,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;cAChDG,WAAW,EAAC,oBAAoB;cAChCd,SAAS,EAAC;YAAwF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACFlB,OAAA;cACEsB,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPlB,OAAA,CAACH,IAAI;UACHiB,EAAE,EAAC,QAAQ;UACXF,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,kDAAkD;QAAAC,QAAA,EAC9DZ,UAAU,CAACkB,GAAG,CAAEC,QAAQ,iBACvBpB,OAAA,CAACH,IAAI;UAEHiB,EAAE,EAAE,aAAaH,kBAAkB,CAACS,QAAQ,CAAC,EAAG;UAChDR,SAAS,EAAC,8EAA8E;UAAAC,QAAA,EAEvFO;QAAQ,GAJJA,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CA5EID,MAAM;EAAA,QAEOJ,WAAW;AAAA;AAAA6B,EAAA,GAFxBzB,MAAM;AA8EZ,eAAeA,MAAM;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}