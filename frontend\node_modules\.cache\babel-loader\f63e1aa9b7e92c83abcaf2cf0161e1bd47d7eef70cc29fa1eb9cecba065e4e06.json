{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { orderService } from '../services/orderService';\nimport { wishlistService } from '../services/wishlistService';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n  const [stats, setStats] = useState({\n    totalOrders: 0,\n    totalSpent: 0,\n    wishlistCount: 0,\n    reviewsCount: 0\n  });\n  const [statsLoading, setStatsLoading] = useState(true);\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n\n  // Fetch user statistics\n  useEffect(() => {\n    const fetchStats = async () => {\n      if (!isAuthenticated) {\n        setStatsLoading(false);\n        return;\n      }\n      try {\n        setStatsLoading(true);\n\n        // Fetch data from multiple sources\n        const [orders, wishlist] = await Promise.all([orderService.getUserOrders().catch(() => []), wishlistService.getWishlist().catch(() => [])]);\n\n        // Calculate statistics\n        const totalOrders = orders.length;\n        const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n        const wishlistCount = wishlist.length;\n\n        // Count reviews (for now, we'll simulate this)\n        // In a real app, you'd have a reviews service\n        const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n        setStats({\n          totalOrders,\n          totalSpent,\n          wishlistCount,\n          reviewsCount\n        });\n      } catch (error) {\n        console.error('Error fetching user statistics:', error);\n        // Keep default values on error\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n    fetchStats();\n  }, [isAuthenticated]);\n\n  // Function to refresh statistics (can be called from other components)\n  const refreshStats = async () => {\n    if (!isAuthenticated) return;\n    try {\n      const [orders, wishlist] = await Promise.all([orderService.getUserOrders().catch(() => []), wishlistService.getWishlist().catch(() => [])]);\n      const totalOrders = orders.length;\n      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n      const wishlistCount = wishlist.length;\n      const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n      setStats({\n        totalOrders,\n        totalSpent,\n        wishlistCount,\n        reviewsCount\n      });\n    } catch (error) {\n      console.error('Error refreshing statistics:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSave = async () => {\n    try {\n      // Update profile using the API service\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Please sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: \"You need to be signed in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: \"My Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), !isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsEditing(true),\n              className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(PencilIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), \"Edit Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2 flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-12 w-12 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-400\",\n                  children: [\"Member since \", new Date().getFullYear()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), \"Full Name\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.name || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(EnvelopeIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), \"Email Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.email || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), \"Phone Number\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.phone || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                  className: \"h-4 w-4 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), \"Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-gray-100 py-2\",\n                children: formData.address || 'Not provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 dark:border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\",\n            children: \"Account Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), statsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center py-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Total Spent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Wishlist Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.wishlistCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Reviews Written\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                children: stats.reviewsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"qK4ZUhzjm8OlZMcSTm8skg6zkYI=\", false, function () {\n  return [useAuth];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "userService", "orderService", "wishlistService", "UserIcon", "EnvelopeIcon", "PhoneIcon", "MapPinIcon", "PencilIcon", "formatPriceIndian", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "user", "isAuthenticated", "isEditing", "setIsEditing", "formData", "setFormData", "name", "email", "phone", "address", "stats", "setStats", "totalOrders", "totalSpent", "wishlistCount", "reviewsCount", "statsLoading", "setStatsLoading", "fetchStats", "orders", "wishlist", "Promise", "all", "getUserOrders", "catch", "getWishlist", "length", "reduce", "sum", "order", "total", "filter", "status", "error", "console", "refreshStats", "handleInputChange", "e", "value", "target", "prev", "handleSave", "updateProfile", "alert", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "Date", "getFullYear", "type", "onChange", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProfilePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { userService } from '../services/userService';\nimport { orderService } from '../services/orderService';\nimport { wishlistService } from '../services/wishlistService';\nimport { UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, PencilIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\n\nconst ProfilePage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  });\n  const [stats, setStats] = useState({\n    totalOrders: 0,\n    totalSpent: 0,\n    wishlistCount: 0,\n    reviewsCount: 0\n  });\n  const [statsLoading, setStatsLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n  }, [user]);\n\n  // Fetch user statistics\n  useEffect(() => {\n    const fetchStats = async () => {\n      if (!isAuthenticated) {\n        setStatsLoading(false);\n        return;\n      }\n\n      try {\n        setStatsLoading(true);\n\n        // Fetch data from multiple sources\n        const [orders, wishlist] = await Promise.all([\n          orderService.getUserOrders().catch(() => []),\n          wishlistService.getWishlist().catch(() => [])\n        ]);\n\n        // Calculate statistics\n        const totalOrders = orders.length;\n        const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n        const wishlistCount = wishlist.length;\n\n        // Count reviews (for now, we'll simulate this)\n        // In a real app, you'd have a reviews service\n        const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n\n        setStats({\n          totalOrders,\n          totalSpent,\n          wishlistCount,\n          reviewsCount\n        });\n      } catch (error) {\n        console.error('Error fetching user statistics:', error);\n        // Keep default values on error\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, [isAuthenticated]);\n\n  // Function to refresh statistics (can be called from other components)\n  const refreshStats = async () => {\n    if (!isAuthenticated) return;\n\n    try {\n      const [orders, wishlist] = await Promise.all([\n        orderService.getUserOrders().catch(() => []),\n        wishlistService.getWishlist().catch(() => [])\n      ]);\n\n      const totalOrders = orders.length;\n      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n      const wishlistCount = wishlist.length;\n      const reviewsCount = orders.filter(order => order.status === 'delivered').length;\n\n      setStats({\n        totalOrders,\n        totalSpent,\n        wishlistCount,\n        reviewsCount\n      });\n    } catch (error) {\n      console.error('Error refreshing statistics:', error);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSave = async () => {\n    try {\n      // Update profile using the API service\n      await userService.updateProfile(formData);\n      setIsEditing(false);\n      // Show success message\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile. Please try again.');\n    }\n  };\n\n  const handleCancel = () => {\n    if (user) {\n      setFormData({\n        name: user.name || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        address: user.address || ''\n      });\n    }\n    setIsEditing(false);\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Please sign in</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            You need to be signed in to view your profile.\n          </p>\n          <div className=\"mt-6\">\n            <a\n              href=\"/login\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Sign in\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n          {/* Header */}\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">My Profile</h1>\n              {!isEditing && (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <PencilIcon className=\"h-4 w-4 mr-2\" />\n                  Edit Profile\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Profile Content */}\n          <div className=\"px-6 py-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Profile Picture */}\n              <div className=\"md:col-span-2 flex items-center space-x-6\">\n                <div className=\"h-24 w-24 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                  <UserIcon className=\"h-12 w-12 text-gray-500 dark:text-gray-400\" />\n                </div>\n                <div>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                    {user?.name || 'User'}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Member since {new Date().getFullYear()}</p>\n                </div>\n              </div>\n\n              {/* Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <UserIcon className=\"h-4 w-4 inline mr-2\" />\n                  Full Name\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.name || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Email */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <EnvelopeIcon className=\"h-4 w-4 inline mr-2\" />\n                  Email Address\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.email || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Phone */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <PhoneIcon className=\"h-4 w-4 inline mr-2\" />\n                  Phone Number\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.phone || 'Not provided'}</p>\n                )}\n              </div>\n\n              {/* Address */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 inline mr-2\" />\n                  Address\n                </label>\n                {isEditing ? (\n                  <textarea\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-gray-100 py-2\">{formData.address || 'Not provided'}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {isEditing && (\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  Save Changes\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Account Statistics */}\n          <div className=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Account Statistics</h3>\n            {statsLoading ? (\n              <div className=\"flex justify-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"></div>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Orders</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.totalOrders}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Spent</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                    {stats.totalSpent > 0 ? formatPriceIndian(stats.totalSpent) : '₹0'}\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Wishlist Items</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.wishlistCount}</p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Reviews Written</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{stats.reviewsCount}</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,QAAQ,6BAA6B;AACvG,SAASC,iBAAiB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC;IACjC4B,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAET,IAAI,CAACS,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACT,IAAI,CAAC,CAAC;;EAEV;EACAf,SAAS,CAAC,MAAM;IACd,MAAMiC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACjB,eAAe,EAAE;QACpBgB,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,IAAI;QACFA,eAAe,CAAC,IAAI,CAAC;;QAErB;QACA,MAAM,CAACE,MAAM,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3ClC,YAAY,CAACmC,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAC5CnC,eAAe,CAACoC,WAAW,CAAC,CAAC,CAACD,KAAK,CAAC,MAAM,EAAE,CAAC,CAC9C,CAAC;;QAEF;QACA,MAAMZ,WAAW,GAAGO,MAAM,CAACO,MAAM;QACjC,MAAMb,UAAU,GAAGM,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,KAAK,EAAE,CAAC,CAAC;QACtE,MAAMhB,aAAa,GAAGM,QAAQ,CAACM,MAAM;;QAErC;QACA;QACA,MAAMX,YAAY,GAAGI,MAAM,CAACY,MAAM,CAACF,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,WAAW,CAAC,CAACN,MAAM;QAEhFf,QAAQ,CAAC;UACPC,WAAW;UACXC,UAAU;UACVC,aAAa;UACbC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACF,CAAC,SAAS;QACRhB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACjB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAClC,eAAe,EAAE;IAEtB,IAAI;MACF,MAAM,CAACkB,MAAM,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3ClC,YAAY,CAACmC,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAC5CnC,eAAe,CAACoC,WAAW,CAAC,CAAC,CAACD,KAAK,CAAC,MAAM,EAAE,CAAC,CAC9C,CAAC;MAEF,MAAMZ,WAAW,GAAGO,MAAM,CAACO,MAAM;MACjC,MAAMb,UAAU,GAAGM,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,KAAK,EAAE,CAAC,CAAC;MACtE,MAAMhB,aAAa,GAAGM,QAAQ,CAACM,MAAM;MACrC,MAAMX,YAAY,GAAGI,MAAM,CAACY,MAAM,CAACF,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,WAAW,CAAC,CAACN,MAAM;MAEhFf,QAAQ,CAAC;QACPC,WAAW;QACXC,UAAU;QACVC,aAAa;QACbC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAA4D,IAAK;IAC1F,MAAM;MAAE/B,IAAI;MAAEgC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChClC,WAAW,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAClC,IAAI,GAAGgC;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAMtD,WAAW,CAACuD,aAAa,CAACtC,QAAQ,CAAC;MACzCD,YAAY,CAAC,KAAK,CAAC;MACnB;MACAwC,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CU,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5C,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;QACvBC,OAAO,EAAET,IAAI,CAACS,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACF,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKgD,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxFjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA,CAACP,QAAQ;UAACuD,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDrD,OAAA;UAAIgD,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7FrD,OAAA;UAAGgD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrD,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjD,OAAA;YACEsD,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,EAC3J;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKgD,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzEjD,OAAA;MAAKgD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DjD,OAAA;QAAKgD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DjD,OAAA;UAAKgD,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtEjD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjD,OAAA;cAAIgD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClF,CAAChD,SAAS,iBACTL,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC,IAAI,CAAE;cAClC0C,SAAS,EAAC,8OAA8O;cAAAC,QAAA,gBAExPjD,OAAA,CAACH,UAAU;gBAACmD,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrD,OAAA;UAAKgD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjD,OAAA;YAAKgD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDjD,OAAA;cAAKgD,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDjD,OAAA;gBAAKgD,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,eACnGjD,OAAA,CAACP,QAAQ;kBAACuD,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNrD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIgD,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,KAAI;gBAAM;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACLrD,OAAA;kBAAGgD,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,eAAa,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFjD,OAAA,CAACP,QAAQ;kBAACuD,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPhD,SAAS,gBACRL,OAAA;gBACE0D,IAAI,EAAC,MAAM;gBACXjD,IAAI,EAAC,MAAM;gBACXgC,KAAK,EAAElC,QAAQ,CAACE,IAAK;gBACrBkD,QAAQ,EAAEpB,iBAAkB;gBAC5BS,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFrD,OAAA;gBAAGgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE1C,QAAQ,CAACE,IAAI,IAAI;cAAc;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFjD,OAAA,CAACN,YAAY;kBAACsD,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPhD,SAAS,gBACRL,OAAA;gBACE0D,IAAI,EAAC,OAAO;gBACZjD,IAAI,EAAC,OAAO;gBACZgC,KAAK,EAAElC,QAAQ,CAACG,KAAM;gBACtBiD,QAAQ,EAAEpB,iBAAkB;gBAC5BS,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFrD,OAAA;gBAAGgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE1C,QAAQ,CAACG,KAAK,IAAI;cAAc;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFjD,OAAA,CAACL,SAAS;kBAACqD,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPhD,SAAS,gBACRL,OAAA;gBACE0D,IAAI,EAAC,KAAK;gBACVjD,IAAI,EAAC,OAAO;gBACZgC,KAAK,EAAElC,QAAQ,CAACI,KAAM;gBACtBgD,QAAQ,EAAEpB,iBAAkB;gBAC5BS,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFrD,OAAA;gBAAGgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE1C,QAAQ,CAACI,KAAK,IAAI;cAAc;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFjD,OAAA,CAACJ,UAAU;kBAACoD,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPhD,SAAS,gBACRL,OAAA;gBACES,IAAI,EAAC,SAAS;gBACdgC,KAAK,EAAElC,QAAQ,CAACK,OAAQ;gBACxB+C,QAAQ,EAAEpB,iBAAkB;gBAC5BqB,IAAI,EAAE,CAAE;gBACRZ,SAAS,EAAC;cAA2M;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC,gBAEFrD,OAAA;gBAAGgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE1C,QAAQ,CAACK,OAAO,IAAI;cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC7F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLhD,SAAS,iBACRL,OAAA;YAAKgD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CjD,OAAA;cACEuD,OAAO,EAAER,YAAa;cACtBC,SAAS,EAAC,qNAAqN;cAAAC,QAAA,EAChO;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrD,OAAA;cACEuD,OAAO,EAAEX,UAAW;cACpBI,SAAS,EAAC,yIAAyI;cAAAC,QAAA,EACpJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrD,OAAA;UAAKgD,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEjD,OAAA;YAAIgD,SAAS,EAAC,2DAA2D;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChGlC,YAAY,gBACXnB,OAAA;YAAKgD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvCjD,OAAA;cAAKgD,SAAS,EAAC;YAAmF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,gBAENrD,OAAA;YAAKgD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDjD,OAAA;cAAKgD,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDjD,OAAA;gBAAGgD,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxErD,OAAA;gBAAGgD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEpC,KAAK,CAACE;cAAW;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDjD,OAAA;gBAAGgD,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvErD,OAAA;gBAAGgD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAC/DpC,KAAK,CAACG,UAAU,GAAG,CAAC,GAAGlB,iBAAiB,CAACe,KAAK,CAACG,UAAU,CAAC,GAAG;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDjD,OAAA;gBAAGgD,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1ErD,OAAA;gBAAGgD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEpC,KAAK,CAACI;cAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDjD,OAAA;gBAAGgD,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3ErD,OAAA;gBAAGgD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEpC,KAAK,CAACK;cAAY;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CA3TID,WAAqB;EAAA,QACSZ,OAAO;AAAA;AAAAwE,EAAA,GADrC5D,WAAqB;AA6T3B,eAAeA,WAAW;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}