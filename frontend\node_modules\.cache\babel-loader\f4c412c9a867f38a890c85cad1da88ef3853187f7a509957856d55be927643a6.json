{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\ProductCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  _s();\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = (highestPrice - lowestPrice) / highestPrice * 100;\n\n  // Check if product is in wishlist when component mounts\n  useEffect(() => {\n    const checkWishlistStatus = async () => {\n      if (isAuthenticated) {\n        try {\n          const inWishlist = await wishlistService.checkWishlist(product._id);\n          setIsInWishlist(inWishlist);\n        } catch (error) {\n          console.error('Error checking wishlist status:', error);\n        }\n      }\n    };\n    checkWishlistStatus();\n  }, [product._id, isAuthenticated]);\n  const handleWishlistToggle = async e => {\n    e.preventDefault(); // Prevent navigation to product detail page\n    e.stopPropagation();\n    if (!isAuthenticated) {\n      // Redirect to login or show login modal\n      alert('Please login to add items to wishlist');\n      return;\n    }\n    setIsLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(product._id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(product._id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Link, {\n    to: `/product/${product._id}`,\n    className: \"block group\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md group-hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: getImageUrl(product.images[0]),\n          alt: getImageAlt(product.images[0], product.name),\n          className: \"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 dark:text-gray-100 line-clamp-2\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n            className: \"h-5 w-5 text-yellow-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 dark:text-gray-400\",\n            children: [product.rating.toFixed(1), \" (\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-baseline justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n              children: formatPriceIndian(lowestPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400 ml-1\",\n              children: [\"from \", product.prices.length, \" stores\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), savings >= 15 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\",\n          children: \"Best Deal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this), savings >= 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center rounded-full bg-green-100 dark:bg-green-900 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-200 mt-2\",\n          children: [\"Save up to \", savings.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\",\n            style: {\n              width: `${100 - savings}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductCard, \"DDza9/EG/8dsIVwoFOVo4g947g4=\", false, function () {\n  return [useAuth];\n});\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "StarIcon", "formatPriceIndian", "getImageUrl", "getImageAlt", "wishlistService", "useAuth", "jsxDEV", "_jsxDEV", "ProductCard", "product", "_s", "isInWishlist", "setIsInWishlist", "isLoading", "setIsLoading", "isAuthenticated", "lowestPrice", "Math", "min", "prices", "map", "p", "price", "highestPrice", "max", "savings", "checkWishlistStatus", "inWishlist", "checkWishlist", "_id", "error", "console", "handleWishlistToggle", "e", "preventDefault", "stopPropagation", "alert", "removeFromWishlist", "addToWishlist", "to", "className", "children", "src", "images", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rating", "toFixed", "reviews", "length", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { Product } from '../types';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  const lowestPrice = Math.min(...product.prices.map((p) => p.price));\n  const highestPrice = Math.max(...product.prices.map((p) => p.price));\n  const savings = ((highestPrice - lowestPrice) / highestPrice) * 100;\n\n  // Check if product is in wishlist when component mounts\n  useEffect(() => {\n    const checkWishlistStatus = async () => {\n      if (isAuthenticated) {\n        try {\n          const inWishlist = await wishlistService.checkWishlist(product._id);\n          setIsInWishlist(inWishlist);\n        } catch (error) {\n          console.error('Error checking wishlist status:', error);\n        }\n      }\n    };\n\n    checkWishlistStatus();\n  }, [product._id, isAuthenticated]);\n\n  const handleWishlistToggle = async (e: React.MouseEvent) => {\n    e.preventDefault(); // Prevent navigation to product detail page\n    e.stopPropagation();\n\n    if (!isAuthenticated) {\n      // Redirect to login or show login modal\n      alert('Please login to add items to wishlist');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(product._id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(product._id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Link to={`/product/${product._id}`} className=\"block group\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md group-hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 relative overflow-hidden\">\n        <div className=\"aspect-square w-full overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 flex items-center justify-center\">\n          <img\n            src={getImageUrl(product.images[0])}\n            alt={getImageAlt(product.images[0], product.name)}\n            className=\"h-full w-full object-cover object-center group-hover:scale-110 transition-transform duration-300 drop-shadow-xl\"\n          />\n        </div>\n\n        <div className=\"mt-4 space-y-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 line-clamp-2\">\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center gap-1\">\n            <StarIcon className=\"h-5 w-5 text-yellow-400\" />\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {product.rating.toFixed(1)} ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          <div className=\"flex items-baseline justify-between\">\n            <div>\n              <span className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                {formatPriceIndian(lowestPrice)}\n              </span>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400 ml-1\">\n                from {product.prices.length} stores\n              </span>\n            </div>\n          </div>\n\n          {savings >= 15 && (\n            <div className=\"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse z-10\">\n              Best Deal\n            </div>\n          )}\n          {savings >= 5 && (\n            <div className=\"inline-flex items-center rounded-full bg-green-100 dark:bg-green-900 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-200 mt-2\">\n              Save up to {savings.toFixed(0)}%\n            </div>\n          )}\n          {/* Price Comparison Bar */}\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\">\n            <div className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full\" style={{ width: `${100 - savings}%` }}></div>\n          </div>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,2BAA2B;AAIpD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAC/E,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMlD,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEkB;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAErC,MAAMW,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGT,OAAO,CAACU,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACnE,MAAMC,YAAY,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAGf,OAAO,CAACU,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMG,OAAO,GAAI,CAACF,YAAY,GAAGP,WAAW,IAAIO,YAAY,GAAI,GAAG;;EAEnE;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM4B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAIX,eAAe,EAAE;QACnB,IAAI;UACF,MAAMY,UAAU,GAAG,MAAMvB,eAAe,CAACwB,aAAa,CAACnB,OAAO,CAACoB,GAAG,CAAC;UACnEjB,eAAe,CAACe,UAAU,CAAC;QAC7B,CAAC,CAAC,OAAOG,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;MACF;IACF,CAAC;IAEDJ,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACjB,OAAO,CAACoB,GAAG,EAAEd,eAAe,CAAC,CAAC;EAElC,MAAMiB,oBAAoB,GAAG,MAAOC,CAAmB,IAAK;IAC1DA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnB,IAAI,CAACpB,eAAe,EAAE;MACpB;MACAqB,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACF;IAEAtB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,IAAIH,YAAY,EAAE;QAChB,MAAMP,eAAe,CAACiC,kBAAkB,CAAC5B,OAAO,CAACoB,GAAG,CAAC;QACrDjB,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM;QACL,MAAMR,eAAe,CAACkC,aAAa,CAAC7B,OAAO,CAACoB,GAAG,CAAC;QAChDjB,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDM,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEP,OAAA,CAACR,IAAI;IAACwC,EAAE,EAAE,YAAY9B,OAAO,CAACoB,GAAG,EAAG;IAACW,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1DlC,OAAA;MAAKiC,SAAS,EAAC,6KAA6K;MAAAC,QAAA,gBAC1LlC,OAAA;QAAKiC,SAAS,EAAC,2KAA2K;QAAAC,QAAA,eACxLlC,OAAA;UACEmC,GAAG,EAAExC,WAAW,CAACO,OAAO,CAACkC,MAAM,CAAC,CAAC,CAAC,CAAE;UACpCC,GAAG,EAAEzC,WAAW,CAACM,OAAO,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAElC,OAAO,CAACoC,IAAI,CAAE;UAClDL,SAAS,EAAC;QAAiH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1C,OAAA;QAAKiC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlC,OAAA;UAAIiC,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAC9EhC,OAAO,CAACoC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAEL1C,OAAA;UAAKiC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtClC,OAAA,CAACP,QAAQ;YAACwC,SAAS,EAAC;UAAyB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD1C,OAAA;YAAMiC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GACvDhC,OAAO,CAACyC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC1C,OAAO,CAAC2C,OAAO,CAACC,MAAM,EAAC,WACvD;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEN1C,OAAA;UAAKiC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDlC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAMiC,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAClExC,iBAAiB,CAACe,WAAW;YAAC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACP1C,OAAA;cAAMiC,SAAS,EAAC,+CAA+C;cAAAC,QAAA,GAAC,OACzD,EAAChC,OAAO,CAACU,MAAM,CAACkC,MAAM,EAAC,SAC9B;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxB,OAAO,IAAI,EAAE,iBACZlB,OAAA;UAAKiC,SAAS,EAAC,sJAAsJ;UAAAC,QAAA,EAAC;QAEtK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACAxB,OAAO,IAAI,CAAC,iBACXlB,OAAA;UAAKiC,SAAS,EAAC,gJAAgJ;UAAAC,QAAA,GAAC,aACnJ,EAAChB,OAAO,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAED1C,OAAA;UAAKiC,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxElC,OAAA;YAAKiC,SAAS,EAAC,8DAA8D;YAACc,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAG,GAAG,GAAG9B,OAAO;YAAI;UAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACvC,EAAA,CAxGIF,WAAuC;EAAA,QAGfH,OAAO;AAAA;AAAAmD,EAAA,GAH/BhD,WAAuC;AA0G7C,eAAeA,WAAW;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}