{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const userService = {\n  // Get user profile\n  getProfile: async () => {\n    const response = await api.get('/users/profile');\n    return response.data;\n  },\n  // Update user profile\n  updateProfile: async userData => {\n    const response = await api.put('/users/profile', userData);\n    return response.data;\n  },\n  // Update password\n  updatePassword: async (currentPassword, newPassword) => {\n    const response = await api.put('/users/password', {\n      currentPassword,\n      newPassword\n    });\n    return response.data;\n  },\n  // Upload avatar\n  uploadAvatar: async file => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    const response = await api.post('/users/avatar', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data.avatarUrl;\n  },\n  // Delete account\n  deleteAccount: async () => {\n    const response = await api.delete('/users/profile');\n    return response.data;\n  },\n  // Get user statistics\n  getUserStats: async () => {\n    const response = await api.get('/users/stats');\n    return response.data;\n  },\n  // Update user preferences\n  updatePreferences: async preferences => {\n    const response = await api.put('/users/preferences', preferences);\n    return response.data;\n  },\n  // Get user addresses\n  getAddresses: async () => {\n    const response = await api.get('/users/addresses');\n    return response.data;\n  },\n  // Add user address\n  addAddress: async address => {\n    const response = await api.post('/users/addresses', address);\n    return response.data;\n  },\n  // Update user address\n  updateAddress: async (addressId, address) => {\n    const response = await api.put(`/users/addresses/${addressId}`, address);\n    return response.data;\n  },\n  // Delete user address\n  deleteAddress: async addressId => {\n    const response = await api.delete(`/users/addresses/${addressId}`);\n    return response.data;\n  },\n  // Get user notifications\n  getNotifications: async () => {\n    const response = await api.get('/users/notifications');\n    return response.data;\n  },\n  // Mark notification as read\n  markNotificationRead: async notificationId => {\n    const response = await api.put(`/users/notifications/${notificationId}/read`);\n    return response.data;\n  },\n  // Delete notification\n  deleteNotification: async notificationId => {\n    const response = await api.delete(`/users/notifications/${notificationId}`);\n    return response.data;\n  },\n  // Get user activity log\n  getActivityLog: async (page = 1, limit = 20) => {\n    const response = await api.get(`/users/activity?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n  // Export user data\n  exportUserData: async () => {\n    const response = await api.get('/users/export');\n    return response.data;\n  },\n  // Deactivate account\n  deactivateAccount: async () => {\n    const response = await api.put('/users/deactivate');\n    return response.data;\n  },\n  // Reactivate account\n  reactivateAccount: async () => {\n    const response = await api.put('/users/reactivate');\n    return response.data;\n  }\n};\nexport default userService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "userService", "getProfile", "response", "get", "data", "updateProfile", "userData", "put", "updatePassword", "currentPassword", "newPassword", "uploadAvatar", "file", "formData", "FormData", "append", "post", "avatarUrl", "deleteAccount", "delete", "getUserStats", "updatePreferences", "preferences", "getAddresses", "addAddress", "address", "updateAddress", "addressId", "deleteAddress", "getNotifications", "markNotificationRead", "notificationId", "deleteNotification", "getActivityLog", "page", "limit", "exportUserData", "deactivateAccount", "reactivateAccount"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/userService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport const userService = {\n  // Get user profile\n  getProfile: async () => {\n    const response = await api.get('/users/profile');\n    return response.data;\n  },\n\n  // Update user profile\n  updateProfile: async (userData) => {\n    const response = await api.put('/users/profile', userData);\n    return response.data;\n  },\n\n  // Update password\n  updatePassword: async (currentPassword, newPassword) => {\n    const response = await api.put('/users/password', {\n      currentPassword,\n      newPassword,\n    });\n    return response.data;\n  },\n\n  // Upload avatar\n  uploadAvatar: async (file) => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    \n    const response = await api.post('/users/avatar', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    \n    return response.data.avatarUrl;\n  },\n\n  // Delete account\n  deleteAccount: async () => {\n    const response = await api.delete('/users/profile');\n    return response.data;\n  },\n\n  // Get user statistics\n  getUserStats: async () => {\n    const response = await api.get('/users/stats');\n    return response.data;\n  },\n\n  // Update user preferences\n  updatePreferences: async (preferences) => {\n    const response = await api.put('/users/preferences', preferences);\n    return response.data;\n  },\n\n  // Get user addresses\n  getAddresses: async () => {\n    const response = await api.get('/users/addresses');\n    return response.data;\n  },\n\n  // Add user address\n  addAddress: async (address) => {\n    const response = await api.post('/users/addresses', address);\n    return response.data;\n  },\n\n  // Update user address\n  updateAddress: async (addressId, address) => {\n    const response = await api.put(`/users/addresses/${addressId}`, address);\n    return response.data;\n  },\n\n  // Delete user address\n  deleteAddress: async (addressId) => {\n    const response = await api.delete(`/users/addresses/${addressId}`);\n    return response.data;\n  },\n\n  // Get user notifications\n  getNotifications: async () => {\n    const response = await api.get('/users/notifications');\n    return response.data;\n  },\n\n  // Mark notification as read\n  markNotificationRead: async (notificationId) => {\n    const response = await api.put(`/users/notifications/${notificationId}/read`);\n    return response.data;\n  },\n\n  // Delete notification\n  deleteNotification: async (notificationId) => {\n    const response = await api.delete(`/users/notifications/${notificationId}`);\n    return response.data;\n  },\n\n  // Get user activity log\n  getActivityLog: async (page = 1, limit = 20) => {\n    const response = await api.get(`/users/activity?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // Export user data\n  exportUserData: async () => {\n    const response = await api.get('/users/export');\n    return response.data;\n  },\n\n  // Deactivate account\n  deactivateAccount: async () => {\n    const response = await api.put('/users/deactivate');\n    return response.data;\n  },\n\n  // Reactivate account\n  reactivateAccount: async () => {\n    const response = await api.put('/users/reactivate');\n    return response.data;\n  }\n};\n\nexport default userService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMK,WAAW,GAAG;EACzB;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,MAAMC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,gBAAgB,CAAC;IAChD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,aAAa,EAAE,MAAOC,QAAQ,IAAK;IACjC,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,gBAAgB,EAAED,QAAQ,CAAC;IAC1D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,cAAc,EAAE,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IACtD,MAAMR,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,iBAAiB,EAAE;MAChDE,eAAe;MACfC;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,YAAY,EAAE,MAAOC,IAAI,IAAK;IAC5B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;IAE/B,MAAMV,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,eAAe,EAAEH,QAAQ,EAAE;MACzDtB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,OAAOW,QAAQ,CAACE,IAAI,CAACa,SAAS;EAChC,CAAC;EAED;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,MAAMhB,QAAQ,GAAG,MAAMd,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,CAAC;IACnD,OAAOjB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMlB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,cAAc,CAAC;IAC9C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAiB,iBAAiB,EAAE,MAAOC,WAAW,IAAK;IACxC,MAAMpB,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,oBAAoB,EAAEe,WAAW,CAAC;IACjE,OAAOpB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAmB,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMrB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAoB,UAAU,EAAE,MAAOC,OAAO,IAAK;IAC7B,MAAMvB,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,kBAAkB,EAAES,OAAO,CAAC;IAC5D,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAsB,aAAa,EAAE,MAAAA,CAAOC,SAAS,EAAEF,OAAO,KAAK;IAC3C,MAAMvB,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,oBAAoBoB,SAAS,EAAE,EAAEF,OAAO,CAAC;IACxE,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAwB,aAAa,EAAE,MAAOD,SAAS,IAAK;IAClC,MAAMzB,QAAQ,GAAG,MAAMd,GAAG,CAAC+B,MAAM,CAAC,oBAAoBQ,SAAS,EAAE,CAAC;IAClE,OAAOzB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAyB,gBAAgB,EAAE,MAAAA,CAAA,KAAY;IAC5B,MAAM3B,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,sBAAsB,CAAC;IACtD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA0B,oBAAoB,EAAE,MAAOC,cAAc,IAAK;IAC9C,MAAM7B,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,wBAAwBwB,cAAc,OAAO,CAAC;IAC7E,OAAO7B,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA4B,kBAAkB,EAAE,MAAOD,cAAc,IAAK;IAC5C,MAAM7B,QAAQ,GAAG,MAAMd,GAAG,CAAC+B,MAAM,CAAC,wBAAwBY,cAAc,EAAE,CAAC;IAC3E,OAAO7B,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA6B,cAAc,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC9C,MAAMjC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,wBAAwB+B,IAAI,UAAUC,KAAK,EAAE,CAAC;IAC7E,OAAOjC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAMlC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAiC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAMnC,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAMpC,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAOL,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}