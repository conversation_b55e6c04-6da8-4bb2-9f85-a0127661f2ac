{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: ''\n  });\n  const {\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProduct();\n  }, [id, isAuthenticated]);\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n    if (!id) return;\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n  const handleReviewSubmit = async e => {\n    e.preventDefault();\n    if (!product || !id) return;\n    try {\n      await productService.addReview(id, {\n        rating: reviewForm.rating,\n        comment: reviewForm.comment\n      });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({\n        userName: '',\n        rating: 5,\n        comment: ''\n      });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-4 text-gray-600\",\n        children: \"Loading product...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-600\",\n        children: error || 'Product not found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full overflow-hidden rounded-lg bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: getImageUrl(product.images[selectedImage]),\n            alt: getImageAlt(product.images[selectedImage], product.name),\n            className: \"w-full h-[500px] object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 grid grid-cols-4 gap-2\",\n          children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedImage(index),\n            className: `relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${selectedImage === index ? 'ring-2 ring-primary-500' : 'hover:ring-2 hover:ring-gray-300'}`,\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getImageUrl(image),\n              alt: getImageAlt(image, `${product.name} ${index + 1}`),\n              className: \"h-full w-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n              className: `h-5 w-5 ${star <= product.rating ? 'text-yellow-400' : 'text-gray-200'}`\n            }, star, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-600\",\n            children: [\"(\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Price Comparison\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: product.prices.slice().sort((a, b) => a.price - b.price).map(price => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium\",\n                  children: price.storeName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: formatPriceIndian(price.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: price.productUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"btn-primary\",\n                children: \"Visit Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 21\n              }, this)]\n            }, price.storeName, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 whitespace-pre-line\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-6\",\n            children: \"Customer Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleReviewSubmit,\n            className: \"card mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: reviewForm.userName,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    userName: e.target.value\n                  }),\n                  required: true,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 flex items-center gap-1\",\n                  children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => setReviewForm({\n                      ...reviewForm,\n                      rating: star\n                    }),\n                    className: \"p-1 hover:scale-110 transition-transform\",\n                    children: star <= reviewForm.rating ? /*#__PURE__*/_jsxDEV(StarIcon, {\n                      className: \"h-6 w-6 text-yellow-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(StarOutlineIcon, {\n                      className: \"h-6 w-6 text-gray-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 27\n                    }, this)\n                  }, star, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: reviewForm.comment,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    comment: e.target.value\n                  }),\n                  required: true,\n                  rows: 4,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn-primary w-full\",\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: product.reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: review.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: new Date(review.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-1\",\n                children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: `h-4 w-4 ${star <= review.rating ? 'text-yellow-400' : 'text-gray-200'}`\n                }, star, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"hSJMJr3aSIv63Y4ozy0/IjKS/38=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "StarIcon", "StarOutlineIcon", "productService", "wishlistService", "useAuth", "formatPriceIndian", "getImageUrl", "getImageAlt", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "isInWishlist", "setIsInWishlist", "wishlistLoading", "setWishlistLoading", "reviewForm", "setReviewForm", "userName", "rating", "comment", "isAuthenticated", "fetchProduct", "data", "getProduct", "inWishlist", "checkWishlist", "wishlistError", "console", "err", "handleWishlistToggle", "alert", "removeFromWishlist", "addToWishlist", "handleReviewSubmit", "e", "preventDefault", "add<PERSON>eview", "updatedProduct", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "images", "alt", "name", "length", "map", "image", "index", "onClick", "star", "reviews", "prices", "slice", "sort", "a", "b", "price", "storeName", "href", "productUrl", "target", "rel", "description", "onSubmit", "type", "value", "onChange", "required", "rows", "review", "Date", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProductDetailPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon, HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { Product } from '../types';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\nconst ProductDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const [product, setProduct] = useState<Product | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: '',\n  });\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id, isAuthenticated]);\n\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n\n    if (!id) return;\n\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n\n  const handleReviewSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!product || !id) return;\n\n    try {\n      await productService.addReview(id, { rating: reviewForm.rating, comment: reviewForm.comment });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({ userName: '', rating: 5, comment: '' });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">Loading product...</p>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"text-center py-12\">\n        <p className=\"text-red-600\">{error || 'Product not found'}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Image Gallery */}\n        <div>\n          <div className=\"w-full overflow-hidden rounded-lg bg-gray-100\">\n            <img\n              src={getImageUrl(product.images[selectedImage])}\n              alt={getImageAlt(product.images[selectedImage], product.name)}\n              className=\"w-full h-[500px] object-contain\"\n            />\n          </div>\n          {product.images.length > 1 && (\n            <div className=\"mt-4 grid grid-cols-4 gap-2\">\n              {product.images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImage(index)}\n                  className={`relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${\n                    selectedImage === index\n                      ? 'ring-2 ring-primary-500'\n                      : 'hover:ring-2 hover:ring-gray-300'\n                  }`}\n                >\n                  <img\n                    src={getImageUrl(image)}\n                    alt={getImageAlt(image, `${product.name} ${index + 1}`)}\n                    className=\"h-full w-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{product.name}</h1>\n\n          {/* Rating */}\n          <div className=\"mt-4 flex items-center\">\n            <div className=\"flex items-center\">\n              {[1, 2, 3, 4, 5].map((star) => (\n                <StarIcon\n                  key={star}\n                  className={`h-5 w-5 ${\n                    star <= product.rating\n                      ? 'text-yellow-400'\n                      : 'text-gray-200'\n                  }`}\n                />\n              ))}\n            </div>\n            <span className=\"ml-2 text-gray-600\">\n              ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          {/* Price Comparison */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Price Comparison</h2>\n            <div className=\"space-y-4\">\n              {product.prices\n                .slice()\n                .sort((a, b) => a.price - b.price)\n                .map((price) => (\n                  <div\n                    key={price.storeName}\n                    className=\"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\"\n                  >\n                    <div>\n                      <h3 className=\"font-medium\">{price.storeName}</h3>\n                      <p className=\"text-2xl font-bold\">\n                        {formatPriceIndian(price.price)}\n                      </p>\n                    </div>\n                    <a\n                      href={price.productUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"btn-primary\"\n                    >\n                      Visit Store\n                    </a>\n                  </div>\n                ))}\n            </div>\n          </div>\n\n          {/* Description */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Description</h2>\n            <p className=\"text-gray-600 whitespace-pre-line\">\n              {product.description}\n            </p>\n          </div>\n\n          {/* Reviews */}\n          <div className=\"mt-12\">\n            <h2 className=\"text-xl font-semibold mb-6\">Customer Reviews</h2>\n\n            {/* Review Form */}\n            <form onSubmit={handleReviewSubmit} className=\"card mb-8\">\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={reviewForm.userName}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, userName: e.target.value })\n                    }\n                    required\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Rating\n                  </label>\n                  <div className=\"mt-1 flex items-center gap-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <button\n                        key={star}\n                        type=\"button\"\n                        onClick={() =>\n                          setReviewForm({ ...reviewForm, rating: star })\n                        }\n                        className=\"p-1 hover:scale-110 transition-transform\"\n                      >\n                        {star <= reviewForm.rating ? (\n                          <StarIcon className=\"h-6 w-6 text-yellow-400\" />\n                        ) : (\n                          <StarOutlineIcon className=\"h-6 w-6 text-gray-300\" />\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Review\n                  </label>\n                  <textarea\n                    value={reviewForm.comment}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, comment: e.target.value })\n                    }\n                    required\n                    rows={4}\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <button type=\"submit\" className=\"btn-primary w-full\">\n                  Submit Review\n                </button>\n              </div>\n            </form>\n\n            {/* Reviews List */}\n            <div className=\"space-y-6\">\n              {product.reviews.map((review, index) => (\n                <div key={index} className=\"card\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">{review.userName}</span>\n                    <span className=\"text-sm text-gray-500\">\n                      {new Date(review.createdAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center mt-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <StarIcon\n                        key={star}\n                        className={`h-4 w-4 ${\n                          star <= review.rating\n                            ? 'text-yellow-400'\n                            : 'text-gray-200'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                  <p className=\"mt-2 text-gray-600\">{review.comment}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASA,QAAQ,IAAIC,eAAe,QAAmB,6BAA6B;AAGpF,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAG,CAAC,GAAGb,SAAS,CAAiB,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC;IAC3C6B,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC;EAAgB,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAErCP,SAAS,CAAC,MAAM;IACd,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACnB,EAAE,EAAE;MACTI,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMgB,IAAI,GAAG,MAAM9B,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;QAChDE,UAAU,CAACkB,IAAI,CAAC;QAChBd,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAIY,eAAe,EAAE;UACnB,IAAI;YACF,MAAMI,UAAU,GAAG,MAAM/B,eAAe,CAACgC,aAAa,CAACvB,EAAE,CAAC;YAC1DU,eAAe,CAACY,UAAU,CAAC;UAC7B,CAAC,CAAC,OAAOE,aAAa,EAAE;YACtBC,OAAO,CAACpB,KAAK,CAAC,iCAAiC,EAAEmB,aAAa,CAAC;UACjE;QACF;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZpB,QAAQ,CAAC,iDAAiD,CAAC;QAC3DmB,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDe,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACnB,EAAE,EAAEkB,eAAe,CAAC,CAAC;EAEzB,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACT,eAAe,EAAE;MACpBU,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACF;IAEA,IAAI,CAAC5B,EAAE,EAAE;IAETY,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,IAAIH,YAAY,EAAE;QAChB,MAAMlB,eAAe,CAACsC,kBAAkB,CAAC7B,EAAE,CAAC;QAC5CU,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM;QACL,MAAMnB,eAAe,CAACuC,aAAa,CAAC9B,EAAE,CAAC;QACvCU,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDuB,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,SAAS;MACRhB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAChC,OAAO,IAAI,CAACD,EAAE,EAAE;IAErB,IAAI;MACF,MAAMV,cAAc,CAAC4C,SAAS,CAAClC,EAAE,EAAE;QAAEgB,MAAM,EAAEH,UAAU,CAACG,MAAM;QAAEC,OAAO,EAAEJ,UAAU,CAACI;MAAQ,CAAC,CAAC;MAC9F;MACA,MAAMkB,cAAc,GAAG,MAAM7C,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;MAC1DE,UAAU,CAACiC,cAAc,CAAC;MAC1BrB,aAAa,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZD,OAAO,CAACpB,KAAK,CAAC,0BAA0B,EAAEqB,GAAG,CAAC;MAC9CE,KAAK,CAAC,4CAA4C,CAAC;IACrD;EACF,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA;QAAKuC,SAAS,EAAC;MAA4F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClH5C,OAAA;QAAGuC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,IAAIpC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEJ,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCxC,OAAA;QAAGuC,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAEhC,KAAK,IAAI;MAAmB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKuC,SAAS,EAAC,gHAAgH;IAAAC,QAAA,eAC7HxC,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDxC,OAAA;QAAAwC,QAAA,gBACExC,OAAA;UAAKuC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DxC,OAAA;YACE6C,GAAG,EAAEhD,WAAW,CAACO,OAAO,CAAC0C,MAAM,CAACpC,aAAa,CAAC,CAAE;YAChDqC,GAAG,EAAEjD,WAAW,CAACM,OAAO,CAAC0C,MAAM,CAACpC,aAAa,CAAC,EAAEN,OAAO,CAAC4C,IAAI,CAAE;YAC9DT,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACLxC,OAAO,CAAC0C,MAAM,CAACG,MAAM,GAAG,CAAC,iBACxBjD,OAAA;UAAKuC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCpC,OAAO,CAAC0C,MAAM,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BpD,OAAA;YAEEqD,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAACyC,KAAK,CAAE;YACvCb,SAAS,EAAE,iEACT7B,aAAa,KAAK0C,KAAK,GACnB,yBAAyB,GACzB,kCAAkC,EACrC;YAAAZ,QAAA,eAEHxC,OAAA;cACE6C,GAAG,EAAEhD,WAAW,CAACsD,KAAK,CAAE;cACxBJ,GAAG,EAAEjD,WAAW,CAACqD,KAAK,EAAE,GAAG/C,OAAO,CAAC4C,IAAI,IAAII,KAAK,GAAG,CAAC,EAAE,CAAE;cACxDb,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC,GAZGQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5C,OAAA;QAAAwC,QAAA,gBACExC,OAAA;UAAIuC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEpC,OAAO,CAAC4C;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGpE5C,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCxC,OAAA;YAAKuC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBtD,OAAA,CAACT,QAAQ;cAEPgD,SAAS,EAAE,WACTe,IAAI,IAAIlD,OAAO,CAACe,MAAM,GAClB,iBAAiB,GACjB,eAAe;YAClB,GALEmC,IAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5C,OAAA;YAAMuC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAACpC,OAAO,CAACmD,OAAO,CAACN,MAAM,EAAC,WAC3B;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxC,OAAA;YAAIuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE5C,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBpC,OAAO,CAACoD,MAAM,CACZC,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC,CACjCX,GAAG,CAAEW,KAAK,iBACT7D,OAAA;cAEEuC,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAIuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEqB,KAAK,CAACC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClD5C,OAAA;kBAAGuC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC9B5C,iBAAiB,CAACiE,KAAK,CAACA,KAAK;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN5C,OAAA;gBACE+D,IAAI,EAAEF,KAAK,CAACG,UAAW;gBACvBC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB3B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,GAhBCiB,KAAK,CAACC,SAAS;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBjB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxC,OAAA;YAAIuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D5C,OAAA;YAAGuC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC7CpC,OAAO,CAAC+D;UAAW;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBxC,OAAA;YAAIuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGhE5C,OAAA;YAAMoE,QAAQ,EAAElC,kBAAmB;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,eACvDxC,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBACEqE,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEtD,UAAU,CAACE,QAAS;kBAC3BqD,QAAQ,EAAGpC,CAAC,IACVlB,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,QAAQ,EAAEiB,CAAC,CAAC8B,MAAM,CAACK;kBAAM,CAAC,CAC1D;kBACDE,QAAQ;kBACRjC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN5C,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAKuC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBtD,OAAA;oBAEEqE,IAAI,EAAC,QAAQ;oBACbhB,OAAO,EAAEA,CAAA,KACPpC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEG,MAAM,EAAEmC;oBAAK,CAAC,CAC9C;oBACDf,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAEnDc,IAAI,IAAItC,UAAU,CAACG,MAAM,gBACxBnB,OAAA,CAACT,QAAQ;sBAACgD,SAAS,EAAC;oBAAyB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEhD5C,OAAA,CAACR,eAAe;sBAAC+C,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrD,GAXIU,IAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYH,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBACEsE,KAAK,EAAEtD,UAAU,CAACI,OAAQ;kBAC1BmD,QAAQ,EAAGpC,CAAC,IACVlB,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEI,OAAO,EAAEe,CAAC,CAAC8B,MAAM,CAACK;kBAAM,CAAC,CACzD;kBACDE,QAAQ;kBACRC,IAAI,EAAE,CAAE;kBACRlC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN5C,OAAA;gBAAQqE,IAAI,EAAC,QAAQ;gBAAC9B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP5C,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBpC,OAAO,CAACmD,OAAO,CAACL,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK,kBACjCpD,OAAA;cAAiBuC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC/BxC,OAAA;gBAAKuC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDxC,OAAA;kBAAMuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEkC,MAAM,CAACxD;gBAAQ;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD5C,OAAA;kBAAMuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpC,IAAImC,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBtD,OAAA,CAACT,QAAQ;kBAEPgD,SAAS,EAAE,WACTe,IAAI,IAAIoB,MAAM,CAACvD,MAAM,GACjB,iBAAiB,GACjB,eAAe;gBAClB,GALEmC,IAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMV,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5C,OAAA;gBAAGuC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEkC,MAAM,CAACtD;cAAO;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAnB9CQ,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAzSID,iBAA2B;EAAA,QAChBX,SAAS,EAYIK,OAAO;AAAA;AAAAmF,EAAA,GAb/B7E,iBAA2B;AA2SjC,eAAeA,iBAAiB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}