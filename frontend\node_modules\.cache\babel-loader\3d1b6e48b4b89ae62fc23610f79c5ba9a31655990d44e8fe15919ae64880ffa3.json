{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * ThemeProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage first\n    const saved = localStorage.getItem('darkMode');\n    if (saved !== null) {\n      return JSON.parse(saved);\n    }\n    // Fall back to system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n  useEffect(() => {\n    // Apply theme to document\n    if (isDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n\n    // Save to localStorage\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  /**\n   * Toggle dark mode\n   */\n  const toggleDarkMode = () => {\n    setIsDarkMode(prev => !prev);\n  };\n  const value = {\n    isDarkMode,\n    toggleDarkMode\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n\n/**\n * Hook to use theme context\n * @returns {Object} Theme context value\n */\n_s(ThemeProvider, \"KkhDWZK0uW9tweg4z0fcyokr+e4=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "ThemeProvider", "children", "_s", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "window", "matchMedia", "matches", "document", "documentElement", "classList", "add", "remove", "setItem", "stringify", "toggleDarkMode", "prev", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst ThemeContext = createContext(undefined);\n\n/**\n * ThemeProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage first\n    const saved = localStorage.getItem('darkMode');\n    if (saved !== null) {\n      return JSON.parse(saved);\n    }\n    // Fall back to system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n\n  useEffect(() => {\n    // Apply theme to document\n    if (isDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    \n    // Save to localStorage\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  /**\n   * Toggle dark mode\n   */\n  const toggleDarkMode = () => {\n    setIsDarkMode(prev => !prev);\n  };\n\n  const value = {\n    isDarkMode,\n    toggleDarkMode,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n/**\n * Hook to use theme context\n * @returns {Object} Theme context value\n */\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAACO,SAAS,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,MAAM;IACjD;IACA,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,IAAIF,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;IAC1B;IACA;IACA,OAAOK,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;EAClE,CAAC,CAAC;EAEFjB,SAAS,CAAC,MAAM;IACd;IACA,IAAIQ,UAAU,EAAE;MACdU,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IACnD;;IAEA;IACAX,YAAY,CAACY,OAAO,CAAC,UAAU,EAAEV,IAAI,CAACW,SAAS,CAAChB,UAAU,CAAC,CAAC;EAC9D,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;AACF;AACA;EACE,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3BhB,aAAa,CAACiB,IAAI,IAAI,CAACA,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMC,KAAK,GAAG;IACZnB,UAAU;IACViB;EACF,CAAC;EAED,oBACEvB,OAAA,CAACC,YAAY,CAACyB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArB,QAAA,EACjCA;EAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;;AAED;AACA;AACA;AACA;AAHAzB,EAAA,CA1CaF,aAAa;AAAA4B,EAAA,GAAb5B,aAAa;AA8C1B,OAAO,MAAM6B,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGtC,UAAU,CAACK,YAAY,CAAC;EACxC,IAAIiC,OAAO,KAAKhC,SAAS,EAAE;IACzB,MAAM,IAAIiC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAQrB,eAAe/B,YAAY;AAAC,IAAA8B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}