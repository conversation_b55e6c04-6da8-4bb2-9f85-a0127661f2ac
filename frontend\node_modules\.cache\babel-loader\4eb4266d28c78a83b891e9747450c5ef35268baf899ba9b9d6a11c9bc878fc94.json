{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\OrdersPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { ShoppingBagIcon, TruckIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\n\n// Mock order data structure\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrdersPage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  // Mock orders data for demonstration\n  const mockOrders = [{\n    _id: '1',\n    orderNumber: 'ORD-2024-001',\n    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n    status: 'delivered',\n    total: 22990,\n    items: [{\n      productId: '1',\n      productName: 'Sony WH-1000XM4 Wireless Headphones',\n      productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n      quantity: 1,\n      price: 22990,\n      storeName: 'Amazon'\n    }],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK123456789'\n  }, {\n    _id: '2',\n    orderNumber: 'ORD-2024-002',\n    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n    status: 'shipped',\n    total: 18999,\n    items: [{\n      productId: '2',\n      productName: 'Adidas Ultraboost 23',\n      productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n      quantity: 1,\n      price: 18999,\n      storeName: 'Adidas'\n    }],\n    shippingAddress: {\n      name: 'John Doe',\n      address: '123 Main Street',\n      city: 'Mumbai',\n      state: 'Maharashtra',\n      pincode: '400001'\n    },\n    trackingNumber: 'TRK987654321'\n  }];\n  useEffect(() => {\n    const fetchOrders = async () => {\n      try {\n        setLoading(true);\n        // TODO: Replace with actual API call\n        // const response = await orderService.getUserOrders();\n        // setOrders(response.data);\n\n        // For now, use mock data\n        setTimeout(() => {\n          setOrders(mockOrders);\n          setLoading(false);\n        }, 1000);\n      } catch (err) {\n        setError('Failed to load orders');\n        setLoading(false);\n      }\n    };\n    if (isAuthenticated) {\n      fetchOrders();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n          className: \"h-5 w-5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 16\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n          className: \"h-5 w-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 16\n        }, this);\n      case 'shipped':\n        return /*#__PURE__*/_jsxDEV(TruckIcon, {\n          className: \"h-5 w-5 text-purple-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 16\n        }, this);\n      case 'delivered':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"h-5 w-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(XCircleIcon, {\n          className: \"h-5 w-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'shipped':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n      case 'delivered':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Please sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: \"You need to be signed in to view your orders.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(XCircleIcon, {\n          className: \"mx-auto h-12 w-12 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n          children: \"Error loading orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this);\n  }\n  if (orders.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8\",\n          children: \"My Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n            children: \"No orders yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n            children: \"Start shopping to see your orders here.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/\",\n              className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n              children: \"Start Shopping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8\",\n        children: [\"My Orders (\", orders.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                  children: [\"Order #\", order.orderNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 dark:text-gray-400\",\n                  children: [\"Placed on \", order.date.toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`,\n                  children: [getStatusIcon(order.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: order.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-gray-900 dark:text-gray-100\",\n                  children: formatPriceIndian(order.total)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: order.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: item.productImage,\n                  alt: item.productName,\n                  className: \"h-16 w-16 object-cover rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                    children: item.productName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                    children: [\"Sold by \", item.storeName, \" \\u2022 Qty: \", item.quantity]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                  children: formatPriceIndian(item.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 dark:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                children: order.trackingNumber && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Tracking: \", order.trackingNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 font-medium\",\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this), order.status === 'delivered' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 font-medium\",\n                  children: \"Write Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, order._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n};\n_s(OrdersPage, \"taKjFgKAbSwKPNEFDK9qcIqadFI=\", false, function () {\n  return [useAuth];\n});\n_c = OrdersPage;\nexport default OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "ShoppingBagIcon", "TruckIcon", "CheckCircleIcon", "XCircleIcon", "formatPriceIndian", "jsxDEV", "_jsxDEV", "OrdersPage", "_s", "user", "isAuthenticated", "orders", "setOrders", "loading", "setLoading", "error", "setError", "mockOrders", "_id", "orderNumber", "date", "Date", "now", "status", "total", "items", "productId", "productName", "productImage", "quantity", "price", "storeName", "shippingAddress", "name", "address", "city", "state", "pincode", "trackingNumber", "fetchOrders", "setTimeout", "err", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "children", "href", "length", "map", "order", "toLocaleDateString", "item", "index", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/OrdersPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { ShoppingBagIcon, TruckIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';\nimport { formatPriceIndian } from '../utils/currency';\n\n// Mock order data structure\ninterface Order {\n  _id: string;\n  orderNumber: string;\n  date: Date;\n  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';\n  total: number;\n  items: Array<{\n    productId: string;\n    productName: string;\n    productImage: string;\n    quantity: number;\n    price: number;\n    storeName: string;\n  }>;\n  shippingAddress: {\n    name: string;\n    address: string;\n    city: string;\n    state: string;\n    pincode: string;\n  };\n  trackingNumber?: string;\n}\n\nconst OrdersPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  // Mock orders data for demonstration\n  const mockOrders: Order[] = [\n    {\n      _id: '1',\n      orderNumber: 'ORD-2024-001',\n      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n      status: 'delivered',\n      total: 22990,\n      items: [\n        {\n          productId: '1',\n          productName: 'Sony WH-1000XM4 Wireless Headphones',\n          productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',\n          quantity: 1,\n          price: 22990,\n          storeName: 'Amazon'\n        }\n      ],\n      shippingAddress: {\n        name: 'John Doe',\n        address: '123 Main Street',\n        city: 'Mumbai',\n        state: 'Maharashtra',\n        pincode: '400001'\n      },\n      trackingNumber: 'TRK123456789'\n    },\n    {\n      _id: '2',\n      orderNumber: 'ORD-2024-002',\n      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n      status: 'shipped',\n      total: 18999,\n      items: [\n        {\n          productId: '2',\n          productName: 'Adidas Ultraboost 23',\n          productImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n          quantity: 1,\n          price: 18999,\n          storeName: 'Adidas'\n        }\n      ],\n      shippingAddress: {\n        name: 'John Doe',\n        address: '123 Main Street',\n        city: 'Mumbai',\n        state: 'Maharashtra',\n        pincode: '400001'\n      },\n      trackingNumber: 'TRK987654321'\n    }\n  ];\n\n  useEffect(() => {\n    const fetchOrders = async () => {\n      try {\n        setLoading(true);\n        // TODO: Replace with actual API call\n        // const response = await orderService.getUserOrders();\n        // setOrders(response.data);\n        \n        // For now, use mock data\n        setTimeout(() => {\n          setOrders(mockOrders);\n          setLoading(false);\n        }, 1000);\n      } catch (err) {\n        setError('Failed to load orders');\n        setLoading(false);\n      }\n    };\n\n    if (isAuthenticated) {\n      fetchOrders();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <ShoppingBagIcon className=\"h-5 w-5 text-yellow-500\" />;\n      case 'processing':\n        return <ShoppingBagIcon className=\"h-5 w-5 text-blue-500\" />;\n      case 'shipped':\n        return <TruckIcon className=\"h-5 w-5 text-purple-500\" />;\n      case 'delivered':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'cancelled':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return <ShoppingBagIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'shipped':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n      case 'delivered':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <ShoppingBagIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Please sign in</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            You need to be signed in to view your orders.\n          </p>\n          <div className=\"mt-6\">\n            <a\n              href=\"/login\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Sign in\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <XCircleIcon className=\"mx-auto h-12 w-12 text-red-500\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">Error loading orders</h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (orders.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8\">My Orders</h1>\n          \n          <div className=\"text-center py-12\">\n            <ShoppingBagIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\">No orders yet</h3>\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n              Start shopping to see your orders here.\n            </p>\n            <div className=\"mt-6\">\n              <a\n                href=\"/\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Start Shopping\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8\">\n          My Orders ({orders.length})\n        </h1>\n\n        <div className=\"space-y-6\">\n          {orders.map((order) => (\n            <div key={order._id} className=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n              {/* Order Header */}\n              <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                      Order #{order.orderNumber}\n                    </h3>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Placed on {order.date.toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center space-x-4\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                      {getStatusIcon(order.status)}\n                      <span className=\"ml-1 capitalize\">{order.status}</span>\n                    </span>\n                    <span className=\"text-lg font-bold text-gray-900 dark:text-gray-100\">\n                      {formatPriceIndian(order.total)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Order Items */}\n              <div className=\"px-6 py-4\">\n                <div className=\"space-y-4\">\n                  {order.items.map((item, index) => (\n                    <div key={index} className=\"flex items-center space-x-4\">\n                      <img\n                        src={item.productImage}\n                        alt={item.productName}\n                        className=\"h-16 w-16 object-cover rounded-md\"\n                      />\n                      <div className=\"flex-1\">\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {item.productName}\n                        </h4>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          Sold by {item.storeName} • Qty: {item.quantity}\n                        </p>\n                      </div>\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                        {formatPriceIndian(item.price)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Order Footer */}\n              <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {order.trackingNumber && (\n                      <span>Tracking: {order.trackingNumber}</span>\n                    )}\n                  </div>\n                  <div className=\"flex space-x-3\">\n                    <button className=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 font-medium\">\n                      View Details\n                    </button>\n                    {order.status === 'delivered' && (\n                      <button className=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 font-medium\">\n                        Write Review\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default OrdersPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,QAAQ,6BAA6B;AACtG,SAASC,iBAAiB,QAAQ,mBAAmB;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAyBA,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMoB,UAAmB,GAAG,CAC1B;IACEC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,CACL;MACEC,SAAS,EAAE,GAAG;MACdC,WAAW,EAAE,qCAAqC;MAClDC,YAAY,EAAE,oEAAoE;MAClFC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE;IACb,CAAC,CACF;IACDC,eAAe,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;IACX,CAAC;IACDC,cAAc,EAAE;EAClB,CAAC,EACD;IACEpB,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,CACL;MACEC,SAAS,EAAE,GAAG;MACdC,WAAW,EAAE,sBAAsB;MACnCC,YAAY,EAAE,iEAAiE;MAC/EC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE;IACb,CAAC,CACF;IACDC,eAAe,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;IACX,CAAC;IACDC,cAAc,EAAE;EAClB,CAAC,CACF;EAEDxC,SAAS,CAAC,MAAM;IACd,MAAMyC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFzB,UAAU,CAAC,IAAI,CAAC;QAChB;QACA;QACA;;QAEA;QACA0B,UAAU,CAAC,MAAM;UACf5B,SAAS,CAACK,UAAU,CAAC;UACrBH,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,OAAO2B,GAAG,EAAE;QACZzB,QAAQ,CAAC,uBAAuB,CAAC;QACjCF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,eAAe,EAAE;MACnB6B,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;EAErB,MAAMgC,aAAa,GAAInB,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOjB,OAAA,CAACN,eAAe;UAAC2C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChE,KAAK,YAAY;QACf,oBAAOzC,OAAA,CAACN,eAAe;UAAC2C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D,KAAK,SAAS;QACZ,oBAAOzC,OAAA,CAACL,SAAS;UAAC0C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D,KAAK,WAAW;QACd,oBAAOzC,OAAA,CAACJ,eAAe;UAACyC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,WAAW;QACd,oBAAOzC,OAAA,CAACH,WAAW;UAACwC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD;QACE,oBAAOzC,OAAA,CAACN,eAAe;UAAC2C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAMC,cAAc,GAAIzB,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,uEAAuE;MAChF,KAAK,YAAY;QACf,OAAO,+DAA+D;MACxE,KAAK,SAAS;QACZ,OAAO,uEAAuE;MAChF,KAAK,WAAW;QACd,OAAO,mEAAmE;MAC5E,KAAK,WAAW;QACd,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,IAAI,CAACb,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKqC,SAAS,EAAC,2EAA2E;MAAAM,QAAA,eACxF3C,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAM,QAAA,gBAC1B3C,OAAA,CAACN,eAAe;UAAC2C,SAAS,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DzC,OAAA;UAAIqC,SAAS,EAAC,2DAA2D;UAAAM,QAAA,EAAC;QAAc;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7FzC,OAAA;UAAGqC,SAAS,EAAC,+CAA+C;UAAAM,QAAA,EAAC;QAE7D;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJzC,OAAA;UAAKqC,SAAS,EAAC,MAAM;UAAAM,QAAA,eACnB3C,OAAA;YACE4C,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,gJAAgJ;YAAAM,QAAA,EAC3J;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKqC,SAAS,EAAC,2EAA2E;MAAAM,QAAA,eACxF3C,OAAA;QAAKqC,SAAS,EAAC;MAAqF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC;EAEV;EAEA,IAAIhC,KAAK,EAAE;IACT,oBACET,OAAA;MAAKqC,SAAS,EAAC,2EAA2E;MAAAM,QAAA,eACxF3C,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAM,QAAA,gBAC1B3C,OAAA,CAACH,WAAW;UAACwC,SAAS,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DzC,OAAA;UAAIqC,SAAS,EAAC,2DAA2D;UAAAM,QAAA,EAAC;QAAoB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnGzC,OAAA;UAAGqC,SAAS,EAAC,+CAA+C;UAAAM,QAAA,EAAElC;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIpC,MAAM,CAACwC,MAAM,KAAK,CAAC,EAAE;IACvB,oBACE7C,OAAA;MAAKqC,SAAS,EAAC,0CAA0C;MAAAM,QAAA,eACvD3C,OAAA;QAAKqC,SAAS,EAAC,6CAA6C;QAAAM,QAAA,gBAC1D3C,OAAA;UAAIqC,SAAS,EAAC,0DAA0D;UAAAM,QAAA,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEvFzC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAM,QAAA,gBAChC3C,OAAA,CAACN,eAAe;YAAC2C,SAAS,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DzC,OAAA;YAAIqC,SAAS,EAAC,2DAA2D;YAAAM,QAAA,EAAC;UAAa;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FzC,OAAA;YAAGqC,SAAS,EAAC,+CAA+C;YAAAM,QAAA,EAAC;UAE7D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAM,QAAA,eACnB3C,OAAA;cACE4C,IAAI,EAAC,GAAG;cACRP,SAAS,EAAC,gJAAgJ;cAAAM,QAAA,EAC3J;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzC,OAAA;IAAKqC,SAAS,EAAC,4DAA4D;IAAAM,QAAA,eACzE3C,OAAA;MAAKqC,SAAS,EAAC,6CAA6C;MAAAM,QAAA,gBAC1D3C,OAAA;QAAIqC,SAAS,EAAC,0DAA0D;QAAAM,QAAA,GAAC,aAC5D,EAACtC,MAAM,CAACwC,MAAM,EAAC,GAC5B;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELzC,OAAA;QAAKqC,SAAS,EAAC,WAAW;QAAAM,QAAA,EACvBtC,MAAM,CAACyC,GAAG,CAAEC,KAAK,iBAChB/C,OAAA;UAAqBqC,SAAS,EAAC,6DAA6D;UAAAM,QAAA,gBAE1F3C,OAAA;YAAKqC,SAAS,EAAC,yDAAyD;YAAAM,QAAA,eACtE3C,OAAA;cAAKqC,SAAS,EAAC,mCAAmC;cAAAM,QAAA,gBAChD3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAIqC,SAAS,EAAC,sDAAsD;kBAAAM,QAAA,GAAC,SAC5D,EAACI,KAAK,CAAClC,WAAW;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACLzC,OAAA;kBAAGqC,SAAS,EAAC,0CAA0C;kBAAAM,QAAA,GAAC,YAC5C,EAACI,KAAK,CAACjC,IAAI,CAACkC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNzC,OAAA;gBAAKqC,SAAS,EAAC,6BAA6B;gBAAAM,QAAA,gBAC1C3C,OAAA;kBAAMqC,SAAS,EAAE,2EAA2EK,cAAc,CAACK,KAAK,CAAC9B,MAAM,CAAC,EAAG;kBAAA0B,QAAA,GACxHP,aAAa,CAACW,KAAK,CAAC9B,MAAM,CAAC,eAC5BjB,OAAA;oBAAMqC,SAAS,EAAC,iBAAiB;oBAAAM,QAAA,EAAEI,KAAK,CAAC9B;kBAAM;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACPzC,OAAA;kBAAMqC,SAAS,EAAC,oDAAoD;kBAAAM,QAAA,EACjE7C,iBAAiB,CAACiD,KAAK,CAAC7B,KAAK;gBAAC;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzC,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAM,QAAA,eACxB3C,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAM,QAAA,EACvBI,KAAK,CAAC5B,KAAK,CAAC2B,GAAG,CAAC,CAACG,IAAI,EAAEC,KAAK,kBAC3BlD,OAAA;gBAAiBqC,SAAS,EAAC,6BAA6B;gBAAAM,QAAA,gBACtD3C,OAAA;kBACEmD,GAAG,EAAEF,IAAI,CAAC3B,YAAa;kBACvB8B,GAAG,EAAEH,IAAI,CAAC5B,WAAY;kBACtBgB,SAAS,EAAC;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACFzC,OAAA;kBAAKqC,SAAS,EAAC,QAAQ;kBAAAM,QAAA,gBACrB3C,OAAA;oBAAIqC,SAAS,EAAC,sDAAsD;oBAAAM,QAAA,EACjEM,IAAI,CAAC5B;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACLzC,OAAA;oBAAGqC,SAAS,EAAC,0CAA0C;oBAAAM,QAAA,GAAC,UAC9C,EAACM,IAAI,CAACxB,SAAS,EAAC,eAAQ,EAACwB,IAAI,CAAC1B,QAAQ;kBAAA;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNzC,OAAA;kBAAKqC,SAAS,EAAC,sDAAsD;kBAAAM,QAAA,EAClE7C,iBAAiB,CAACmD,IAAI,CAACzB,KAAK;gBAAC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA,GAhBES,KAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzC,OAAA;YAAKqC,SAAS,EAAC,uCAAuC;YAAAM,QAAA,eACpD3C,OAAA;cAAKqC,SAAS,EAAC,mCAAmC;cAAAM,QAAA,gBAChD3C,OAAA;gBAAKqC,SAAS,EAAC,0CAA0C;gBAAAM,QAAA,EACtDI,KAAK,CAACf,cAAc,iBACnBhC,OAAA;kBAAA2C,QAAA,GAAM,YAAU,EAACI,KAAK,CAACf,cAAc;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAC7C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNzC,OAAA;gBAAKqC,SAAS,EAAC,gBAAgB;gBAAAM,QAAA,gBAC7B3C,OAAA;kBAAQqC,SAAS,EAAC,0EAA0E;kBAAAM,QAAA,EAAC;gBAE7F;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRM,KAAK,CAAC9B,MAAM,KAAK,WAAW,iBAC3BjB,OAAA;kBAAQqC,SAAS,EAAC,0EAA0E;kBAAAM,QAAA,EAAC;gBAE7F;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GArEEM,KAAK,CAACnC,GAAG;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAjRID,UAAoB;EAAA,QACUR,OAAO;AAAA;AAAA4D,EAAA,GADrCpD,UAAoB;AAmR1B,eAAeA,UAAU;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}