{"ast": null, "code": "// Mock data for development\nconst mockProducts = [{\n  _id: '1',\n  name: 'Sony WH-1000XM4 Wireless Headphones',\n  description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg', 'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 348.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 349.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'John Doe',\n    rating: 5,\n    comment: 'Best headphones I have ever owned!',\n    createdAt: new Date('2025-04-14')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '2',\n  name: 'Samsung 55\" 4K Smart TV',\n  description: 'Crystal clear display with smart features',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 497.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 499.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.5,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '5',\n  name: 'Nike Air Max 270',\n  description: 'Comfortable athletic shoes with Air cushioning',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n  prices: [{\n    storeName: 'Nike',\n    price: 150.00,\n    productUrl: 'https://nike.com'\n  }, {\n    storeName: 'Foot Locker',\n    price: 149.99,\n    productUrl: 'https://footlocker.com'\n  }],\n  rating: 4.6,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '6',\n  name: 'Levi\\'s 501 Original Fit Jeans',\n  description: 'Classic straight leg jeans',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/61G5JoL0-AL._AC_UX569_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 59.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Levi\\'s',\n    price: 69.50,\n    productUrl: 'https://levis.com'\n  }],\n  rating: 4.5,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '7',\n  name: 'Ray-Ban Aviator Sunglasses',\n  description: 'Classic aviator style with UV protection',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/61QZhWyQqAL._AC_UX679_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 154.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Sunglass Hut',\n    price: 169.00,\n    productUrl: 'https://sunglasshut.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '8',\n  name: 'Casio G-Shock Watch',\n  description: 'Durable digital watch with multiple features',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71D9CuBzpZL._AC_UX679_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 99.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Casio',\n    price: 110.00,\n    productUrl: 'https://casio.com'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '17',\n  name: 'Adidas Ultraboost 21',\n  description: 'Premium running shoes with responsive cushioning',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71mGu6-H3pL._AC_UX575_.jpg'],\n  prices: [{\n    storeName: 'Adidas',\n    price: 180.00,\n    productUrl: 'https://adidas.com'\n  }, {\n    storeName: 'Amazon',\n    price: 169.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.8,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '18',\n  name: 'Michael Kors Leather Crossbody Bag',\n  description: 'Elegant leather bag with adjustable strap',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71D3S6yqQtL._AC_UY575_.jpg'],\n  prices: [{\n    storeName: 'Michael Kors',\n    price: 298.00,\n    productUrl: 'https://michaelkors.com'\n  }, {\n    storeName: 'Amazon',\n    price: 278.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.7,\n  reviews: [],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}];\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async filters => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let filteredProducts = [...mockProducts];\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower));\n      }\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => {\n          var _filters$category;\n          return p.category.toLowerCase() === ((_filters$category = filters.category) === null || _filters$category === void 0 ? void 0 : _filters$category.toLowerCase());\n        });\n      }\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0));\n      }\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity));\n      }\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price)));\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price)));\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n    return filteredProducts;\n  },\n  // Get single product by ID\n  getProduct: async id => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n  // Add a review to a product\n  addReview: async (productId, review) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n    product.reviews.push(newReview);\n\n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n    return product;\n  },\n  // Search products\n  searchProducts: async query => {\n    return productService.getProducts({\n      search: query\n    });\n  },\n  // Get products by category\n  getProductsByCategory: async category => {\n    return productService.getProducts({\n      category\n    });\n  },\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};", "map": {"version": 3, "names": ["mockProducts", "_id", "name", "description", "category", "images", "prices", "storeName", "price", "productUrl", "rating", "reviews", "userName", "comment", "createdAt", "Date", "onSale", "productService", "getProducts", "filters", "Promise", "resolve", "setTimeout", "filteredProducts", "search", "searchLower", "toLowerCase", "filter", "p", "includes", "_filters$category", "minPrice", "Math", "min", "map", "maxPrice", "max", "Infinity", "sort", "a", "b", "getProduct", "id", "product", "find", "Error", "add<PERSON>eview", "productId", "review", "newReview", "push", "reduce", "sum", "r", "length", "searchProducts", "query", "getProductsByCategory", "getTodaysDeals"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/api.ts"], "sourcesContent": ["import { Product, ProductFilters, Review } from '../types';\n\n// Mock data for development\nconst mockProducts: Product[] = [\n  {\n    _id: '1',\n    name: 'Sony WH-1000XM4 Wireless Headphones',\n    description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg',\n      'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 348.00,\n        productUrl: 'https://amazon.com'\n      },\n      {\n        storeName: 'Best Buy',\n        price: 349.99,\n        productUrl: 'https://bestbuy.com'\n      }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'John Doe',\n        rating: 5,\n        comment: 'Best headphones I have ever owned!',\n        createdAt: new Date('2025-04-14')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '2',\n    name: 'Samsung 55\" 4K Smart TV',\n    description: 'Crystal clear display with smart features',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 497.99,\n        productUrl: 'https://amazon.com'\n      },\n      {\n        storeName: 'Best Buy',\n        price: 499.99,\n        productUrl: 'https://bestbuy.com'\n      }\n    ],\n    rating: 4.5,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '5',\n    name: 'Nike Air Max 270',\n    description: 'Comfortable athletic shoes with Air cushioning',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n    prices: [\n      { storeName: 'Nike', price: 150.00, productUrl: 'https://nike.com' },\n      { storeName: 'Foot Locker', price: 149.99, productUrl: 'https://footlocker.com' }\n    ],\n    rating: 4.6,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '6',\n    name: 'Levi\\'s 501 Original Fit Jeans',\n    description: 'Classic straight leg jeans',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/61G5JoL0-AL._AC_UX569_.jpg'],\n    prices: [\n      { storeName: 'Amazon', price: 59.99, productUrl: 'https://amazon.com' },\n      { storeName: 'Levi\\'s', price: 69.50, productUrl: 'https://levis.com' }\n    ],\n    rating: 4.5,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '7',\n    name: 'Ray-Ban Aviator Sunglasses',\n    description: 'Classic aviator style with UV protection',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/61QZhWyQqAL._AC_UX679_.jpg'],\n    prices: [\n      { storeName: 'Amazon', price: 154.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Sunglass Hut', price: 169.00, productUrl: 'https://sunglasshut.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '8',\n    name: 'Casio G-Shock Watch',\n    description: 'Durable digital watch with multiple features',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71D9CuBzpZL._AC_UX679_.jpg'],\n    prices: [\n      { storeName: 'Amazon', price: 99.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Casio', price: 110.00, productUrl: 'https://casio.com' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '17',\n    name: 'Adidas Ultraboost 21',\n    description: 'Premium running shoes with responsive cushioning',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71mGu6-H3pL._AC_UX575_.jpg'],\n    prices: [\n      { storeName: 'Adidas', price: 180.00, productUrl: 'https://adidas.com' },\n      { storeName: 'Amazon', price: 169.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.8,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '18',\n    name: 'Michael Kors Leather Crossbody Bag',\n    description: 'Elegant leather bag with adjustable strap',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71D3S6yqQtL._AC_UY575_.jpg'],\n    prices: [\n      { storeName: 'Michael Kors', price: 298.00, productUrl: 'https://michaelkors.com' },\n      { storeName: 'Amazon', price: 278.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.7,\n    reviews: [],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n];\n\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async (filters?: ProductFilters) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    let filteredProducts = [...mockProducts];\n\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => \n          p.name.toLowerCase().includes(searchLower) ||\n          p.description.toLowerCase().includes(searchLower)\n        );\n      }\n\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => \n          p.category.toLowerCase() === filters.category?.toLowerCase()\n        );\n      }\n\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0)\n        );\n      }\n\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity)\n        );\n      }\n\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => \n              Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price))\n            );\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => \n              Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price))\n            );\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n\n    return filteredProducts;\n  },\n\n  // Get single product by ID\n  getProduct: async (id: string) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n\n  // Add a review to a product\n  addReview: async (productId: string, review: Omit<Review, 'createdAt'>) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n\n    product.reviews.push(newReview);\n    \n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n\n    return product;\n  },\n\n  // Search products\n  searchProducts: async (query: string) => {\n    return productService.getProducts({ search: query });\n  },\n\n  // Get products by category\n  getProductsByCategory: async (category: string) => {\n    return productService.getProducts({ category });\n  },\n\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};\n"], "mappings": "AAEA;AACA,MAAMA,YAAuB,GAAG,CAC9B;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qCAAqC;EAC3CC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,EACjE,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,CACF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,UAAU;IACpBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oCAAoC;IAC7CC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,CACF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,gDAAgD;EAC7DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAmB,CAAC,EACpE;IAAEF,SAAS,EAAE,aAAa;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAyB,CAAC,CAClF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,4BAA4B;EACzCC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvE;IAAEF,SAAS,EAAE,SAAS;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAoB,CAAC,CACxE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,4BAA4B;EAClCC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA0B,CAAC,CACpF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvE;IAAEF,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAoB,CAAC,CACvE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,sBAAsB;EAC5BC,WAAW,EAAE,kDAAkD;EAC/DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,EACD;EACEf,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,oCAAoC;EAC1CC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA0B,CAAC,EACnF;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,EAAE;EACXG,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAOC,OAAwB,IAAK;IAC/C;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIE,gBAAgB,GAAG,CAAC,GAAGvB,YAAY,CAAC;IAExC,IAAImB,OAAO,EAAE;MACX,IAAIA,OAAO,CAACK,MAAM,EAAE;QAClB,MAAMC,WAAW,GAAGN,OAAO,CAACK,MAAM,CAACE,WAAW,CAAC,CAAC;QAChDH,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CA,CAAC,CAAC1B,IAAI,CAACwB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC1CG,CAAC,CAACzB,WAAW,CAACuB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAClD,CAAC;MACH;MAEA,IAAIN,OAAO,CAACf,QAAQ,EAAE;QACpBmB,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC;UAAA,IAAAE,iBAAA;UAAA,OAC1CF,CAAC,CAACxB,QAAQ,CAACsB,WAAW,CAAC,CAAC,OAAAI,iBAAA,GAAKX,OAAO,CAACf,QAAQ,cAAA0B,iBAAA,uBAAhBA,iBAAA,CAAkBJ,WAAW,CAAC,CAAC;QAAA,CAC9D,CAAC;MACH;MAEA,IAAIP,OAAO,CAACY,QAAQ,EAAE;QACpBR,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACC,GAAG,CAAC,GAAGL,CAAC,CAACtB,MAAM,CAAC4B,GAAG,CAAC1B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKW,OAAO,CAACY,QAAQ,IAAI,CAAC,CAC3E,CAAC;MACH;MAEA,IAAIZ,OAAO,CAACgB,QAAQ,EAAE;QACpBZ,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACI,GAAG,CAAC,GAAGR,CAAC,CAACtB,MAAM,CAAC4B,GAAG,CAAC1B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKW,OAAO,CAACgB,QAAQ,IAAIE,QAAQ,CAClF,CAAC;MACH;MAEA,IAAIlB,OAAO,CAACmB,IAAI,EAAE;QAChB,QAAQnB,OAAO,CAACmB,IAAI;UAClB,KAAK,WAAW;YACdf,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACjC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAAC,GAAGwB,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAAClC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,YAAY;YACfe,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAAClC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAAC,GAAGwB,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACjC,MAAM,CAAC4B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,QAAQ;YACXe,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9B,MAAM,GAAG6B,CAAC,CAAC7B,MAAM,CAAC;YACpD;QACJ;MACF;IACF;IAEA,OAAOa,gBAAgB;EACzB,CAAC;EAED;EACAkB,UAAU,EAAE,MAAOC,EAAU,IAAK;IAChC;IACA,MAAM,IAAItB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG3C,YAAY,CAAC4C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAKyC,EAAE,CAAC;IACpD,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAClD,OAAOF,OAAO;EAChB,CAAC;EAED;EACAG,SAAS,EAAE,MAAAA,CAAOC,SAAiB,EAAEC,MAAiC,KAAK;IACzE;IACA,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG3C,YAAY,CAAC4C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAK8C,SAAS,CAAC;IAC3D,IAAI,CAACJ,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAElD,MAAMI,SAAS,GAAG;MAChB,GAAGD,MAAM;MACTlC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAED4B,OAAO,CAAChC,OAAO,CAACuC,IAAI,CAACD,SAAS,CAAC;;IAE/B;IACAN,OAAO,CAACjC,MAAM,GAAGiC,OAAO,CAAChC,OAAO,CAACwC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC3C,MAAM,EAAE,CAAC,CAAC,GAAGiC,OAAO,CAAChC,OAAO,CAAC2C,MAAM;IAE/F,OAAOX,OAAO;EAChB,CAAC;EAED;EACAY,cAAc,EAAE,MAAOC,KAAa,IAAK;IACvC,OAAOvC,cAAc,CAACC,WAAW,CAAC;MAAEM,MAAM,EAAEgC;IAAM,CAAC,CAAC;EACtD,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAOrD,QAAgB,IAAK;IACjD,OAAOa,cAAc,CAACC,WAAW,CAAC;MAAEd;IAAS,CAAC,CAAC;EACjD,CAAC;EAED;EACAsD,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B;IACA,MAAM,IAAItC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOrB,YAAY,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,MAAM,CAAC;EAC3C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}