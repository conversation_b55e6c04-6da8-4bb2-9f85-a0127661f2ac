{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async (filters = {}) => {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n  // Get featured products\n  getFeaturedProducts: async (limit = 8) => {\n    const response = await api.get(`/products/featured?limit=${limit}`);\n    return response.data;\n  },\n  // Get products on sale\n  getSaleProducts: async (limit = 20) => {\n    const response = await api.get(`/products/sale?limit=${limit}`);\n    return response.data;\n  },\n  // Get trending products\n  getTrendingProducts: async (limit = 10) => {\n    const response = await api.get(`/products/trending?limit=${limit}`);\n    return response.data;\n  },\n  // Get single product by ID\n  getProduct: async id => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n  // Search products\n  searchProducts: async (query, filters = {}) => {\n    const params = new URLSearchParams({\n      search: query,\n      ...filters\n    });\n    const response = await api.get(`/products/search?${params.toString()}`);\n    return response.data;\n  },\n  // Get products by category\n  getProductsByCategory: async (category, filters = {}) => {\n    const params = new URLSearchParams({\n      category,\n      ...filters\n    });\n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n  // Add review to product\n  addReview: async (productId, review) => {\n    const response = await api.post(`/products/${productId}/reviews`, review);\n    return response.data;\n  },\n  // Get product reviews\n  getProductReviews: async (productId, page = 1, limit = 10) => {\n    const response = await api.get(`/products/${productId}/reviews?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n  // Get categories\n  getCategories: async () => {\n    const response = await api.get('/products/categories');\n    return response.data;\n  },\n  // Get brands\n  getBrands: async () => {\n    const response = await api.get('/products/brands');\n    return response.data;\n  },\n  // Get price history for a product\n  getPriceHistory: async (productId, days = 30) => {\n    const response = await api.get(`/products/${productId}/price-history?days=${days}`);\n    return response.data;\n  },\n  // Compare products\n  compareProducts: async productIds => {\n    const response = await api.post('/products/compare', {\n      productIds\n    });\n    return response.data;\n  },\n  // Get similar products\n  getSimilarProducts: async (productId, limit = 6) => {\n    const response = await api.get(`/products/${productId}/similar?limit=${limit}`);\n    return response.data;\n  },\n  // Get product availability\n  getProductAvailability: async productId => {\n    const response = await api.get(`/products/${productId}/availability`);\n    return response.data;\n  }\n};\nexport default productService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "productService", "getProducts", "filters", "params", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "append", "toString", "response", "get", "data", "getFeaturedProducts", "limit", "getSaleProducts", "getTrendingProducts", "getProduct", "id", "searchProducts", "query", "search", "getProductsByCategory", "category", "add<PERSON>eview", "productId", "review", "post", "getProductReviews", "page", "getCategories", "getBrands", "getPriceHistory", "days", "compareProducts", "productIds", "getSimilarProducts", "getProductAvailability"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/productService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async (filters = {}) => {\n    const params = new URLSearchParams();\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get featured products\n  getFeaturedProducts: async (limit = 8) => {\n    const response = await api.get(`/products/featured?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get products on sale\n  getSaleProducts: async (limit = 20) => {\n    const response = await api.get(`/products/sale?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get trending products\n  getTrendingProducts: async (limit = 10) => {\n    const response = await api.get(`/products/trending?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get single product by ID\n  getProduct: async (id) => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n\n  // Search products\n  searchProducts: async (query, filters = {}) => {\n    const params = new URLSearchParams({\n      search: query,\n      ...filters\n    });\n    \n    const response = await api.get(`/products/search?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get products by category\n  getProductsByCategory: async (category, filters = {}) => {\n    const params = new URLSearchParams({\n      category,\n      ...filters\n    });\n    \n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n\n  // Add review to product\n  addReview: async (productId, review) => {\n    const response = await api.post(`/products/${productId}/reviews`, review);\n    return response.data;\n  },\n\n  // Get product reviews\n  getProductReviews: async (productId, page = 1, limit = 10) => {\n    const response = await api.get(`/products/${productId}/reviews?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // Get categories\n  getCategories: async () => {\n    const response = await api.get('/products/categories');\n    return response.data;\n  },\n\n  // Get brands\n  getBrands: async () => {\n    const response = await api.get('/products/brands');\n    return response.data;\n  },\n\n  // Get price history for a product\n  getPriceHistory: async (productId, days = 30) => {\n    const response = await api.get(`/products/${productId}/price-history?days=${days}`);\n    return response.data;\n  },\n\n  // Compare products\n  compareProducts: async (productIds) => {\n    const response = await api.post('/products/compare', { productIds });\n    return response.data;\n  },\n\n  // Get similar products\n  getSimilarProducts: async (productId, limit = 6) => {\n    const response = await api.get(`/products/${productId}/similar?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get product availability\n  getProductAvailability: async (productId) => {\n    const response = await api.get(`/products/${productId}/availability`);\n    return response.data;\n  }\n};\n\nexport default productService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMK,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;IACnC,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpCC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,MAAMC,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAaX,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,mBAAmB,EAAE,MAAAA,CAAOC,KAAK,GAAG,CAAC,KAAK;IACxC,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,4BAA4BG,KAAK,EAAE,CAAC;IACnE,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,eAAe,EAAE,MAAAA,CAAOD,KAAK,GAAG,EAAE,KAAK;IACrC,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,wBAAwBG,KAAK,EAAE,CAAC;IAC/D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,mBAAmB,EAAE,MAAAA,CAAOF,KAAK,GAAG,EAAE,KAAK;IACzC,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,4BAA4BG,KAAK,EAAE,CAAC;IACnE,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAK,UAAU,EAAE,MAAOC,EAAE,IAAK;IACxB,MAAMR,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAaO,EAAE,EAAE,CAAC;IACjD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,cAAc,EAAE,MAAAA,CAAOC,KAAK,EAAErB,OAAO,GAAG,CAAC,CAAC,KAAK;IAC7C,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCoB,MAAM,EAAED,KAAK;MACb,GAAGrB;IACL,CAAC,CAAC;IAEF,MAAMW,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,oBAAoBX,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;IACvE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,qBAAqB,EAAE,MAAAA,CAAOC,QAAQ,EAAExB,OAAO,GAAG,CAAC,CAAC,KAAK;IACvD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCsB,QAAQ;MACR,GAAGxB;IACL,CAAC,CAAC;IAEF,MAAMW,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAaX,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;IACtC,MAAMhB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0C,IAAI,CAAC,aAAaF,SAAS,UAAU,EAAEC,MAAM,CAAC;IACzE,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,iBAAiB,EAAE,MAAAA,CAAOH,SAAS,EAAEI,IAAI,GAAG,CAAC,EAAEf,KAAK,GAAG,EAAE,KAAK;IAC5D,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAac,SAAS,iBAAiBI,IAAI,UAAUf,KAAK,EAAE,CAAC;IAC5F,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkB,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,MAAMpB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,sBAAsB,CAAC;IACtD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAmB,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAMrB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAoB,eAAe,EAAE,MAAAA,CAAOP,SAAS,EAAEQ,IAAI,GAAG,EAAE,KAAK;IAC/C,MAAMvB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAac,SAAS,uBAAuBQ,IAAI,EAAE,CAAC;IACnF,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAsB,eAAe,EAAE,MAAOC,UAAU,IAAK;IACrC,MAAMzB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0C,IAAI,CAAC,mBAAmB,EAAE;MAAEQ;IAAW,CAAC,CAAC;IACpE,OAAOzB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAwB,kBAAkB,EAAE,MAAAA,CAAOX,SAAS,EAAEX,KAAK,GAAG,CAAC,KAAK;IAClD,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAac,SAAS,kBAAkBX,KAAK,EAAE,CAAC;IAC/E,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAyB,sBAAsB,EAAE,MAAOZ,SAAS,IAAK;IAC3C,MAAMf,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAac,SAAS,eAAe,CAAC;IACrE,OAAOf,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}