{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      try {\n        const productData = await productService.getProduct(id);\n        setProduct(productData);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProduct();\n  }, [id, isAuthenticated]);\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n    if (!id) return;\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error || 'Product not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  }\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = highestPrice - lowestPrice;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"aspect-square w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-700 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(product.images[selectedImage]),\n                alt: getImageAlt(product.images[selectedImage], product.name),\n                className: \"h-full w-full object-cover object-center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-4 gap-2\",\n              children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedImage(index),\n                className: `aspect-square overflow-hidden rounded-md ${selectedImage === index ? 'ring-2 ring-blue-500' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(image),\n                  alt: getImageAlt(image, product.name),\n                  className: \"h-full w-full object-cover object-center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 flex-1\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleWishlistToggle,\n                disabled: wishlistLoading,\n                className: \"ml-4 p-3 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\",\n                title: isInWishlist ? 'Remove from wishlist' : 'Add to wishlist',\n                children: isInWishlist ? /*#__PURE__*/_jsxDEV(HeartSolidIcon, {\n                  className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"h-6 w-6 text-gray-600 dark:text-gray-400 hover:text-red-500 transition-colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: `h-5 w-5 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: [product.rating, \" (\", product.reviews.length, \" reviews)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                  children: formatPriceIndian(lowestPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), savings > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg text-green-600 dark:text-green-400\",\n                  children: [\"Save \", formatPriceIndian(savings)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                children: [\"Prices from \", product.prices.length, \" store\", product.prices.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600 dark:text-gray-400\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), product.features && product.features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                children: \"Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"mt-2 space-y-1\",\n                children: product.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-gray-600 dark:text-gray-400 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this), feature]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200 dark:border-gray-700 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\",\n            children: \"Price Comparison\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n            children: product.prices.map((price, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 dark:text-gray-100\",\n                    children: price.storeName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                    children: formatPriceIndian(price.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this), price.originalPrice > price.price && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                    children: formatPriceIndian(price.originalPrice)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [price.discount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-block bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full mb-2\",\n                    children: [price.discount, \"% OFF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm ${price.inStock ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`,\n                    children: price.inStock ? `In Stock (${price.stockCount})` : 'Out of Stock'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: price.productUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block\",\n                children: [\"View on \", price.storeName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200 dark:border-gray-700 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\",\n            children: \"Customer Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), product.reviews.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: product.reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-b border-gray-200 dark:border-gray-600 pb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: `h-4 w-4 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium text-gray-900 dark:text-gray-100\",\n                  children: review.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                  children: new Date(review.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"No reviews yet. Be the first to review this product!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"JwZSQdcwFBvVUiYj+/OIzNVZhyU=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "StarIcon", "HeartIcon", "HeartSolidIcon", "productService", "wishlistService", "useAuth", "formatPriceIndian", "getImageUrl", "getImageAlt", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "isInWishlist", "setIsInWishlist", "wishlistLoading", "setWishlistLoading", "isAuthenticated", "fetchProduct", "productData", "getProduct", "inWishlist", "checkWishlist", "wishlistError", "console", "err", "handleWishlistToggle", "alert", "removeFromWishlist", "addToWishlist", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "lowestPrice", "Math", "min", "prices", "map", "p", "price", "highestPrice", "max", "savings", "src", "images", "alt", "name", "length", "image", "index", "disabled", "title", "Array", "_", "i", "floor", "rating", "reviews", "description", "features", "feature", "storeName", "originalPrice", "discount", "inStock", "stockCount", "href", "productUrl", "target", "rel", "review", "userName", "Date", "createdAt", "toLocaleDateString", "comment", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProductDetailPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\nconst ProductDetailPage = () => {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      \n      try {\n        const productData = await productService.getProduct(id);\n        setProduct(productData);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id, isAuthenticated]);\n\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n\n    if (!id) return;\n\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n\n\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 dark:text-red-400 mb-4\">{error || 'Product not found'}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = highestPrice - lowestPrice;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8\">\n            {/* Product Images */}\n            <div>\n              <div className=\"aspect-square w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-700 mb-4\">\n                <img\n                  src={getImageUrl(product.images[selectedImage])}\n                  alt={getImageAlt(product.images[selectedImage], product.name)}\n                  className=\"h-full w-full object-cover object-center\"\n                />\n              </div>\n              \n              {product.images.length > 1 && (\n                <div className=\"grid grid-cols-4 gap-2\">\n                  {product.images.map((image, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setSelectedImage(index)}\n                      className={`aspect-square overflow-hidden rounded-md ${\n                        selectedImage === index ? 'ring-2 ring-blue-500' : ''\n                      }`}\n                    >\n                      <img\n                        src={getImageUrl(image)}\n                        alt={getImageAlt(image, product.name)}\n                        className=\"h-full w-full object-cover object-center\"\n                      />\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Product Info */}\n            <div>\n              <div className=\"flex items-start justify-between\">\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 flex-1\">{product.name}</h1>\n                \n                {/* Wishlist Button */}\n                <button\n                  onClick={handleWishlistToggle}\n                  disabled={wishlistLoading}\n                  className=\"ml-4 p-3 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\"\n                  title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}\n                >\n                  {isInWishlist ? (\n                    <HeartSolidIcon className=\"h-6 w-6 text-red-500\" />\n                  ) : (\n                    <HeartIcon className=\"h-6 w-6 text-gray-600 dark:text-gray-400 hover:text-red-500 transition-colors\" />\n                  )}\n                </button>\n              </div>\n\n              {/* Rating */}\n              <div className=\"mt-4 flex items-center\">\n                <div className=\"flex items-center\">\n                  {[...Array(5)].map((_, i) => (\n                    <StarIcon\n                      key={i}\n                      className={`h-5 w-5 ${\n                        i < Math.floor(product.rating)\n                          ? 'text-yellow-400'\n                          : 'text-gray-300 dark:text-gray-600'\n                      }`}\n                    />\n                  ))}\n                </div>\n                <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">\n                  {product.rating} ({product.reviews.length} reviews)\n                </span>\n              </div>\n\n              {/* Price */}\n              <div className=\"mt-6\">\n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n                    {formatPriceIndian(lowestPrice)}\n                  </span>\n                  {savings > 0 && (\n                    <span className=\"text-lg text-green-600 dark:text-green-400\">\n                      Save {formatPriceIndian(savings)}\n                    </span>\n                  )}\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  Prices from {product.prices.length} store{product.prices.length !== 1 ? 's' : ''}\n                </p>\n              </div>\n\n              {/* Description */}\n              <div className=\"mt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Description</h3>\n                <p className=\"mt-2 text-gray-600 dark:text-gray-400\">{product.description}</p>\n              </div>\n\n              {/* Features */}\n              {product.features && product.features.length > 0 && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Features</h3>\n                  <ul className=\"mt-2 space-y-1\">\n                    {product.features.map((feature, index) => (\n                      <li key={index} className=\"text-gray-600 dark:text-gray-400 flex items-center\">\n                        <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Price Comparison */}\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-8\">\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\">Price Comparison</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {product.prices.map((price, index) => (\n                <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">{price.storeName}</h4>\n                      <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                        {formatPriceIndian(price.price)}\n                      </p>\n                      {price.originalPrice > price.price && (\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400 line-through\">\n                          {formatPriceIndian(price.originalPrice)}\n                        </p>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      {price.discount > 0 && (\n                        <span className=\"inline-block bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full mb-2\">\n                          {price.discount}% OFF\n                        </span>\n                      )}\n                      <p className={`text-sm ${price.inStock ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\n                        {price.inStock ? `In Stock (${price.stockCount})` : 'Out of Stock'}\n                      </p>\n                    </div>\n                  </div>\n                  <a\n                    href={price.productUrl}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block\"\n                  >\n                    View on {price.storeName}\n                  </a>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Reviews */}\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-8\">\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\">Customer Reviews</h3>\n            \n            {product.reviews.length > 0 ? (\n              <div className=\"space-y-6\">\n                {product.reviews.map((review, index) => (\n                  <div key={index} className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n                    <div className=\"flex items-center mb-2\">\n                      <div className=\"flex items-center\">\n                        {[...Array(5)].map((_, i) => (\n                          <StarIcon\n                            key={i}\n                            className={`h-4 w-4 ${\n                              i < review.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'\n                            }`}\n                          />\n                        ))}\n                      </div>\n                      <span className=\"ml-2 font-medium text-gray-900 dark:text-gray-100\">{review.userName}</span>\n                      <span className=\"ml-2 text-sm text-gray-500 dark:text-gray-400\">\n                        {new Date(review.createdAt).toLocaleDateString()}\n                      </span>\n                    </div>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{review.comment}</p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-600 dark:text-gray-400\">No reviews yet. Be the first to review this product!</p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASA,SAAS,IAAIC,cAAc,QAAQ,2BAA2B;AACvE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM;IAAE4B;EAAgB,CAAC,GAAGrB,OAAO,CAAC,CAAC;EAErCR,SAAS,CAAC,MAAM;IACd,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACd,EAAE,EAAE;MAET,IAAI;QACF,MAAMe,WAAW,GAAG,MAAMzB,cAAc,CAAC0B,UAAU,CAAChB,EAAE,CAAC;QACvDE,UAAU,CAACa,WAAW,CAAC;QACvBT,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAIO,eAAe,EAAE;UACnB,IAAI;YACF,MAAMI,UAAU,GAAG,MAAM1B,eAAe,CAAC2B,aAAa,CAAClB,EAAE,CAAC;YAC1DU,eAAe,CAACO,UAAU,CAAC;UAC7B,CAAC,CAAC,OAAOE,aAAa,EAAE;YACtBC,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEc,aAAa,CAAC;UACjE;QACF;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZf,QAAQ,CAAC,iDAAiD,CAAC;QAC3Dc,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEgB,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDU,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACd,EAAE,EAAEa,eAAe,CAAC,CAAC;EAEzB,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACT,eAAe,EAAE;MACpBU,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACF;IAEA,IAAI,CAACvB,EAAE,EAAE;IAETY,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,IAAIH,YAAY,EAAE;QAChB,MAAMlB,eAAe,CAACiC,kBAAkB,CAACxB,EAAE,CAAC;QAC5CU,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM;QACL,MAAMnB,eAAe,CAACkC,aAAa,CAACzB,EAAE,CAAC;QACvCU,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDkB,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,SAAS;MACRX,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAID,IAAIT,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK6B,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxF9B,OAAA;QAAK6B,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,IAAI1B,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEJ,OAAA;MAAK6B,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxF9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAG6B,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAEtB,KAAK,IAAI;QAAmB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrFlC,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCT,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGrC,OAAO,CAACsC,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EACjE,MAAMC,YAAY,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAG3C,OAAO,CAACsC,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EAClE,MAAMG,OAAO,GAAGF,YAAY,GAAGP,WAAW;EAE1C,oBACEvC,OAAA;IAAK6B,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5D9B,OAAA;MAAK6B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD9B,OAAA;QAAK6B,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E9B,OAAA;UAAK6B,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBAExD9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChG9B,OAAA;gBACEiD,GAAG,EAAEpD,WAAW,CAACO,OAAO,CAAC8C,MAAM,CAACxC,aAAa,CAAC,CAAE;gBAChDyC,GAAG,EAAErD,WAAW,CAACM,OAAO,CAAC8C,MAAM,CAACxC,aAAa,CAAC,EAAEN,OAAO,CAACgD,IAAI,CAAE;gBAC9DvB,SAAS,EAAC;cAA0C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL9B,OAAO,CAAC8C,MAAM,CAACG,MAAM,GAAG,CAAC,iBACxBrD,OAAA;cAAK6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC1B,OAAO,CAAC8C,MAAM,CAACP,GAAG,CAAC,CAACW,KAAK,EAAEC,KAAK,kBAC/BvD,OAAA;gBAEEmC,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC4C,KAAK,CAAE;gBACvC1B,SAAS,EAAE,4CACTnB,aAAa,KAAK6C,KAAK,GAAG,sBAAsB,GAAG,EAAE,EACpD;gBAAAzB,QAAA,eAEH9B,OAAA;kBACEiD,GAAG,EAAEpD,WAAW,CAACyD,KAAK,CAAE;kBACxBH,GAAG,EAAErD,WAAW,CAACwD,KAAK,EAAElD,OAAO,CAACgD,IAAI,CAAE;kBACtCvB,SAAS,EAAC;gBAA0C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC,GAVGqB,KAAK;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9B,OAAA;gBAAI6B,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EAAE1B,OAAO,CAACgD;cAAI;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAG9FlC,OAAA;gBACEmC,OAAO,EAAEV,oBAAqB;gBAC9B+B,QAAQ,EAAE1C,eAAgB;gBAC1Be,SAAS,EAAC,mIAAmI;gBAC7I4B,KAAK,EAAE7C,YAAY,GAAG,sBAAsB,GAAG,iBAAkB;gBAAAkB,QAAA,EAEhElB,YAAY,gBACXZ,OAAA,CAACR,cAAc;kBAACqC,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnDlC,OAAA,CAACT,SAAS;kBAACsC,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACvG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC9B,OAAA;gBAAK6B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/B,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,EAAEC,CAAC,kBACtB5D,OAAA,CAACV,QAAQ;kBAEPuC,SAAS,EAAE,WACT+B,CAAC,GAAGpB,IAAI,CAACqB,KAAK,CAACzD,OAAO,CAAC0D,MAAM,CAAC,GAC1B,iBAAiB,GACjB,kCAAkC;gBACrC,GALEF,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMP,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlC,OAAA;gBAAM6B,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,GAC5D1B,OAAO,CAAC0D,MAAM,EAAC,IAAE,EAAC1D,OAAO,CAAC2D,OAAO,CAACV,MAAM,EAAC,WAC5C;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAK6B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9B,OAAA;kBAAM6B,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAClElC,iBAAiB,CAAC2C,WAAW;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,EACNc,OAAO,GAAG,CAAC,iBACVhD,OAAA;kBAAM6B,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,GAAC,OACtD,EAAClC,iBAAiB,CAACoD,OAAO,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNlC,OAAA;gBAAG6B,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,GAAC,cAC/C,EAAC1B,OAAO,CAACsC,MAAM,CAACW,MAAM,EAAC,QAAM,EAACjD,OAAO,CAACsC,MAAM,CAACW,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFlC,OAAA;gBAAG6B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE1B,OAAO,CAAC4D;cAAW;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,EAGL9B,OAAO,CAAC6D,QAAQ,IAAI7D,OAAO,CAAC6D,QAAQ,CAACZ,MAAM,GAAG,CAAC,iBAC9CrD,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFlC,OAAA;gBAAI6B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3B1B,OAAO,CAAC6D,QAAQ,CAACtB,GAAG,CAAC,CAACuB,OAAO,EAAEX,KAAK,kBACnCvD,OAAA;kBAAgB6B,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBAC5E9B,OAAA;oBAAM6B,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC9DgC,OAAO;gBAAA,GAFDX,KAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE9B,OAAA;YAAI6B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7FlC,OAAA;YAAK6B,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClE1B,OAAO,CAACsC,MAAM,CAACC,GAAG,CAAC,CAACE,KAAK,EAAEU,KAAK,kBAC/BvD,OAAA;cAAiB6B,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACrF9B,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD9B,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAEe,KAAK,CAACsB;kBAAS;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnFlC,OAAA;oBAAG6B,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC/DlC,iBAAiB,CAACiD,KAAK,CAACA,KAAK;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACHW,KAAK,CAACuB,aAAa,GAAGvB,KAAK,CAACA,KAAK,iBAChC7C,OAAA;oBAAG6B,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,EACjElC,iBAAiB,CAACiD,KAAK,CAACuB,aAAa;kBAAC;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlC,OAAA;kBAAK6B,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACxBe,KAAK,CAACwB,QAAQ,GAAG,CAAC,iBACjBrE,OAAA;oBAAM6B,SAAS,EAAC,oHAAoH;oBAAAC,QAAA,GACjIe,KAAK,CAACwB,QAAQ,EAAC,OAClB;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eACDlC,OAAA;oBAAG6B,SAAS,EAAE,WAAWgB,KAAK,CAACyB,OAAO,GAAG,oCAAoC,GAAG,gCAAgC,EAAG;oBAAAxC,QAAA,EAChHe,KAAK,CAACyB,OAAO,GAAG,aAAazB,KAAK,CAAC0B,UAAU,GAAG,GAAG;kBAAc;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlC,OAAA;gBACEwE,IAAI,EAAE3B,KAAK,CAAC4B,UAAW;gBACvBC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB9C,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,GAC1H,UACS,EAACe,KAAK,CAACsB,SAAS;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA,GA/BIqB,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE9B,OAAA;YAAI6B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAE5F9B,OAAO,CAAC2D,OAAO,CAACV,MAAM,GAAG,CAAC,gBACzBrD,OAAA;YAAK6B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB1B,OAAO,CAAC2D,OAAO,CAACpB,GAAG,CAAC,CAACiC,MAAM,EAAErB,KAAK,kBACjCvD,OAAA;cAAiB6B,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAC7E9B,OAAA;gBAAK6B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9B,OAAA;kBAAK6B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC/B,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,EAAEC,CAAC,kBACtB5D,OAAA,CAACV,QAAQ;oBAEPuC,SAAS,EAAE,WACT+B,CAAC,GAAGgB,MAAM,CAACd,MAAM,GAAG,iBAAiB,GAAG,kCAAkC;kBACzE,GAHEF,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIP,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlC,OAAA;kBAAM6B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAAE8C,MAAM,CAACC;gBAAQ;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5FlC,OAAA;kBAAM6B,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAC5D,IAAIgD,IAAI,CAACF,MAAM,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlC,OAAA;gBAAG6B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE8C,MAAM,CAACK;cAAO;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAjB5DqB,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENlC,OAAA;YAAG6B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACxG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAhSID,iBAAiB;EAAA,QACNZ,SAAS,EAQIM,OAAO;AAAA;AAAAuF,EAAA,GAT/BjF,iBAAiB;AAkSvB,eAAeA,iBAAiB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}