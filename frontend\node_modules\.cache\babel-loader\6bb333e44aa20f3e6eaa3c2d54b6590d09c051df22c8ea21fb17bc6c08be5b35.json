{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const userService = {\n  // Get user profile\n  getProfile: async () => {\n    const response = await api.get('/users/profile');\n    return response.data;\n  },\n  // Update user profile\n  updateProfile: async userData => {\n    const response = await api.put('/users/profile', userData);\n    return response.data;\n  },\n  // Update password\n  updatePassword: async (currentPassword, newPassword) => {\n    await api.put('/users/password', {\n      currentPassword,\n      newPassword\n    });\n  },\n  // Upload avatar\n  uploadAvatar: async file => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    const response = await api.post('/users/avatar', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data.avatarUrl;\n  },\n  // Delete account\n  deleteAccount: async () => {\n    await api.delete('/users/profile');\n  },\n  // Get user statistics\n  getUserStats: async () => {\n    const response = await api.get('/users/stats');\n    return response.data;\n  }\n};\nexport default userService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "userService", "getProfile", "response", "get", "data", "updateProfile", "userData", "put", "updatePassword", "currentPassword", "newPassword", "uploadAvatar", "file", "formData", "FormData", "append", "post", "avatarUrl", "deleteAccount", "delete", "getUserStats"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/userService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { User } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport const userService = {\n  // Get user profile\n  getProfile: async (): Promise<User> => {\n    const response = await api.get('/users/profile');\n    return response.data;\n  },\n\n  // Update user profile\n  updateProfile: async (userData: Partial<User>): Promise<User> => {\n    const response = await api.put('/users/profile', userData);\n    return response.data;\n  },\n\n  // Update password\n  updatePassword: async (currentPassword: string, newPassword: string): Promise<void> => {\n    await api.put('/users/password', {\n      currentPassword,\n      newPassword,\n    });\n  },\n\n  // Upload avatar\n  uploadAvatar: async (file: File): Promise<string> => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    \n    const response = await api.post('/users/avatar', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    \n    return response.data.avatarUrl;\n  },\n\n  // Delete account\n  deleteAccount: async (): Promise<void> => {\n    await api.delete('/users/profile');\n  },\n\n  // Get user statistics\n  getUserStats: async (): Promise<{\n    totalOrders: number;\n    totalSpent: number;\n    wishlistCount: number;\n    reviewsCount: number;\n  }> => {\n    const response = await api.get('/users/stats');\n    return response.data;\n  },\n};\n\nexport default userService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMK,WAAW,GAAG;EACzB;EACAC,UAAU,EAAE,MAAAA,CAAA,KAA2B;IACrC,MAAMC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,gBAAgB,CAAC;IAChD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,aAAa,EAAE,MAAOC,QAAuB,IAAoB;IAC/D,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAACmB,GAAG,CAAC,gBAAgB,EAAED,QAAQ,CAAC;IAC1D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,cAAc,EAAE,MAAAA,CAAOC,eAAuB,EAAEC,WAAmB,KAAoB;IACrF,MAAMtB,GAAG,CAACmB,GAAG,CAAC,iBAAiB,EAAE;MAC/BE,eAAe;MACfC;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,YAAY,EAAE,MAAOC,IAAU,IAAsB;IACnD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;IAE/B,MAAMV,QAAQ,GAAG,MAAMd,GAAG,CAAC4B,IAAI,CAAC,eAAe,EAAEH,QAAQ,EAAE;MACzDtB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,OAAOW,QAAQ,CAACE,IAAI,CAACa,SAAS;EAChC,CAAC;EAED;EACAC,aAAa,EAAE,MAAAA,CAAA,KAA2B;IACxC,MAAM9B,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,CAAC;EACpC,CAAC;EAED;EACAC,YAAY,EAAE,MAAAA,CAAA,KAKR;IACJ,MAAMlB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,cAAc,CAAC;IAC9C,OAAOD,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}