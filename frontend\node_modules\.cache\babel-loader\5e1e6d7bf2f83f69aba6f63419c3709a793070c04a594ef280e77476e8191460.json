{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\SearchResultsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchResultsPage = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const query = searchParams.get('q') || '';\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = query ? await productService.searchProducts(query) : await productService.getProducts();\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load search results');\n        console.error('Error fetching search results:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, [query]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n          children: query ? `Search Results for \"${query}\"` : 'All Products'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mt-2\",\n          children: [products.length, \" product\", products.length !== 1 ? 's' : '', \" found\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg\",\n          children: [\"No products found\", query && ` for \"${query}\"`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n          product: product\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResultsPage, \"BO00Sjh7Y+4EAl6HX7C2lT78QIQ=\", false, function () {\n  return [useSearchParams];\n});\n_c = SearchResultsPage;\nexport default SearchResultsPage;\nvar _c;\n$RefreshReg$(_c, \"SearchResultsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "ProductCard", "productService", "jsxDEV", "_jsxDEV", "SearchResultsPage", "_s", "searchParams", "products", "setProducts", "loading", "setLoading", "error", "setError", "query", "get", "fetchProducts", "results", "searchProducts", "getProducts", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "window", "location", "reload", "map", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/SearchResultsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport ProductCard from '../components/ProductCard';\nimport { productService } from '../services/productService';\n\nconst SearchResultsPage = () => {\n  const [searchParams] = useSearchParams();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  \n  const query = searchParams.get('q') || '';\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const results = query \n          ? await productService.searchProducts(query)\n          : await productService.getProducts();\n        setProducts(results.products || results);\n        setError('');\n      } catch (err) {\n        setError('Failed to load search results');\n        console.error('Error fetching search results:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, [query]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n            {query ? `Search Results for \"${query}\"` : 'All Products'}\n          </h1>\n          {!loading && (\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {products.length} product{products.length !== 1 ? 's' : ''} found\n            </p>\n          )}\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-red-600 dark:text-red-400 mb-4\">{error}</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Try Again\n            </button>\n          </div>\n        ) : products.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-600 dark:text-gray-400 text-lg\">\n              No products found{query && ` for \"${query}\"`}\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {products.map((product) => (\n              <ProductCard key={product._id} product={product} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SearchResultsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EACxC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMgB,KAAK,GAAGP,YAAY,CAACQ,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;EAEzChB,SAAS,CAAC,MAAM;IACd,MAAMiB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMM,OAAO,GAAGH,KAAK,GACjB,MAAMZ,cAAc,CAACgB,cAAc,CAACJ,KAAK,CAAC,GAC1C,MAAMZ,cAAc,CAACiB,WAAW,CAAC,CAAC;QACtCV,WAAW,CAACQ,OAAO,CAACT,QAAQ,IAAIS,OAAO,CAAC;QACxCJ,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZP,QAAQ,CAAC,+BAA+B,CAAC;QACzCQ,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEQ,GAAG,CAAC;MACtD,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACF,KAAK,CAAC,CAAC;EAEX,oBACEV,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5DnB,OAAA;MAAKkB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnB,OAAA;UAAIkB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAChET,KAAK,GAAG,uBAAuBA,KAAK,GAAG,GAAG;QAAc;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,EACJ,CAACjB,OAAO,iBACPN,OAAA;UAAGkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GACjDf,QAAQ,CAACoB,MAAM,EAAC,UAAQ,EAACpB,QAAQ,CAACoB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QAC7D;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELjB,OAAO,gBACNN,OAAA;QAAKkB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDnB,OAAA;UAAKkB,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,GACJf,KAAK,gBACPR,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnB,OAAA;UAAGkB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAEX;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DvB,OAAA;UACEyB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCV,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJnB,QAAQ,CAACoB,MAAM,KAAK,CAAC,gBACvBxB,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCnB,OAAA;UAAGkB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GAAC,mBACrC,EAACT,KAAK,IAAI,SAASA,KAAK,GAAG;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENvB,OAAA;QAAKkB,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFf,QAAQ,CAACyB,GAAG,CAAEC,OAAO,iBACpB9B,OAAA,CAACH,WAAW;UAAmBiC,OAAO,EAAEA;QAAQ,GAA9BA,OAAO,CAACC,GAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAxEID,iBAAiB;EAAA,QACEL,eAAe;AAAA;AAAAoC,EAAA,GADlC/B,iBAAiB;AA0EvB,eAAeA,iBAAiB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}