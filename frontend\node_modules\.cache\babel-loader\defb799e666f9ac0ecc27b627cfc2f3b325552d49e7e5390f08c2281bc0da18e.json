{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: ''\n  });\n  const {\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProduct();\n  }, [id, isAuthenticated]);\n  const handleReviewSubmit = async e => {\n    e.preventDefault();\n    if (!product || !id) return;\n    try {\n      await productService.addReview(id, {\n        rating: reviewForm.rating,\n        comment: reviewForm.comment\n      });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({\n        userName: '',\n        rating: 5,\n        comment: ''\n      });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-4 text-gray-600\",\n        children: \"Loading product...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-600\",\n        children: error || 'Product not found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full overflow-hidden rounded-lg bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: getImageUrl(product.images[selectedImage]),\n            alt: getImageAlt(product.images[selectedImage], product.name),\n            className: \"w-full h-[500px] object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 grid grid-cols-4 gap-2\",\n          children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedImage(index),\n            className: `relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${selectedImage === index ? 'ring-2 ring-primary-500' : 'hover:ring-2 hover:ring-gray-300'}`,\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getImageUrl(image),\n              alt: getImageAlt(image, `${product.name} ${index + 1}`),\n              className: \"h-full w-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n              className: `h-5 w-5 ${star <= product.rating ? 'text-yellow-400' : 'text-gray-200'}`\n            }, star, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-600\",\n            children: [\"(\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Price Comparison\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: product.prices.slice().sort((a, b) => a.price - b.price).map(price => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium\",\n                  children: price.storeName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: formatPriceIndian(price.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: price.productUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"btn-primary\",\n                children: \"Visit Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this)]\n            }, price.storeName, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 whitespace-pre-line\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-6\",\n            children: \"Customer Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleReviewSubmit,\n            className: \"card mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: reviewForm.userName,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    userName: e.target.value\n                  }),\n                  required: true,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 flex items-center gap-1\",\n                  children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => setReviewForm({\n                      ...reviewForm,\n                      rating: star\n                    }),\n                    className: \"p-1 hover:scale-110 transition-transform\",\n                    children: star <= reviewForm.rating ? /*#__PURE__*/_jsxDEV(StarIcon, {\n                      className: \"h-6 w-6 text-yellow-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(StarOutlineIcon, {\n                      className: \"h-6 w-6 text-gray-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 27\n                    }, this)\n                  }, star, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: reviewForm.comment,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    comment: e.target.value\n                  }),\n                  required: true,\n                  rows: 4,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn-primary w-full\",\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: product.reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: review.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: new Date(review.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-1\",\n                children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: `h-4 w-4 ${star <= review.rating ? 'text-yellow-400' : 'text-gray-200'}`\n                }, star, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"hSJMJr3aSIv63Y4ozy0/IjKS/38=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "StarIcon", "StarOutlineIcon", "productService", "wishlistService", "useAuth", "formatPriceIndian", "getImageUrl", "getImageAlt", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "isInWishlist", "setIsInWishlist", "wishlistLoading", "setWishlistLoading", "reviewForm", "setReviewForm", "userName", "rating", "comment", "isAuthenticated", "fetchProduct", "data", "getProduct", "inWishlist", "checkWishlist", "wishlistError", "console", "err", "handleReviewSubmit", "e", "preventDefault", "add<PERSON>eview", "updatedProduct", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "images", "alt", "name", "length", "map", "image", "index", "onClick", "star", "reviews", "prices", "slice", "sort", "a", "b", "price", "storeName", "href", "productUrl", "target", "rel", "description", "onSubmit", "type", "value", "onChange", "required", "rows", "review", "Date", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProductDetailPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon, HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { Product } from '../types';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\nconst ProductDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const [product, setProduct] = useState<Product | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: '',\n  });\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id, isAuthenticated]);\n\n  const handleReviewSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!product || !id) return;\n\n    try {\n      await productService.addReview(id, { rating: reviewForm.rating, comment: reviewForm.comment });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({ userName: '', rating: 5, comment: '' });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">Loading product...</p>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"text-center py-12\">\n        <p className=\"text-red-600\">{error || 'Product not found'}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Image Gallery */}\n        <div>\n          <div className=\"w-full overflow-hidden rounded-lg bg-gray-100\">\n            <img\n              src={getImageUrl(product.images[selectedImage])}\n              alt={getImageAlt(product.images[selectedImage], product.name)}\n              className=\"w-full h-[500px] object-contain\"\n            />\n          </div>\n          {product.images.length > 1 && (\n            <div className=\"mt-4 grid grid-cols-4 gap-2\">\n              {product.images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImage(index)}\n                  className={`relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${\n                    selectedImage === index\n                      ? 'ring-2 ring-primary-500'\n                      : 'hover:ring-2 hover:ring-gray-300'\n                  }`}\n                >\n                  <img\n                    src={getImageUrl(image)}\n                    alt={getImageAlt(image, `${product.name} ${index + 1}`)}\n                    className=\"h-full w-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{product.name}</h1>\n\n          {/* Rating */}\n          <div className=\"mt-4 flex items-center\">\n            <div className=\"flex items-center\">\n              {[1, 2, 3, 4, 5].map((star) => (\n                <StarIcon\n                  key={star}\n                  className={`h-5 w-5 ${\n                    star <= product.rating\n                      ? 'text-yellow-400'\n                      : 'text-gray-200'\n                  }`}\n                />\n              ))}\n            </div>\n            <span className=\"ml-2 text-gray-600\">\n              ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          {/* Price Comparison */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Price Comparison</h2>\n            <div className=\"space-y-4\">\n              {product.prices\n                .slice()\n                .sort((a, b) => a.price - b.price)\n                .map((price) => (\n                  <div\n                    key={price.storeName}\n                    className=\"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\"\n                  >\n                    <div>\n                      <h3 className=\"font-medium\">{price.storeName}</h3>\n                      <p className=\"text-2xl font-bold\">\n                        {formatPriceIndian(price.price)}\n                      </p>\n                    </div>\n                    <a\n                      href={price.productUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"btn-primary\"\n                    >\n                      Visit Store\n                    </a>\n                  </div>\n                ))}\n            </div>\n          </div>\n\n          {/* Description */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Description</h2>\n            <p className=\"text-gray-600 whitespace-pre-line\">\n              {product.description}\n            </p>\n          </div>\n\n          {/* Reviews */}\n          <div className=\"mt-12\">\n            <h2 className=\"text-xl font-semibold mb-6\">Customer Reviews</h2>\n\n            {/* Review Form */}\n            <form onSubmit={handleReviewSubmit} className=\"card mb-8\">\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={reviewForm.userName}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, userName: e.target.value })\n                    }\n                    required\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Rating\n                  </label>\n                  <div className=\"mt-1 flex items-center gap-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <button\n                        key={star}\n                        type=\"button\"\n                        onClick={() =>\n                          setReviewForm({ ...reviewForm, rating: star })\n                        }\n                        className=\"p-1 hover:scale-110 transition-transform\"\n                      >\n                        {star <= reviewForm.rating ? (\n                          <StarIcon className=\"h-6 w-6 text-yellow-400\" />\n                        ) : (\n                          <StarOutlineIcon className=\"h-6 w-6 text-gray-300\" />\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Review\n                  </label>\n                  <textarea\n                    value={reviewForm.comment}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, comment: e.target.value })\n                    }\n                    required\n                    rows={4}\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <button type=\"submit\" className=\"btn-primary w-full\">\n                  Submit Review\n                </button>\n              </div>\n            </form>\n\n            {/* Reviews List */}\n            <div className=\"space-y-6\">\n              {product.reviews.map((review, index) => (\n                <div key={index} className=\"card\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">{review.userName}</span>\n                    <span className=\"text-sm text-gray-500\">\n                      {new Date(review.createdAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center mt-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <StarIcon\n                        key={star}\n                        className={`h-4 w-4 ${\n                          star <= review.rating\n                            ? 'text-yellow-400'\n                            : 'text-gray-200'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                  <p className=\"mt-2 text-gray-600\">{review.comment}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASA,QAAQ,IAAIC,eAAe,QAAmB,6BAA6B;AAGpF,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAG,CAAC,GAAGb,SAAS,CAAiB,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC;IAC3C6B,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC;EAAgB,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAErCP,SAAS,CAAC,MAAM;IACd,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACnB,EAAE,EAAE;MACTI,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMgB,IAAI,GAAG,MAAM9B,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;QAChDE,UAAU,CAACkB,IAAI,CAAC;QAChBd,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAIY,eAAe,EAAE;UACnB,IAAI;YACF,MAAMI,UAAU,GAAG,MAAM/B,eAAe,CAACgC,aAAa,CAACvB,EAAE,CAAC;YAC1DU,eAAe,CAACY,UAAU,CAAC;UAC7B,CAAC,CAAC,OAAOE,aAAa,EAAE;YACtBC,OAAO,CAACpB,KAAK,CAAC,iCAAiC,EAAEmB,aAAa,CAAC;UACjE;QACF;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZpB,QAAQ,CAAC,iDAAiD,CAAC;QAC3DmB,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDe,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACnB,EAAE,EAAEkB,eAAe,CAAC,CAAC;EAEzB,MAAMS,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC5B,OAAO,IAAI,CAACD,EAAE,EAAE;IAErB,IAAI;MACF,MAAMV,cAAc,CAACwC,SAAS,CAAC9B,EAAE,EAAE;QAAEgB,MAAM,EAAEH,UAAU,CAACG,MAAM;QAAEC,OAAO,EAAEJ,UAAU,CAACI;MAAQ,CAAC,CAAC;MAC9F;MACA,MAAMc,cAAc,GAAG,MAAMzC,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;MAC1DE,UAAU,CAAC6B,cAAc,CAAC;MAC1BjB,aAAa,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZD,OAAO,CAACpB,KAAK,CAAC,0BAA0B,EAAEqB,GAAG,CAAC;MAC9CM,KAAK,CAAC,4CAA4C,CAAC;IACrD;EACF,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKoC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrC,OAAA;QAAKoC,SAAS,EAAC;MAA4F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClHzC,OAAA;QAAGoC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,IAAIjC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEJ,OAAA;MAAKoC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrC,OAAA;QAAGoC,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE7B,KAAK,IAAI;MAAmB;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,oBACEzC,OAAA;IAAKoC,SAAS,EAAC,gHAAgH;IAAAC,QAAA,eAC7HrC,OAAA;MAAKoC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrC,OAAA;QAAAqC,QAAA,gBACErC,OAAA;UAAKoC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DrC,OAAA;YACE0C,GAAG,EAAE7C,WAAW,CAACO,OAAO,CAACuC,MAAM,CAACjC,aAAa,CAAC,CAAE;YAChDkC,GAAG,EAAE9C,WAAW,CAACM,OAAO,CAACuC,MAAM,CAACjC,aAAa,CAAC,EAAEN,OAAO,CAACyC,IAAI,CAAE;YAC9DT,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACLrC,OAAO,CAACuC,MAAM,CAACG,MAAM,GAAG,CAAC,iBACxB9C,OAAA;UAAKoC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCjC,OAAO,CAACuC,MAAM,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BjD,OAAA;YAEEkD,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAACsC,KAAK,CAAE;YACvCb,SAAS,EAAE,iEACT1B,aAAa,KAAKuC,KAAK,GACnB,yBAAyB,GACzB,kCAAkC,EACrC;YAAAZ,QAAA,eAEHrC,OAAA;cACE0C,GAAG,EAAE7C,WAAW,CAACmD,KAAK,CAAE;cACxBJ,GAAG,EAAE9C,WAAW,CAACkD,KAAK,EAAE,GAAG5C,OAAO,CAACyC,IAAI,IAAII,KAAK,GAAG,CAAC,EAAE,CAAE;cACxDb,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC,GAZGQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzC,OAAA;QAAAqC,QAAA,gBACErC,OAAA;UAAIoC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEjC,OAAO,CAACyC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGpEzC,OAAA;UAAKoC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrC,OAAA;YAAKoC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBnD,OAAA,CAACT,QAAQ;cAEP6C,SAAS,EAAE,WACTe,IAAI,IAAI/C,OAAO,CAACe,MAAM,GAClB,iBAAiB,GACjB,eAAe;YAClB,GALEgC,IAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzC,OAAA;YAAMoC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAACjC,OAAO,CAACgD,OAAO,CAACN,MAAM,EAAC,WAC3B;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrC,OAAA;YAAIoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEzC,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBjC,OAAO,CAACiD,MAAM,CACZC,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC,CACjCX,GAAG,CAAEW,KAAK,iBACT1D,OAAA;cAEEoC,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHrC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAIoC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEqB,KAAK,CAACC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClDzC,OAAA;kBAAGoC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC9BzC,iBAAiB,CAAC8D,KAAK,CAACA,KAAK;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNzC,OAAA;gBACE4D,IAAI,EAAEF,KAAK,CAACG,UAAW;gBACvBC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB3B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,GAhBCiB,KAAK,CAACC,SAAS;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBjB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrC,OAAA;YAAIoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DzC,OAAA;YAAGoC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC7CjC,OAAO,CAAC4D;UAAW;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBrC,OAAA;YAAIoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGhEzC,OAAA;YAAMiE,QAAQ,EAAEnC,kBAAmB;YAACM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACvDrC,OAAA;cAAKoC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAOoC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRzC,OAAA;kBACEkE,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEnD,UAAU,CAACE,QAAS;kBAC3BkD,QAAQ,EAAGrC,CAAC,IACVd,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,QAAQ,EAAEa,CAAC,CAAC+B,MAAM,CAACK;kBAAM,CAAC,CAC1D;kBACDE,QAAQ;kBACRjC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAOoC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRzC,OAAA;kBAAKoC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBnD,OAAA;oBAEEkE,IAAI,EAAC,QAAQ;oBACbhB,OAAO,EAAEA,CAAA,KACPjC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEG,MAAM,EAAEgC;oBAAK,CAAC,CAC9C;oBACDf,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAEnDc,IAAI,IAAInC,UAAU,CAACG,MAAM,gBACxBnB,OAAA,CAACT,QAAQ;sBAAC6C,SAAS,EAAC;oBAAyB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEhDzC,OAAA,CAACR,eAAe;sBAAC4C,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrD,GAXIU,IAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYH,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAOoC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRzC,OAAA;kBACEmE,KAAK,EAAEnD,UAAU,CAACI,OAAQ;kBAC1BgD,QAAQ,EAAGrC,CAAC,IACVd,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEI,OAAO,EAAEW,CAAC,CAAC+B,MAAM,CAACK;kBAAM,CAAC,CACzD;kBACDE,QAAQ;kBACRC,IAAI,EAAE,CAAE;kBACRlC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzC,OAAA;gBAAQkE,IAAI,EAAC,QAAQ;gBAAC9B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPzC,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBjC,OAAO,CAACgD,OAAO,CAACL,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK,kBACjCjD,OAAA;cAAiBoC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC/BrC,OAAA;gBAAKoC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDrC,OAAA;kBAAMoC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEkC,MAAM,CAACrD;gBAAQ;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDzC,OAAA;kBAAMoC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpC,IAAImC,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNzC,OAAA;gBAAKoC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAEI,IAAI,iBACxBnD,OAAA,CAACT,QAAQ;kBAEP6C,SAAS,EAAE,WACTe,IAAI,IAAIoB,MAAM,CAACpD,MAAM,GACjB,iBAAiB,GACjB,eAAe;gBAClB,GALEgC,IAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMV,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzC,OAAA;gBAAGoC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEkC,MAAM,CAACnD;cAAO;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAnB9CQ,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAhRID,iBAA2B;EAAA,QAChBX,SAAS,EAYIK,OAAO;AAAA;AAAAgF,EAAA,GAb/B1E,iBAA2B;AAkRjC,eAAeA,iBAAiB;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}