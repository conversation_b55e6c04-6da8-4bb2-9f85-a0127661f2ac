{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage first, then system preference\n    const saved = localStorage.getItem('darkMode');\n    if (saved !== null) {\n      return JSON.parse(saved);\n    }\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n  useEffect(() => {\n    // Save to localStorage\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n\n    // Apply to document\n    if (isDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [isDarkMode]);\n  const toggleDarkMode = () => {\n    setIsDarkMode(!isDarkMode);\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: {\n      isDarkMode,\n      toggleDarkMode\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeProvider, \"JIAjlZOqizWYtRiSED6xcbEzkOQ=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "ThemeProvider", "children", "_s", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "window", "matchMedia", "matches", "setItem", "stringify", "document", "documentElement", "classList", "add", "remove", "toggleDarkMode", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\n\ninterface ThemeContextType {\n  isDarkMode: boolean;\n  toggleDarkMode: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage first, then system preference\n    const saved = localStorage.getItem('darkMode');\n    if (saved !== null) {\n      return JSON.parse(saved);\n    }\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n\n  useEffect(() => {\n    // Save to localStorage\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n    \n    // Apply to document\n    if (isDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [isDarkMode]);\n\n  const toggleDarkMode = () => {\n    setIsDarkMode(!isDarkMode);\n  };\n\n  return (\n    <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO9E,MAAMC,YAAY,gBAAGN,aAAa,CAA+BO,SAAS,CAAC;AAE3E,OAAO,MAAMC,aAAsD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,MAAM;IACjD;IACA,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,IAAIF,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;IAC1B;IACA,OAAOK,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;EAClE,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACd;IACAY,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEL,IAAI,CAACM,SAAS,CAACX,UAAU,CAAC,CAAC;;IAE5D;IACA,IAAIA,UAAU,EAAE;MACdY,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IACnD;EACF,CAAC,EAAE,CAAChB,UAAU,CAAC,CAAC;EAEhB,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3BhB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,oBACEN,OAAA,CAACC,YAAY,CAACuB,QAAQ;IAACC,KAAK,EAAE;MAAEnB,UAAU;MAAEiB;IAAe,CAAE;IAAAnB,QAAA,EAC1DA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACxB,EAAA,CA/BWF,aAAsD;AAAA2B,EAAA,GAAtD3B,aAAsD;AAiCnE,OAAO,MAAM4B,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGrC,UAAU,CAACK,YAAY,CAAC;EACxC,IAAIgC,OAAO,KAAK/B,SAAS,EAAE;IACzB,MAAM,IAAIgC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}