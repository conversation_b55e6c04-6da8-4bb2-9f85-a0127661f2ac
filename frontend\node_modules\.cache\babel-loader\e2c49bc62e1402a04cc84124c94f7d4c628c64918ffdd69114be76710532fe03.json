{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\UserStatsContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\nimport { useUserStats } from '../hooks/useUserStats';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserStatsContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * UserStatsProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const UserStatsProvider = ({\n  children\n}) => {\n  _s();\n  const userStatsHook = useUserStats();\n  return /*#__PURE__*/_jsxDEV(UserStatsContext.Provider, {\n    value: userStatsHook,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n\n/**\n * Hook to use user stats context\n * @returns {Object} User stats context value\n */\n_s(UserStatsProvider, \"EEUMaK0mc9MPWiww4PWwoIJVLMU=\", false, function () {\n  return [useUserStats];\n});\n_c = UserStatsProvider;\nexport const useUserStatsContext = () => {\n  _s2();\n  const context = useContext(UserStatsContext);\n  if (context === undefined) {\n    throw new Error('useUserStatsContext must be used within a UserStatsProvider');\n  }\n  return context;\n};\n_s2(useUserStatsContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default UserStatsContext;\nvar _c;\n$RefreshReg$(_c, \"UserStatsProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useUserStats", "jsxDEV", "_jsxDEV", "UserStatsContext", "undefined", "UserStatsProvider", "children", "_s", "userStatsHook", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useUserStatsContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/UserStatsContext.js"], "sourcesContent": ["import React, { createContext, useContext } from 'react';\nimport { useUserStats } from '../hooks/useUserStats';\n\nconst UserStatsContext = createContext(undefined);\n\n/**\n * UserStatsProvider component\n * @param {Object} props\n * @param {React.ReactNode} props.children\n */\nexport const UserStatsProvider = ({ children }) => {\n  const userStatsHook = useUserStats();\n\n  return (\n    <UserStatsContext.Provider value={userStatsHook}>\n      {children}\n    </UserStatsContext.Provider>\n  );\n};\n\n/**\n * Hook to use user stats context\n * @returns {Object} User stats context value\n */\nexport const useUserStatsContext = () => {\n  const context = useContext(UserStatsContext);\n  if (context === undefined) {\n    throw new Error('useUserStatsContext must be used within a UserStatsProvider');\n  }\n  return context;\n};\n\nexport default UserStatsContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,gBAAgB,gBAAGL,aAAa,CAACM,SAAS,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAMC,aAAa,GAAGR,YAAY,CAAC,CAAC;EAEpC,oBACEE,OAAA,CAACC,gBAAgB,CAACM,QAAQ;IAACC,KAAK,EAAEF,aAAc;IAAAF,QAAA,EAC7CA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;;AAED;AACA;AACA;AACA;AAHAP,EAAA,CAVaF,iBAAiB;EAAA,QACNL,YAAY;AAAA;AAAAe,EAAA,GADvBV,iBAAiB;AAc9B,OAAO,MAAMW,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvC,MAAMC,OAAO,GAAGnB,UAAU,CAACI,gBAAgB,CAAC;EAC5C,IAAIe,OAAO,KAAKd,SAAS,EAAE;IACzB,MAAM,IAAIe,KAAK,CAAC,6DAA6D,CAAC;EAChF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,mBAAmB;AAQhC,eAAeb,gBAAgB;AAAC,IAAAY,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}