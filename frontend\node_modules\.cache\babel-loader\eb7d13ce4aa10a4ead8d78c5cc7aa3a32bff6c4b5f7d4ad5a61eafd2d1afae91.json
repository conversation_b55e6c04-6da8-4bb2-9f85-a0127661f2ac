{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\CategoryPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { productService } from '../services/productService';\nimport ProductCard from '../components/ProductCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryPage = () => {\n  _s();\n  const {\n    category\n  } = useParams();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const data = await productService.getProductsByCategory(category || '');\n        setProducts(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, [category]);\n  if (loading) return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500 text-center\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8 capitalize\",\n      children: category\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-center text-gray-500\",\n      children: \"No products found in this category.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        product: product\n      }, product._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryPage, \"Xs+fStdHynfYjijX7CW6jrNs9Qc=\", false, function () {\n  return [useParams];\n});\n_c = CategoryPage;\nexport default CategoryPage;\nvar _c;\n$RefreshReg$(_c, \"CategoryPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "productService", "ProductCard", "LoadingSpinner", "jsxDEV", "_jsxDEV", "CategoryPage", "_s", "category", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "data", "getProductsByCategory", "err", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "length", "map", "product", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/CategoryPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Product } from '../types';\nimport { productService } from '../services/productService';\nimport ProductCard from '../components/ProductCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst CategoryPage = () => {\n  const { category } = useParams<{ category: string }>();\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const data = await productService.getProductsByCategory(category || '');\n        setProducts(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load products');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, [category]);\n\n  if (loading) return <LoadingSpinner />;\n  if (error) return <div className=\"text-red-500 text-center\">{error}</div>;\n\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold mb-8 capitalize\">{category}</h1>\n      {products.length === 0 ? (\n        <p className=\"text-center text-gray-500\">No products found in this category.</p>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <ProductCard key={product._id} product={product} />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CategoryPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAE5C,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGR,SAAS,CAAuB,CAAC;EACtD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,MAAMiB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,IAAI,GAAG,MAAMf,cAAc,CAACgB,qBAAqB,CAACT,QAAQ,IAAI,EAAE,CAAC;QACvEE,WAAW,CAACM,IAAI,CAAC;QACjBF,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZJ,QAAQ,CAAC,yBAAyB,CAAC;MACrC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,IAAIG,OAAO,EAAE,oBAAON,OAAA,CAACF,cAAc;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtC,IAAIT,KAAK,EAAE,oBAAOR,OAAA;IAAKkB,SAAS,EAAC,0BAA0B;IAAAC,QAAA,EAAEX;EAAK;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAEzE,oBACEjB,OAAA;IAAAmB,QAAA,gBACEnB,OAAA;MAAIkB,SAAS,EAAC,oCAAoC;MAAAC,QAAA,EAAEhB;IAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EACjEb,QAAQ,CAACgB,MAAM,KAAK,CAAC,gBACpBpB,OAAA;MAAGkB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAmC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAEhFjB,OAAA;MAAKkB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFf,QAAQ,CAACiB,GAAG,CAAEC,OAAO,iBACpBtB,OAAA,CAACH,WAAW;QAAmByB,OAAO,EAAEA;MAAQ,GAA9BA,OAAO,CAACC,GAAG;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACf,EAAA,CAxCID,YAAY;EAAA,QACKN,SAAS;AAAA;AAAA6B,EAAA,GAD1BvB,YAAY;AA0ClB,eAAeA,YAAY;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}