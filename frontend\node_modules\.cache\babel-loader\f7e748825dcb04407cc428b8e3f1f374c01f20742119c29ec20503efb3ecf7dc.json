{"ast": null, "code": "// Mock data for development\nconst mockProducts = [{\n  _id: '1',\n  name: 'Sony WH-1000XM4 Wireless Headphones',\n  description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg', 'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 29058,\n    productUrl: 'https://www.amazon.in/Sony-WH-1000XM4-Cancelling-Headphones-Bluetooth/dp/B0863TXGM3',\n    inStock: true,\n    lastUpdated: new Date()\n  }, {\n    storeName: 'Flipkart',\n    price: 29224,\n    productUrl: 'https://www.flipkart.com/sony-wh-1000xm4-bluetooth-headphones/p/itm9f84f49ad6ac8',\n    inStock: true,\n    lastUpdated: new Date()\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: '<PERSON>',\n    rating: 5,\n    comment: 'Best headphones I have ever owned!',\n    createdAt: new Date('2025-04-14')\n  }, {\n    userName: 'Priya <PERSON>',\n    rating: 5,\n    comment: 'Excellent sound quality and battery life. Worth every rupee!',\n    createdAt: new Date('2025-04-12')\n  }, {\n    userName: 'Rahul Mehta',\n    rating: 4,\n    comment: 'Great for work from home. Noise cancellation is amazing.',\n    createdAt: new Date('2025-04-10')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '2',\n  name: 'Samsung 55\" 4K Smart TV',\n  description: 'Crystal clear display with smart features',\n  category: 'Electronics',\n  images: ['https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 41582,\n    productUrl: 'https://www.amazon.in/gaming-laptops/s?k=gaming+laptops'\n  }, {\n    storeName: 'Flipkart',\n    price: 41749,\n    productUrl: 'https://www.flipkart.com/gaming-laptops/pr?sid=6bo%2Cb5g'\n  }],\n  rating: 4.5,\n  reviews: [{\n    userName: 'Amit Kumar',\n    rating: 5,\n    comment: 'Amazing laptop! Perfect for gaming and work.',\n    createdAt: new Date('2025-04-13')\n  }, {\n    userName: 'Sneha Patel',\n    rating: 4,\n    comment: 'Good performance but gets a bit warm during heavy usage.',\n    createdAt: new Date('2025-04-11')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '5',\n  name: 'Nike Air Max 270',\n  description: 'Comfortable athletic shoes with Air cushioning',\n  category: 'Fashion',\n  images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n  prices: [{\n    storeName: 'Nike',\n    price: 12525,\n    productUrl: 'https://www.nike.com/in/t/air-max-270-mens-shoes-KkLcGR/AH8050-002',\n    inStock: true,\n    lastUpdated: new Date()\n  }, {\n    storeName: 'Amazon',\n    price: 12524,\n    productUrl: 'https://amazon.in',\n    inStock: true,\n    lastUpdated: new Date()\n  }],\n  rating: 4.6,\n  reviews: [{\n    userName: 'Ravi Singh',\n    rating: 5,\n    comment: 'Super comfortable shoes! Great for running and daily wear.',\n    createdAt: new Date('2025-04-09')\n  }, {\n    userName: 'Neha Gupta',\n    rating: 4,\n    comment: 'Good quality but sizing runs a bit small.',\n    createdAt: new Date('2025-04-08')\n  }, {\n    userName: 'Karan Joshi',\n    rating: 5,\n    comment: 'Love the design and comfort. Highly recommended!',\n    createdAt: new Date('2025-04-07')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '6',\n  name: 'Levi\\'s 501 Original Fit Jeans',\n  description: 'Classic straight leg jeans',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 5009,\n    productUrl: 'https://amazon.in'\n  }, {\n    storeName: 'Myntra',\n    price: 5803,\n    productUrl: 'https://myntra.com'\n  }],\n  rating: 4.5,\n  reviews: [{\n    userName: 'Deepika Rao',\n    rating: 4,\n    comment: 'Good quality jeans. Fit is perfect and comfortable.',\n    createdAt: new Date('2025-04-06')\n  }, {\n    userName: 'Suresh Kumar',\n    rating: 5,\n    comment: 'Classic Levis quality. These jeans last for years!',\n    createdAt: new Date('2025-04-05')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '7',\n  name: 'Ray-Ban Aviator Sunglasses',\n  description: 'Classic aviator style with UV protection',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1572635196237-14b3f281503f?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 154.00,\n    productUrl: 'https://www.amazon.in/Ray-Ban-Aviator-Sunglasses/dp/B001GNBJQ4'\n  }, {\n    storeName: 'Sunglass Hut',\n    price: 169.00,\n    productUrl: 'https://www.sunglasshut.com/us/ray-ban-aviator-classic-gold/8052896020578'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Arjun Malhotra',\n    rating: 5,\n    comment: 'Classic aviators! Great quality and style.',\n    createdAt: new Date('2025-04-04')\n  }, {\n    userName: 'Pooja Agarwal',\n    rating: 4,\n    comment: 'Good sunglasses but a bit pricey. UV protection is excellent.',\n    createdAt: new Date('2025-04-03')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '8',\n  name: 'Casio G-Shock Watch',\n  description: 'Durable digital watch with multiple features',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1533139502658-0198f920d8e8?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Amazon',\n    price: 99.00,\n    productUrl: 'https://www.amazon.in/Casio-G-Shock-Digital-Watch/dp/B07DQZQZQZ'\n  }, {\n    storeName: 'Casio',\n    price: 110.00,\n    productUrl: 'https://www.casio.com/us/watches/gshock/product.GA2100-1A/'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Vikash Sharma',\n    rating: 5,\n    comment: 'Tough watch! Perfect for outdoor activities.',\n    createdAt: new Date('2025-04-02')\n  }, {\n    userName: 'Manisha Jain',\n    rating: 5,\n    comment: 'Bought for my husband. He loves all the features!',\n    createdAt: new Date('2025-04-01')\n  }, {\n    userName: 'Rohit Verma',\n    rating: 4,\n    comment: 'Good watch but takes time to learn all functions.',\n    createdAt: new Date('2025-03-30')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '17',\n  name: 'Adidas Ultraboost 21',\n  description: 'Premium running shoes with responsive cushioning',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Adidas',\n    price: 180.00,\n    productUrl: 'https://www.adidas.com/us/ultraboost-21-shoes/FY0377.html'\n  }, {\n    storeName: 'Amazon',\n    price: 169.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Sanjay Reddy',\n    rating: 5,\n    comment: 'Best running shoes I have ever owned! Great cushioning.',\n    createdAt: new Date('2025-03-28')\n  }, {\n    userName: 'Kavita Nair',\n    rating: 5,\n    comment: 'Perfect for marathon training. Very comfortable.',\n    createdAt: new Date('2025-03-26')\n  }, {\n    userName: 'Rajesh Gupta',\n    rating: 4,\n    comment: 'Good shoes but a bit expensive. Quality is excellent.',\n    createdAt: new Date('2025-03-24')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '18',\n  name: 'Michael Kors Leather Crossbody Bag',\n  description: 'Elegant leather bag with adjustable strap',\n  category: 'Fashion',\n  images: ['https://images.unsplash.com/photo-1548036328-c9fa89d128fa?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Michael Kors',\n    price: 298.00,\n    productUrl: 'https://www.michaelkors.com/leather-crossbody-bag/_/R-30F2G7CM2L'\n  }, {\n    storeName: 'Amazon',\n    price: 278.95,\n    productUrl: 'https://amazon.com'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Priyanka Singh',\n    rating: 5,\n    comment: 'Beautiful bag! Perfect size for daily use.',\n    createdAt: new Date('2025-03-22')\n  }, {\n    userName: 'Anjali Sharma',\n    rating: 4,\n    comment: 'Good quality leather. A bit pricey but worth it.',\n    createdAt: new Date('2025-03-20')\n  }, {\n    userName: 'Meera Patel',\n    rating: 5,\n    comment: 'Love the design and quality. Highly recommended!',\n    createdAt: new Date('2025-03-18')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n},\n// Home & Garden Category\n{\n  _id: '19',\n  name: 'Philips Hue Smart LED Bulb Set',\n  description: 'Color-changing smart LED bulbs with wireless control',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1563461660947-507ef49e9c47?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Philips',\n    price: 199.99,\n    productUrl: 'https://www.philips-hue.com/en-us/p/hue-white-and-color-ambiance-starter-kit-e26/046677555334'\n  }, {\n    storeName: 'Amazon',\n    price: 189.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 194.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Ravi Kumar',\n    rating: 5,\n    comment: 'Amazing smart lights! Easy to set up and control.',\n    createdAt: new Date('2025-03-16')\n  }, {\n    userName: 'Sunita Joshi',\n    rating: 4,\n    comment: 'Good product but app could be better. Colors are vibrant.',\n    createdAt: new Date('2025-03-14')\n  }, {\n    userName: 'Anil Verma',\n    rating: 5,\n    comment: 'Perfect for mood lighting. Great value for money.',\n    createdAt: new Date('2025-03-12')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '20',\n  name: 'KitchenAid Stand Mixer',\n  description: 'Professional 5-quart stand mixer with multiple attachments',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1544441893-675973e31985?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'KitchenAid',\n    price: 399.99,\n    productUrl: 'https://www.kitchenaid.com/countertop-appliances/stand-mixers/tilt-head-stand-mixers/p.artisan-series-5-quart-tilt-head-stand-mixer.ksm150psob.html'\n  }, {\n    storeName: 'Amazon',\n    price: 379.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Williams Sonoma',\n    price: 429.99,\n    productUrl: 'https://www.williams-sonoma.com/products/kitchenaid-artisan-stand-mixer/'\n  }],\n  rating: 4.9,\n  reviews: [{\n    userName: 'Lakshmi Iyer',\n    rating: 5,\n    comment: 'Excellent mixer! Makes baking so much easier.',\n    createdAt: new Date('2025-03-10')\n  }, {\n    userName: 'Rajesh Agarwal',\n    rating: 5,\n    comment: 'Heavy duty and reliable. Perfect for my bakery.',\n    createdAt: new Date('2025-03-08')\n  }, {\n    userName: 'Nisha Kapoor',\n    rating: 4,\n    comment: 'Great quality but quite expensive. Worth the investment.',\n    createdAt: new Date('2025-03-06')\n  }, {\n    userName: 'Suresh Reddy',\n    rating: 5,\n    comment: 'Best kitchen appliance I have ever bought!',\n    createdAt: new Date('2025-03-04')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '21',\n  name: 'DEWALT Power Tool Set',\n  description: '20V MAX Cordless Drill Combo Kit with 2 batteries',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1581244277943-fe4a9c777189?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Home Depot',\n    price: 299.00,\n    productUrl: 'https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-Charger-and-Bag-DCK240C2/204373168'\n  }, {\n    storeName: 'Amazon',\n    price: 289.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Lowes',\n    price: 294.99,\n    productUrl: 'https://www.lowes.com/pd/DEWALT-20-Volt-Max-2-Tool-Combo-Kit/1000191169'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Manoj Singh',\n    rating: 5,\n    comment: 'Excellent tools! Great for home projects.',\n    createdAt: new Date('2025-03-02')\n  }, {\n    userName: 'Kavya Nair',\n    rating: 4,\n    comment: 'Good quality but batteries could last longer.',\n    createdAt: new Date('2025-02-28')\n  }, {\n    userName: 'Ashok Kumar',\n    rating: 5,\n    comment: 'Professional grade tools. Highly recommended!',\n    createdAt: new Date('2025-02-26')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '22',\n  name: 'Dyson V15 Detect Vacuum',\n  description: 'Cordless vacuum with laser dust detection',\n  category: 'Home & Garden',\n  images: ['https://images.unsplash.com/photo-1558317374-067fb5f30001?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Dyson',\n    price: 699.99,\n    productUrl: 'https://www.dyson.com/vacuum-cleaners/stick-vacuum-cleaners/dyson-v15-detect-nickel-yellow'\n  }, {\n    storeName: 'Amazon',\n    price: 679.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Best Buy',\n    price: 689.99,\n    productUrl: 'https://bestbuy.com'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Shweta Sharma',\n    rating: 5,\n    comment: 'Amazing vacuum! The laser detection is incredible.',\n    createdAt: new Date('2025-02-24')\n  }, {\n    userName: 'Vinod Gupta',\n    rating: 4,\n    comment: 'Powerful suction but quite expensive.',\n    createdAt: new Date('2025-02-22')\n  }, {\n    userName: 'Rekha Patel',\n    rating: 5,\n    comment: 'Best vacuum cleaner I have ever used!',\n    createdAt: new Date('2025-02-20')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n},\n// Sports Category\n{\n  _id: '23',\n  name: 'NordicTrack Commercial 1750 Treadmill',\n  description: 'Smart treadmill with 14-inch HD touchscreen and iFit integration',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1595078475328-1ab05d0a6a0e?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'NordicTrack',\n    price: 1899.00,\n    productUrl: 'https://www.nordictrack.com/treadmills/commercial-1750-treadmill'\n  }, {\n    storeName: 'Amazon',\n    price: 1799.00,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Dick\\'s Sporting Goods',\n    price: 1849.99,\n    productUrl: 'https://dickssportinggoods.com'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Fitness Guru',\n    rating: 5,\n    comment: 'Excellent treadmill! Perfect for home workouts.',\n    createdAt: new Date('2025-02-18')\n  }, {\n    userName: 'Ramesh Joshi',\n    rating: 4,\n    comment: 'Good machine but takes up a lot of space.',\n    createdAt: new Date('2025-02-16')\n  }, {\n    userName: 'Sneha Reddy',\n    rating: 5,\n    comment: 'Great investment for health. Love the iFit features.',\n    createdAt: new Date('2025-02-14')\n  }, {\n    userName: 'Kiran Kumar',\n    rating: 4,\n    comment: 'Solid build quality. Assembly was a bit challenging.',\n    createdAt: new Date('2025-02-12')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '24',\n  name: 'Wilson Evolution Basketball',\n  description: 'Official size indoor game basketball with moisture-wicking technology',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1519861531473-9200262188bf?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Wilson',\n    price: 59.99,\n    productUrl: 'https://www.wilson.com/en-us/product/evolution-basketball-WTB0516'\n  }, {\n    storeName: 'Amazon',\n    price: 54.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'Dick\\'s Sporting Goods',\n    price: 57.99,\n    productUrl: 'https://dickssportinggoods.com'\n  }],\n  rating: 4.9,\n  reviews: [{\n    userName: 'Basketball Pro',\n    rating: 5,\n    comment: 'Perfect basketball! Great grip and bounce.',\n    createdAt: new Date('2025-02-10')\n  }, {\n    userName: 'Arjun Singh',\n    rating: 5,\n    comment: 'Best basketball for indoor games. Highly recommended!',\n    createdAt: new Date('2025-02-08')\n  }, {\n    userName: 'Rohit Sharma',\n    rating: 4,\n    comment: 'Good quality ball. Worth the price.',\n    createdAt: new Date('2025-02-06')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}, {\n  _id: '25',\n  name: 'Garmin Forerunner 945 GPS Watch',\n  description: 'Advanced running watch with full-color mapping and training metrics',\n  category: 'Sports',\n  images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?auto=format&fit=crop&w=800'],\n  prices: [{\n    storeName: 'Garmin',\n    price: 599.99,\n    productUrl: 'https://garmin.com'\n  }, {\n    storeName: 'Amazon',\n    price: 579.95,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'REI',\n    price: 589.99,\n    productUrl: 'https://rei.com'\n  }],\n  rating: 4.7,\n  reviews: [{\n    userName: 'Tech Runner',\n    rating: 5,\n    comment: 'Amazing GPS watch! Perfect for serious runners.',\n    createdAt: new Date('2025-02-04')\n  }, {\n    userName: 'Marathon Man',\n    rating: 4,\n    comment: 'Great features but battery life could be better.',\n    createdAt: new Date('2025-02-02')\n  }, {\n    userName: 'Fitness Freak',\n    rating: 5,\n    comment: 'Best running watch in the market. Love the mapping!',\n    createdAt: new Date('2025-01-31')\n  }, {\n    userName: 'Priya Runner',\n    rating: 4,\n    comment: 'Good watch but takes time to learn all features.',\n    createdAt: new Date('2025-01-29')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: true\n}, {\n  _id: '26',\n  name: 'Yeti Tundra 45 Cooler',\n  description: 'Premium hard cooler for outdoor activities and camping',\n  category: 'Sports',\n  images: ['https://picsum.photos/id/26/800/800'],\n  prices: [{\n    storeName: 'Yeti',\n    price: 325.00,\n    productUrl: 'https://www.yeti.com/en_US/coolers/hard-coolers/tundra-45-cooler/YT45.html'\n  }, {\n    storeName: 'Amazon',\n    price: 299.99,\n    productUrl: 'https://amazon.com'\n  }, {\n    storeName: 'REI',\n    price: 319.99,\n    productUrl: 'https://rei.com'\n  }],\n  rating: 4.8,\n  reviews: [{\n    userName: 'Outdoor Explorer',\n    rating: 5,\n    comment: 'Best cooler for camping! Keeps ice for days.',\n    createdAt: new Date('2025-01-27')\n  }, {\n    userName: 'Adventure Seeker',\n    rating: 5,\n    comment: 'Premium quality cooler. Worth every penny!',\n    createdAt: new Date('2025-01-25')\n  }, {\n    userName: 'Camping Expert',\n    rating: 4,\n    comment: 'Great cooler but quite heavy when full.',\n    createdAt: new Date('2025-01-23')\n  }, {\n    userName: 'Nature Lover',\n    rating: 5,\n    comment: 'Perfect for long camping trips. Highly durable!',\n    createdAt: new Date('2025-01-21')\n  }],\n  createdAt: new Date('2025-04-01'),\n  onSale: false\n}];\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async filters => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let filteredProducts = [...mockProducts];\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower));\n      }\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => {\n          var _filters$category;\n          return p.category.toLowerCase() === ((_filters$category = filters.category) === null || _filters$category === void 0 ? void 0 : _filters$category.toLowerCase());\n        });\n      }\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0));\n      }\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity));\n      }\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price)));\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price)));\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n    return filteredProducts;\n  },\n  // Get single product by ID\n  getProduct: async id => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n  // Add a review to a product\n  addReview: async (productId, review) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n    product.reviews.push(newReview);\n\n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n    return product;\n  },\n  // Search products\n  searchProducts: async query => {\n    return productService.getProducts({\n      search: query\n    });\n  },\n  // Get products by category\n  getProductsByCategory: async category => {\n    return productService.getProducts({\n      category\n    });\n  },\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};", "map": {"version": 3, "names": ["mockProducts", "_id", "name", "description", "category", "images", "prices", "storeName", "price", "productUrl", "inStock", "lastUpdated", "Date", "rating", "reviews", "userName", "comment", "createdAt", "onSale", "productService", "getProducts", "filters", "Promise", "resolve", "setTimeout", "filteredProducts", "search", "searchLower", "toLowerCase", "filter", "p", "includes", "_filters$category", "minPrice", "Math", "min", "map", "maxPrice", "max", "Infinity", "sort", "a", "b", "getProduct", "id", "product", "find", "Error", "add<PERSON>eview", "productId", "review", "newReview", "push", "reduce", "sum", "r", "length", "searchProducts", "query", "getProductsByCategory", "getTodaysDeals"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/api.ts"], "sourcesContent": ["import { Product, ProductFilters, Review } from '../types';\n\n// Mock data for development\nconst mockProducts: Product[] = [\n  {\n    _id: '1',\n    name: 'Sony WH-1000XM4 Wireless Headphones',\n    description: 'Industry-leading noise canceling with Dual Noise Sensor technology',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71o8Q5XJS5L._AC_SL1500_.jpg',\n      'https://m.media-amazon.com/images/I/81yD9yJ2r6L._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 29058,\n        productUrl: 'https://www.amazon.in/Sony-WH-1000XM4-Cancelling-Headphones-Bluetooth/dp/B0863TXGM3',\n        inStock: true,\n        lastUpdated: new Date()\n      },\n      {\n        storeName: 'Flipkart',\n        price: 29224,\n        productUrl: 'https://www.flipkart.com/sony-wh-1000xm4-bluetooth-headphones/p/itm9f84f49ad6ac8',\n        inStock: true,\n        lastUpdated: new Date()\n      }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: '<PERSON> Doe',\n        rating: 5,\n        comment: 'Best headphones I have ever owned!',\n        createdAt: new Date('2025-04-14')\n      },\n      {\n        userName: 'Priya Sharma',\n        rating: 5,\n        comment: 'Excellent sound quality and battery life. Worth every rupee!',\n        createdAt: new Date('2025-04-12')\n      },\n      {\n        userName: 'Rahul Mehta',\n        rating: 4,\n        comment: 'Great for work from home. Noise cancellation is amazing.',\n        createdAt: new Date('2025-04-10')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '2',\n    name: 'Samsung 55\" 4K Smart TV',\n    description: 'Crystal clear display with smart features',\n    category: 'Electronics',\n    images: [\n      'https://m.media-amazon.com/images/I/71LJJrKbezL._AC_SL1500_.jpg'\n    ],\n    prices: [\n      {\n        storeName: 'Amazon',\n        price: 41582,\n        productUrl: 'https://www.amazon.in/gaming-laptops/s?k=gaming+laptops'\n      },\n      {\n        storeName: 'Flipkart',\n        price: 41749,\n        productUrl: 'https://www.flipkart.com/gaming-laptops/pr?sid=6bo%2Cb5g'\n      }\n    ],\n    rating: 4.5,\n    reviews: [\n      {\n        userName: 'Amit Kumar',\n        rating: 5,\n        comment: 'Amazing laptop! Perfect for gaming and work.',\n        createdAt: new Date('2025-04-13')\n      },\n      {\n        userName: 'Sneha Patel',\n        rating: 4,\n        comment: 'Good performance but gets a bit warm during heavy usage.',\n        createdAt: new Date('2025-04-11')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '5',\n    name: 'Nike Air Max 270',\n    description: 'Comfortable athletic shoes with Air cushioning',\n    category: 'Fashion',\n    images: ['https://m.media-amazon.com/images/I/71jeoX0rMBL._AC_UX575_.jpg'],\n    prices: [\n      { storeName: 'Nike', price: 12525, productUrl: 'https://www.nike.com/in/t/air-max-270-mens-shoes-KkLcGR/AH8050-002', inStock: true, lastUpdated: new Date() },\n      { storeName: 'Amazon', price: 12524, productUrl: 'https://amazon.in', inStock: true, lastUpdated: new Date() }\n    ],\n    rating: 4.6,\n    reviews: [\n      {\n        userName: 'Ravi Singh',\n        rating: 5,\n        comment: 'Super comfortable shoes! Great for running and daily wear.',\n        createdAt: new Date('2025-04-09')\n      },\n      {\n        userName: 'Neha Gupta',\n        rating: 4,\n        comment: 'Good quality but sizing runs a bit small.',\n        createdAt: new Date('2025-04-08')\n      },\n      {\n        userName: 'Karan Joshi',\n        rating: 5,\n        comment: 'Love the design and comfort. Highly recommended!',\n        createdAt: new Date('2025-04-07')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '6',\n    name: 'Levi\\'s 501 Original Fit Jeans',\n    description: 'Classic straight leg jeans',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 5009, productUrl: 'https://amazon.in' },\n      { storeName: 'Myntra', price: 5803, productUrl: 'https://myntra.com' }\n    ],\n    rating: 4.5,\n    reviews: [\n      {\n        userName: 'Deepika Rao',\n        rating: 4,\n        comment: 'Good quality jeans. Fit is perfect and comfortable.',\n        createdAt: new Date('2025-04-06')\n      },\n      {\n        userName: 'Suresh Kumar',\n        rating: 5,\n        comment: 'Classic Levis quality. These jeans last for years!',\n        createdAt: new Date('2025-04-05')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '7',\n    name: 'Ray-Ban Aviator Sunglasses',\n    description: 'Classic aviator style with UV protection',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1572635196237-14b3f281503f?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 154.00, productUrl: 'https://www.amazon.in/Ray-Ban-Aviator-Sunglasses/dp/B001GNBJQ4' },\n      { storeName: 'Sunglass Hut', price: 169.00, productUrl: 'https://www.sunglasshut.com/us/ray-ban-aviator-classic-gold/8052896020578' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Arjun Malhotra',\n        rating: 5,\n        comment: 'Classic aviators! Great quality and style.',\n        createdAt: new Date('2025-04-04')\n      },\n      {\n        userName: 'Pooja Agarwal',\n        rating: 4,\n        comment: 'Good sunglasses but a bit pricey. UV protection is excellent.',\n        createdAt: new Date('2025-04-03')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '8',\n    name: 'Casio G-Shock Watch',\n    description: 'Durable digital watch with multiple features',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1533139502658-0198f920d8e8?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Amazon', price: 99.00, productUrl: 'https://www.amazon.in/Casio-G-Shock-Digital-Watch/dp/B07DQZQZQZ' },\n      { storeName: 'Casio', price: 110.00, productUrl: 'https://www.casio.com/us/watches/gshock/product.GA2100-1A/' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Vikash Sharma',\n        rating: 5,\n        comment: 'Tough watch! Perfect for outdoor activities.',\n        createdAt: new Date('2025-04-02')\n      },\n      {\n        userName: 'Manisha Jain',\n        rating: 5,\n        comment: 'Bought for my husband. He loves all the features!',\n        createdAt: new Date('2025-04-01')\n      },\n      {\n        userName: 'Rohit Verma',\n        rating: 4,\n        comment: 'Good watch but takes time to learn all functions.',\n        createdAt: new Date('2025-03-30')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '17',\n    name: 'Adidas Ultraboost 21',\n    description: 'Premium running shoes with responsive cushioning',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Adidas', price: 180.00, productUrl: 'https://www.adidas.com/us/ultraboost-21-shoes/FY0377.html' },\n      { storeName: 'Amazon', price: 169.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Sanjay Reddy',\n        rating: 5,\n        comment: 'Best running shoes I have ever owned! Great cushioning.',\n        createdAt: new Date('2025-03-28')\n      },\n      {\n        userName: 'Kavita Nair',\n        rating: 5,\n        comment: 'Perfect for marathon training. Very comfortable.',\n        createdAt: new Date('2025-03-26')\n      },\n      {\n        userName: 'Rajesh Gupta',\n        rating: 4,\n        comment: 'Good shoes but a bit expensive. Quality is excellent.',\n        createdAt: new Date('2025-03-24')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '18',\n    name: 'Michael Kors Leather Crossbody Bag',\n    description: 'Elegant leather bag with adjustable strap',\n    category: 'Fashion',\n    images: ['https://images.unsplash.com/photo-1548036328-c9fa89d128fa?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Michael Kors', price: 298.00, productUrl: 'https://www.michaelkors.com/leather-crossbody-bag/_/R-30F2G7CM2L' },\n      { storeName: 'Amazon', price: 278.95, productUrl: 'https://amazon.com' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Priyanka Singh',\n        rating: 5,\n        comment: 'Beautiful bag! Perfect size for daily use.',\n        createdAt: new Date('2025-03-22')\n      },\n      {\n        userName: 'Anjali Sharma',\n        rating: 4,\n        comment: 'Good quality leather. A bit pricey but worth it.',\n        createdAt: new Date('2025-03-20')\n      },\n      {\n        userName: 'Meera Patel',\n        rating: 5,\n        comment: 'Love the design and quality. Highly recommended!',\n        createdAt: new Date('2025-03-18')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  // Home & Garden Category\n  {\n    _id: '19',\n    name: 'Philips Hue Smart LED Bulb Set',\n    description: 'Color-changing smart LED bulbs with wireless control',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1563461660947-507ef49e9c47?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Philips', price: 199.99, productUrl: 'https://www.philips-hue.com/en-us/p/hue-white-and-color-ambiance-starter-kit-e26/046677555334' },\n      { storeName: 'Amazon', price: 189.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Best Buy', price: 194.99, productUrl: 'https://bestbuy.com' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Ravi Kumar',\n        rating: 5,\n        comment: 'Amazing smart lights! Easy to set up and control.',\n        createdAt: new Date('2025-03-16')\n      },\n      {\n        userName: 'Sunita Joshi',\n        rating: 4,\n        comment: 'Good product but app could be better. Colors are vibrant.',\n        createdAt: new Date('2025-03-14')\n      },\n      {\n        userName: 'Anil Verma',\n        rating: 5,\n        comment: 'Perfect for mood lighting. Great value for money.',\n        createdAt: new Date('2025-03-12')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '20',\n    name: 'KitchenAid Stand Mixer',\n    description: 'Professional 5-quart stand mixer with multiple attachments',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1544441893-675973e31985?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'KitchenAid', price: 399.99, productUrl: 'https://www.kitchenaid.com/countertop-appliances/stand-mixers/tilt-head-stand-mixers/p.artisan-series-5-quart-tilt-head-stand-mixer.ksm150psob.html' },\n      { storeName: 'Amazon', price: 379.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Williams Sonoma', price: 429.99, productUrl: 'https://www.williams-sonoma.com/products/kitchenaid-artisan-stand-mixer/' }\n    ],\n    rating: 4.9,\n    reviews: [\n      {\n        userName: 'Lakshmi Iyer',\n        rating: 5,\n        comment: 'Excellent mixer! Makes baking so much easier.',\n        createdAt: new Date('2025-03-10')\n      },\n      {\n        userName: 'Rajesh Agarwal',\n        rating: 5,\n        comment: 'Heavy duty and reliable. Perfect for my bakery.',\n        createdAt: new Date('2025-03-08')\n      },\n      {\n        userName: 'Nisha Kapoor',\n        rating: 4,\n        comment: 'Great quality but quite expensive. Worth the investment.',\n        createdAt: new Date('2025-03-06')\n      },\n      {\n        userName: 'Suresh Reddy',\n        rating: 5,\n        comment: 'Best kitchen appliance I have ever bought!',\n        createdAt: new Date('2025-03-04')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '21',\n    name: 'DEWALT Power Tool Set',\n    description: '20V MAX Cordless Drill Combo Kit with 2 batteries',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1581244277943-fe4a9c777189?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Home Depot', price: 299.00, productUrl: 'https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-Charger-and-Bag-DCK240C2/204373168' },\n      { storeName: 'Amazon', price: 289.99, productUrl: 'https://amazon.com' },\n      { storeName: 'Lowes', price: 294.99, productUrl: 'https://www.lowes.com/pd/DEWALT-20-Volt-Max-2-Tool-Combo-Kit/1000191169' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Manoj Singh',\n        rating: 5,\n        comment: 'Excellent tools! Great for home projects.',\n        createdAt: new Date('2025-03-02')\n      },\n      {\n        userName: 'Kavya Nair',\n        rating: 4,\n        comment: 'Good quality but batteries could last longer.',\n        createdAt: new Date('2025-02-28')\n      },\n      {\n        userName: 'Ashok Kumar',\n        rating: 5,\n        comment: 'Professional grade tools. Highly recommended!',\n        createdAt: new Date('2025-02-26')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '22',\n    name: 'Dyson V15 Detect Vacuum',\n    description: 'Cordless vacuum with laser dust detection',\n    category: 'Home & Garden',\n    images: ['https://images.unsplash.com/photo-1558317374-067fb5f30001?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Dyson', price: 699.99, productUrl: 'https://www.dyson.com/vacuum-cleaners/stick-vacuum-cleaners/dyson-v15-detect-nickel-yellow' },\n      { storeName: 'Amazon', price: 679.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Best Buy', price: 689.99, productUrl: 'https://bestbuy.com' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Shweta Sharma',\n        rating: 5,\n        comment: 'Amazing vacuum! The laser detection is incredible.',\n        createdAt: new Date('2025-02-24')\n      },\n      {\n        userName: 'Vinod Gupta',\n        rating: 4,\n        comment: 'Powerful suction but quite expensive.',\n        createdAt: new Date('2025-02-22')\n      },\n      {\n        userName: 'Rekha Patel',\n        rating: 5,\n        comment: 'Best vacuum cleaner I have ever used!',\n        createdAt: new Date('2025-02-20')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  // Sports Category\n  {\n    _id: '23',\n    name: 'NordicTrack Commercial 1750 Treadmill',\n    description: 'Smart treadmill with 14-inch HD touchscreen and iFit integration',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1595078475328-1ab05d0a6a0e?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'NordicTrack', price: 1899.00, productUrl: 'https://www.nordictrack.com/treadmills/commercial-1750-treadmill' },\n      { storeName: 'Amazon', price: 1799.00, productUrl: 'https://amazon.com' },\n      { storeName: 'Dick\\'s Sporting Goods', price: 1849.99, productUrl: 'https://dickssportinggoods.com' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Fitness Guru',\n        rating: 5,\n        comment: 'Excellent treadmill! Perfect for home workouts.',\n        createdAt: new Date('2025-02-18')\n      },\n      {\n        userName: 'Ramesh Joshi',\n        rating: 4,\n        comment: 'Good machine but takes up a lot of space.',\n        createdAt: new Date('2025-02-16')\n      },\n      {\n        userName: 'Sneha Reddy',\n        rating: 5,\n        comment: 'Great investment for health. Love the iFit features.',\n        createdAt: new Date('2025-02-14')\n      },\n      {\n        userName: 'Kiran Kumar',\n        rating: 4,\n        comment: 'Solid build quality. Assembly was a bit challenging.',\n        createdAt: new Date('2025-02-12')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '24',\n    name: 'Wilson Evolution Basketball',\n    description: 'Official size indoor game basketball with moisture-wicking technology',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1519861531473-9200262188bf?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Wilson', price: 59.99, productUrl: 'https://www.wilson.com/en-us/product/evolution-basketball-WTB0516' },\n      { storeName: 'Amazon', price: 54.95, productUrl: 'https://amazon.com' },\n      { storeName: 'Dick\\'s Sporting Goods', price: 57.99, productUrl: 'https://dickssportinggoods.com' }\n    ],\n    rating: 4.9,\n    reviews: [\n      {\n        userName: 'Basketball Pro',\n        rating: 5,\n        comment: 'Perfect basketball! Great grip and bounce.',\n        createdAt: new Date('2025-02-10')\n      },\n      {\n        userName: 'Arjun Singh',\n        rating: 5,\n        comment: 'Best basketball for indoor games. Highly recommended!',\n        createdAt: new Date('2025-02-08')\n      },\n      {\n        userName: 'Rohit Sharma',\n        rating: 4,\n        comment: 'Good quality ball. Worth the price.',\n        createdAt: new Date('2025-02-06')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  },\n  {\n    _id: '25',\n    name: 'Garmin Forerunner 945 GPS Watch',\n    description: 'Advanced running watch with full-color mapping and training metrics',\n    category: 'Sports',\n    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?auto=format&fit=crop&w=800'],\n    prices: [\n      { storeName: 'Garmin', price: 599.99, productUrl: 'https://garmin.com' },\n      { storeName: 'Amazon', price: 579.95, productUrl: 'https://amazon.com' },\n      { storeName: 'REI', price: 589.99, productUrl: 'https://rei.com' }\n    ],\n    rating: 4.7,\n    reviews: [\n      {\n        userName: 'Tech Runner',\n        rating: 5,\n        comment: 'Amazing GPS watch! Perfect for serious runners.',\n        createdAt: new Date('2025-02-04')\n      },\n      {\n        userName: 'Marathon Man',\n        rating: 4,\n        comment: 'Great features but battery life could be better.',\n        createdAt: new Date('2025-02-02')\n      },\n      {\n        userName: 'Fitness Freak',\n        rating: 5,\n        comment: 'Best running watch in the market. Love the mapping!',\n        createdAt: new Date('2025-01-31')\n      },\n      {\n        userName: 'Priya Runner',\n        rating: 4,\n        comment: 'Good watch but takes time to learn all features.',\n        createdAt: new Date('2025-01-29')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: true\n  },\n  {\n    _id: '26',\n    name: 'Yeti Tundra 45 Cooler',\n    description: 'Premium hard cooler for outdoor activities and camping',\n    category: 'Sports',\n    images: ['https://picsum.photos/id/26/800/800'],\n    prices: [\n      { storeName: 'Yeti', price: 325.00, productUrl: 'https://www.yeti.com/en_US/coolers/hard-coolers/tundra-45-cooler/YT45.html' },\n      { storeName: 'Amazon', price: 299.99, productUrl: 'https://amazon.com' },\n      { storeName: 'REI', price: 319.99, productUrl: 'https://rei.com' }\n    ],\n    rating: 4.8,\n    reviews: [\n      {\n        userName: 'Outdoor Explorer',\n        rating: 5,\n        comment: 'Best cooler for camping! Keeps ice for days.',\n        createdAt: new Date('2025-01-27')\n      },\n      {\n        userName: 'Adventure Seeker',\n        rating: 5,\n        comment: 'Premium quality cooler. Worth every penny!',\n        createdAt: new Date('2025-01-25')\n      },\n      {\n        userName: 'Camping Expert',\n        rating: 4,\n        comment: 'Great cooler but quite heavy when full.',\n        createdAt: new Date('2025-01-23')\n      },\n      {\n        userName: 'Nature Lover',\n        rating: 5,\n        comment: 'Perfect for long camping trips. Highly durable!',\n        createdAt: new Date('2025-01-21')\n      }\n    ],\n    createdAt: new Date('2025-04-01'),\n    onSale: false\n  }\n];\n\nexport const productService = {\n  // Get all products with optional filters\n  getProducts: async (filters?: ProductFilters) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    let filteredProducts = [...mockProducts];\n\n    if (filters) {\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(p => \n          p.name.toLowerCase().includes(searchLower) ||\n          p.description.toLowerCase().includes(searchLower)\n        );\n      }\n\n      if (filters.category) {\n        filteredProducts = filteredProducts.filter(p => \n          p.category.toLowerCase() === filters.category?.toLowerCase()\n        );\n      }\n\n      if (filters.minPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.min(...p.prices.map(price => price.price)) >= (filters.minPrice || 0)\n        );\n      }\n\n      if (filters.maxPrice) {\n        filteredProducts = filteredProducts.filter(p => \n          Math.max(...p.prices.map(price => price.price)) <= (filters.maxPrice || Infinity)\n        );\n      }\n\n      if (filters.sort) {\n        switch (filters.sort) {\n          case 'price-asc':\n            filteredProducts.sort((a, b) => \n              Math.min(...a.prices.map(p => p.price)) - Math.min(...b.prices.map(p => p.price))\n            );\n            break;\n          case 'price-desc':\n            filteredProducts.sort((a, b) => \n              Math.min(...b.prices.map(p => p.price)) - Math.min(...a.prices.map(p => p.price))\n            );\n            break;\n          case 'rating':\n            filteredProducts.sort((a, b) => b.rating - a.rating);\n            break;\n        }\n      }\n    }\n\n    return filteredProducts;\n  },\n\n  // Get single product by ID\n  getProduct: async (id: string) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === id);\n    if (!product) throw new Error('Product not found');\n    return product;\n  },\n\n  // Add a review to a product\n  addReview: async (productId: string, review: Omit<Review, 'createdAt'>) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const product = mockProducts.find(p => p._id === productId);\n    if (!product) throw new Error('Product not found');\n\n    const newReview = {\n      ...review,\n      createdAt: new Date()\n    };\n\n    product.reviews.push(newReview);\n    \n    // Update product rating\n    product.rating = product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length;\n\n    return product;\n  },\n\n  // Search products\n  searchProducts: async (query: string) => {\n    return productService.getProducts({ search: query });\n  },\n\n  // Get products by category\n  getProductsByCategory: async (category: string) => {\n    return productService.getProducts({ category });\n  },\n\n  // Get today's deals\n  getTodaysDeals: async () => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return mockProducts.filter(p => p.onSale);\n  }\n};\n"], "mappings": "AAEA;AACA,MAAMA,YAAuB,GAAG,CAC9B;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qCAAqC;EAC3CC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,EACjE,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,qFAAqF;IACjGC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAIC,IAAI,CAAC;EACxB,CAAC,EACD;IACEL,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,kFAAkF;IAC9FC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAIC,IAAI,CAAC;EACxB,CAAC,CACF;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,UAAU;IACpBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oCAAoC;IAC7CC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8DAA8D;IACvEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,0DAA0D;IACnEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CACN,iEAAiE,CAClE;EACDC,MAAM,EAAE,CACN;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,EACD;IACEF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC,CACF;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8CAA8C;IACvDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,0DAA0D;IACnEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,gDAAgD;EAC7DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gEAAgE,CAAC;EAC1EC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,MAAM;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE,oEAAoE;IAAEC,OAAO,EAAE,IAAI;IAAEC,WAAW,EAAE,IAAIC,IAAI,CAAC;EAAE,CAAC,EAC7J;IAAEL,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE,mBAAmB;IAAEC,OAAO,EAAE,IAAI;IAAEC,WAAW,EAAE,IAAIC,IAAI,CAAC;EAAE,CAAC,CAC/G;EACDC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4DAA4D;IACrEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,4BAA4B;EACzCC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAoB,CAAC,EACrE;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACvE;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oDAAoD;IAC7DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,4BAA4B;EAClCC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAiE,CAAC,EACpH;IAAEF,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA4E,CAAC,CACtI;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,+DAA+D;IACxEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAkE,CAAC,EACpH;IAAEF,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6D,CAAC,CAChH;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8CAA8C;IACvDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,sBAAsB;EAC5BC,WAAW,EAAE,kDAAkD;EAC/DC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA4D,CAAC,EAC/G;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,yDAAyD;IAClEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,uDAAuD;IAChEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,oCAAoC;EAC1CC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAmE,CAAC,EAC5H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACzE;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC;AACD;AACA;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,sDAAsD;EACnEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,SAAS;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAgG,CAAC,EACpJ;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAC5E;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,2DAA2D;IACpEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,YAAY;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsJ,CAAC,EAC7M;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,iBAAiB;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA2E,CAAC,CACxI;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,+CAA+C;IACxDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,iDAAiD;IAC1DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,0DAA0D;IACnEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,YAAY;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqI,CAAC,EAC5L;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA0E,CAAC,CAC7H;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,YAAY;IACtBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,+CAA+C;IACxDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,+CAA+C;IACxDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,sFAAsF,CAAC;EAChGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6F,CAAC,EAC/I;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAC5E;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,oDAAoD;IAC7DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,uCAAuC;IAChDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,uCAAuC;IAChDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC;AACD;AACA;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uCAAuC;EAC7CC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAmE,CAAC,EAC5H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACzE;IAAEF,SAAS,EAAE,wBAAwB;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAiC,CAAC,CACtG;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,iDAAiD;IAC1DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,sDAAsD;IAC/DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,sDAAsD;IAC/DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,6BAA6B;EACnCC,WAAW,EAAE,uEAAuE;EACpFC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAoE,CAAC,EACtH;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvE;IAAEF,SAAS,EAAE,wBAAwB;IAAEC,KAAK,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAiC,CAAC,CACpG;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,uDAAuD;IAChEC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,qCAAqC;IAC9CC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,iCAAiC;EACvCC,WAAW,EAAE,qEAAqE;EAClFC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,yFAAyF,CAAC;EACnGC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAkB,CAAC,CACnE;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,aAAa;IACvBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,iDAAiD;IAC1DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,eAAe;IACzBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,kDAAkD;IAC3DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,EACD;EACEjB,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,wDAAwD;EACrEC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,qCAAqC,CAAC;EAC/CC,MAAM,EAAE,CACN;IAAEC,SAAS,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAA6E,CAAC,EAC9H;IAAEF,SAAS,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxE;IAAEF,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAkB,CAAC,CACnE;EACDI,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,CACP;IACEC,QAAQ,EAAE,kBAAkB;IAC5BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,8CAA8C;IACvDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,kBAAkB;IAC5BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,gBAAgB;IAC1BF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,yCAAyC;IAClDC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,EACD;IACEG,QAAQ,EAAE,cAAc;IACxBF,MAAM,EAAE,CAAC;IACTG,OAAO,EAAE,iDAAiD;IAC1DC,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;EAClC,CAAC,CACF;EACDK,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC;EACjCM,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAOC,OAAwB,IAAK;IAC/C;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIE,gBAAgB,GAAG,CAAC,GAAGzB,YAAY,CAAC;IAExC,IAAIqB,OAAO,EAAE;MACX,IAAIA,OAAO,CAACK,MAAM,EAAE;QAClB,MAAMC,WAAW,GAAGN,OAAO,CAACK,MAAM,CAACE,WAAW,CAAC,CAAC;QAChDH,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CA,CAAC,CAAC5B,IAAI,CAAC0B,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC1CG,CAAC,CAAC3B,WAAW,CAACyB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAClD,CAAC;MACH;MAEA,IAAIN,OAAO,CAACjB,QAAQ,EAAE;QACpBqB,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC;UAAA,IAAAE,iBAAA;UAAA,OAC1CF,CAAC,CAAC1B,QAAQ,CAACwB,WAAW,CAAC,CAAC,OAAAI,iBAAA,GAAKX,OAAO,CAACjB,QAAQ,cAAA4B,iBAAA,uBAAhBA,iBAAA,CAAkBJ,WAAW,CAAC,CAAC;QAAA,CAC9D,CAAC;MACH;MAEA,IAAIP,OAAO,CAACY,QAAQ,EAAE;QACpBR,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACC,GAAG,CAAC,GAAGL,CAAC,CAACxB,MAAM,CAAC8B,GAAG,CAAC5B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKa,OAAO,CAACY,QAAQ,IAAI,CAAC,CAC3E,CAAC;MACH;MAEA,IAAIZ,OAAO,CAACgB,QAAQ,EAAE;QACpBZ,gBAAgB,GAAGA,gBAAgB,CAACI,MAAM,CAACC,CAAC,IAC1CI,IAAI,CAACI,GAAG,CAAC,GAAGR,CAAC,CAACxB,MAAM,CAAC8B,GAAG,CAAC5B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC,CAAC,KAAKa,OAAO,CAACgB,QAAQ,IAAIE,QAAQ,CAClF,CAAC;MACH;MAEA,IAAIlB,OAAO,CAACmB,IAAI,EAAE;QAChB,QAAQnB,OAAO,CAACmB,IAAI;UAClB,KAAK,WAAW;YACdf,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACnC,MAAM,CAAC8B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACtB,KAAK,CAAC,CAAC,GAAG0B,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAACpC,MAAM,CAAC8B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACtB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,YAAY;YACfiB,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACzBR,IAAI,CAACC,GAAG,CAAC,GAAGO,CAAC,CAACpC,MAAM,CAAC8B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACtB,KAAK,CAAC,CAAC,GAAG0B,IAAI,CAACC,GAAG,CAAC,GAAGM,CAAC,CAACnC,MAAM,CAAC8B,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACtB,KAAK,CAAC,CAClF,CAAC;YACD;UACF,KAAK,QAAQ;YACXiB,gBAAgB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC7B,MAAM,GAAG4B,CAAC,CAAC5B,MAAM,CAAC;YACpD;QACJ;MACF;IACF;IAEA,OAAOY,gBAAgB;EACzB,CAAC;EAED;EACAkB,UAAU,EAAE,MAAOC,EAAU,IAAK;IAChC;IACA,MAAM,IAAItB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG7C,YAAY,CAAC8C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC7B,GAAG,KAAK2C,EAAE,CAAC;IACpD,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAClD,OAAOF,OAAO;EAChB,CAAC;EAED;EACAG,SAAS,EAAE,MAAAA,CAAOC,SAAiB,EAAEC,MAAiC,KAAK;IACzE;IACA,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMsB,OAAO,GAAG7C,YAAY,CAAC8C,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAC7B,GAAG,KAAKgD,SAAS,CAAC;IAC3D,IAAI,CAACJ,OAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IAElD,MAAMI,SAAS,GAAG;MAChB,GAAGD,MAAM;MACTjC,SAAS,EAAE,IAAIL,IAAI,CAAC;IACtB,CAAC;IAEDiC,OAAO,CAAC/B,OAAO,CAACsC,IAAI,CAACD,SAAS,CAAC;;IAE/B;IACAN,OAAO,CAAChC,MAAM,GAAGgC,OAAO,CAAC/B,OAAO,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC1C,MAAM,EAAE,CAAC,CAAC,GAAGgC,OAAO,CAAC/B,OAAO,CAAC0C,MAAM;IAE/F,OAAOX,OAAO;EAChB,CAAC;EAED;EACAY,cAAc,EAAE,MAAOC,KAAa,IAAK;IACvC,OAAOvC,cAAc,CAACC,WAAW,CAAC;MAAEM,MAAM,EAAEgC;IAAM,CAAC,CAAC;EACtD,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAOvD,QAAgB,IAAK;IACjD,OAAOe,cAAc,CAACC,WAAW,CAAC;MAAEhB;IAAS,CAAC,CAAC;EACjD,CAAC;EAED;EACAwD,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B;IACA,MAAM,IAAItC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOvB,YAAY,CAAC6B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,MAAM,CAAC;EAC3C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}