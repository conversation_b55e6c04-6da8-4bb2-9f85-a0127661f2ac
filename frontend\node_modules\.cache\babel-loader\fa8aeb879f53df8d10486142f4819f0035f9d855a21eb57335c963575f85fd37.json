{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport './App.css';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { UserStatsProvider } from './contexts/UserStatsContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport SearchResultsPage from './pages/SearchResultsPage';\nimport CategoryPage from './pages/CategoryPage';\nimport TodaysDealsPage from './pages/TodaysDealsPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport WishlistPage from './pages/WishlistPage';\nimport ProfilePage from './pages/ProfilePage';\nimport OrdersPage from './pages/OrdersPage';\nimport NotFoundPage from './pages/NotFoundPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(UserStatsProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container mx-auto px-4 py-8\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 30,\n                    columnNumber: 42\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/product/:id\",\n                  element: /*#__PURE__*/_jsxDEV(ProductDetailPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 31,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/search\",\n                  element: /*#__PURE__*/_jsxDEV(SearchResultsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 48\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/category/:category\",\n                  element: /*#__PURE__*/_jsxDEV(CategoryPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/deals\",\n                  element: /*#__PURE__*/_jsxDEV(TodaysDealsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 34,\n                    columnNumber: 47\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 47\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/register\",\n                  element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 50\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/wishlist\",\n                  element: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 37,\n                    columnNumber: 50\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/profile\",\n                  element: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 38,\n                    columnNumber: 47\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/orders\",\n                  element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 46\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"*\",\n                  element: /*#__PURE__*/_jsxDEV(NotFoundPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 40\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "UserStatsProvider", "<PERSON><PERSON><PERSON>", "HomePage", "ProductDetailPage", "SearchResultsPage", "CategoryPage", "TodaysDealsPage", "LoginPage", "RegisterPage", "WishlistPage", "ProfilePage", "OrdersPage", "NotFoundPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport './App.css';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { UserStatsProvider } from './contexts/UserStatsContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport SearchResultsPage from './pages/SearchResultsPage';\nimport CategoryPage from './pages/CategoryPage';\nimport TodaysDealsPage from './pages/TodaysDealsPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport WishlistPage from './pages/WishlistPage';\nimport ProfilePage from './pages/ProfilePage';\nimport OrdersPage from './pages/OrdersPage';\nimport NotFoundPage from './pages/NotFoundPage';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <UserStatsProvider>\n          <Router>\n          <div className=\"min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors\">\n            <Navbar />\n            <div className=\"container mx-auto px-4 py-8\">\n              <Routes>\n                <Route path=\"/\" element={<HomePage />} />\n                <Route path=\"/product/:id\" element={<ProductDetailPage />} />\n                <Route path=\"/search\" element={<SearchResultsPage />} />\n                <Route path=\"/category/:category\" element={<CategoryPage />} />\n                <Route path=\"/deals\" element={<TodaysDealsPage />} />\n                <Route path=\"/login\" element={<LoginPage />} />\n                <Route path=\"/register\" element={<RegisterPage />} />\n                <Route path=\"/wishlist\" element={<WishlistPage />} />\n              <Route path=\"/profile\" element={<ProfilePage />} />\n              <Route path=\"/orders\" element={<OrdersPage />} />\n              <Route path=\"*\" element={<NotFoundPage />} />\n              </Routes>\n            </div>\n          </div>\n          </Router>\n        </UserStatsProvider>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAClB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACf,aAAa;IAAAiB,QAAA,eACZF,OAAA,CAAChB,YAAY;MAAAkB,QAAA,eACXF,OAAA,CAACd,iBAAiB;QAAAgB,QAAA,eAChBF,OAAA,CAACnB,MAAM;UAAAqB,QAAA,eACPF,OAAA;YAAKG,SAAS,EAAC,6DAA6D;YAAAD,QAAA,gBAC1EF,OAAA,CAACb,MAAM;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVP,OAAA;cAAKG,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CF,OAAA,CAAClB,MAAM;gBAAAoB,QAAA,gBACLF,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAET,OAAA,CAACZ,QAAQ;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzCP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,cAAc;kBAACC,OAAO,eAAET,OAAA,CAACX,iBAAiB;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7DP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,SAAS;kBAACC,OAAO,eAAET,OAAA,CAACV,iBAAiB;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,qBAAqB;kBAACC,OAAO,eAAET,OAAA,CAACT,YAAY;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAET,OAAA,CAACR,eAAe;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAET,OAAA,CAACP,SAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAET,OAAA,CAACN,YAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAET,OAAA,CAACL,YAAY;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAET,OAAA,CAACJ,WAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,SAAS;kBAACC,OAAO,eAAET,OAAA,CAACH,UAAU;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDP,OAAA,CAACjB,KAAK;kBAACyB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAET,OAAA,CAACF,YAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACG,EAAA,GA7BQT,GAAG;AA+BZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}