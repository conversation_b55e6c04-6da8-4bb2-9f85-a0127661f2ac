{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: ''\n  });\n  const {\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      try {\n        const productData = await productService.getProduct(id);\n        setProduct(productData);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProduct();\n  }, [id, isAuthenticated]);\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n    if (!id) return;\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n  const handleReviewSubmit = async e => {\n    e.preventDefault();\n    if (!product || !id) return;\n    try {\n      await productService.addReview(id, {\n        rating: reviewForm.rating,\n        comment: reviewForm.comment\n      });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({\n        userName: '',\n        rating: 5,\n        comment: ''\n      });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 dark:text-red-400 mb-4\",\n          children: error || 'Product not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  }\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = highestPrice - lowestPrice;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"aspect-square w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-700 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(product.images[selectedImage]),\n                alt: getImageAlt(product.images[selectedImage], product.name),\n                className: \"h-full w-full object-cover object-center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-4 gap-2\",\n              children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedImage(index),\n                className: `aspect-square overflow-hidden rounded-md ${selectedImage === index ? 'ring-2 ring-blue-500' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(image),\n                  alt: getImageAlt(image, product.name),\n                  className: \"h-full w-full object-cover object-center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 flex-1\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleWishlistToggle,\n                disabled: wishlistLoading,\n                className: \"ml-4 p-3 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\",\n                title: isInWishlist ? 'Remove from wishlist' : 'Add to wishlist',\n                children: isInWishlist ? /*#__PURE__*/_jsxDEV(HeartSolidIcon, {\n                  className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"h-6 w-6 text-gray-600 dark:text-gray-400 hover:text-red-500 transition-colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: `h-5 w-5 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: [product.rating, \" (\", product.reviews.length, \" reviews)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                  children: formatPriceIndian(lowestPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), savings > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg text-green-600 dark:text-green-400\",\n                  children: [\"Save \", formatPriceIndian(savings)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                children: [\"Prices from \", product.prices.length, \" store\", product.prices.length !== 1 ? 's' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600 dark:text-gray-400\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), product.features && product.features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                children: \"Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"mt-2 space-y-1\",\n                children: product.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-gray-600 dark:text-gray-400 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this), feature]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200 dark:border-gray-700 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\",\n            children: \"Price Comparison\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n            children: product.prices.map((price, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 dark:text-gray-100\",\n                    children: price.storeName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                    children: formatPriceIndian(price.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), price.originalPrice > price.price && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                    children: formatPriceIndian(price.originalPrice)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [price.discount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-block bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full mb-2\",\n                    children: [price.discount, \"% OFF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm ${price.inStock ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`,\n                    children: price.inStock ? `In Stock (${price.stockCount})` : 'Out of Stock'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: price.productUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block\",\n                children: [\"View on \", price.storeName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200 dark:border-gray-700 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\",\n            children: \"Customer Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), product.reviews.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: product.reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-b border-gray-200 dark:border-gray-600 pb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: `h-4 w-4 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium text-gray-900 dark:text-gray-100\",\n                  children: review.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                  children: new Date(review.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"No reviews yet. Be the first to review this product!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"tw07bgsEc3Dngfx4SHrJnFwPQB8=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "StarIcon", "HeartIcon", "HeartSolidIcon", "productService", "wishlistService", "useAuth", "formatPriceIndian", "getImageUrl", "getImageAlt", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "isInWishlist", "setIsInWishlist", "wishlistLoading", "setWishlistLoading", "reviewForm", "setReviewForm", "userName", "rating", "comment", "isAuthenticated", "fetchProduct", "productData", "getProduct", "inWishlist", "checkWishlist", "wishlistError", "console", "err", "handleWishlistToggle", "alert", "removeFromWishlist", "addToWishlist", "handleReviewSubmit", "e", "preventDefault", "add<PERSON>eview", "updatedProduct", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "lowestPrice", "Math", "min", "prices", "map", "p", "price", "highestPrice", "max", "savings", "src", "images", "alt", "name", "length", "image", "index", "disabled", "title", "Array", "_", "i", "floor", "reviews", "description", "features", "feature", "storeName", "originalPrice", "discount", "inStock", "stockCount", "href", "productUrl", "target", "rel", "review", "Date", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProductDetailPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { HeartIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { productService } from '../services/productService';\nimport { wishlistService } from '../services/wishlistService';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatPriceIndian, getImageUrl, getImageAlt } from '../utils/currency';\n\nconst ProductDetailPage = () => {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [isInWishlist, setIsInWishlist] = useState(false);\n  const [wishlistLoading, setWishlistLoading] = useState(false);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: ''\n  });\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      \n      try {\n        const productData = await productService.getProduct(id);\n        setProduct(productData);\n        setError(null);\n\n        // Check wishlist status if user is authenticated\n        if (isAuthenticated) {\n          try {\n            const inWishlist = await wishlistService.checkWishlist(id);\n            setIsInWishlist(inWishlist);\n          } catch (wishlistError) {\n            console.error('Error checking wishlist status:', wishlistError);\n          }\n        }\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id, isAuthenticated]);\n\n  const handleWishlistToggle = async () => {\n    if (!isAuthenticated) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n\n    if (!id) return;\n\n    setWishlistLoading(true);\n    try {\n      if (isInWishlist) {\n        await wishlistService.removeFromWishlist(id);\n        setIsInWishlist(false);\n      } else {\n        await wishlistService.addToWishlist(id);\n        setIsInWishlist(true);\n      }\n    } catch (error) {\n      console.error('Error toggling wishlist:', error);\n      alert('Failed to update wishlist. Please try again.');\n    } finally {\n      setWishlistLoading(false);\n    }\n  };\n\n  const handleReviewSubmit = async (e) => {\n    e.preventDefault();\n    if (!product || !id) return;\n\n    try {\n      await productService.addReview(id, { rating: reviewForm.rating, comment: reviewForm.comment });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({ userName: '', rating: 5, comment: '' });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 dark:text-red-400 mb-4\">{error || 'Product not found'}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const lowestPrice = Math.min(...product.prices.map(p => p.price));\n  const highestPrice = Math.max(...product.prices.map(p => p.price));\n  const savings = highestPrice - lowestPrice;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8\">\n            {/* Product Images */}\n            <div>\n              <div className=\"aspect-square w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-700 mb-4\">\n                <img\n                  src={getImageUrl(product.images[selectedImage])}\n                  alt={getImageAlt(product.images[selectedImage], product.name)}\n                  className=\"h-full w-full object-cover object-center\"\n                />\n              </div>\n              \n              {product.images.length > 1 && (\n                <div className=\"grid grid-cols-4 gap-2\">\n                  {product.images.map((image, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setSelectedImage(index)}\n                      className={`aspect-square overflow-hidden rounded-md ${\n                        selectedImage === index ? 'ring-2 ring-blue-500' : ''\n                      }`}\n                    >\n                      <img\n                        src={getImageUrl(image)}\n                        alt={getImageAlt(image, product.name)}\n                        className=\"h-full w-full object-cover object-center\"\n                      />\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Product Info */}\n            <div>\n              <div className=\"flex items-start justify-between\">\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100 flex-1\">{product.name}</h1>\n                \n                {/* Wishlist Button */}\n                <button\n                  onClick={handleWishlistToggle}\n                  disabled={wishlistLoading}\n                  className=\"ml-4 p-3 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\"\n                  title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}\n                >\n                  {isInWishlist ? (\n                    <HeartSolidIcon className=\"h-6 w-6 text-red-500\" />\n                  ) : (\n                    <HeartIcon className=\"h-6 w-6 text-gray-600 dark:text-gray-400 hover:text-red-500 transition-colors\" />\n                  )}\n                </button>\n              </div>\n\n              {/* Rating */}\n              <div className=\"mt-4 flex items-center\">\n                <div className=\"flex items-center\">\n                  {[...Array(5)].map((_, i) => (\n                    <StarIcon\n                      key={i}\n                      className={`h-5 w-5 ${\n                        i < Math.floor(product.rating)\n                          ? 'text-yellow-400'\n                          : 'text-gray-300 dark:text-gray-600'\n                      }`}\n                    />\n                  ))}\n                </div>\n                <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">\n                  {product.rating} ({product.reviews.length} reviews)\n                </span>\n              </div>\n\n              {/* Price */}\n              <div className=\"mt-6\">\n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n                    {formatPriceIndian(lowestPrice)}\n                  </span>\n                  {savings > 0 && (\n                    <span className=\"text-lg text-green-600 dark:text-green-400\">\n                      Save {formatPriceIndian(savings)}\n                    </span>\n                  )}\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  Prices from {product.prices.length} store{product.prices.length !== 1 ? 's' : ''}\n                </p>\n              </div>\n\n              {/* Description */}\n              <div className=\"mt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Description</h3>\n                <p className=\"mt-2 text-gray-600 dark:text-gray-400\">{product.description}</p>\n              </div>\n\n              {/* Features */}\n              {product.features && product.features.length > 0 && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Features</h3>\n                  <ul className=\"mt-2 space-y-1\">\n                    {product.features.map((feature, index) => (\n                      <li key={index} className=\"text-gray-600 dark:text-gray-400 flex items-center\">\n                        <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Price Comparison */}\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-8\">\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\">Price Comparison</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {product.prices.map((price, index) => (\n                <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">{price.storeName}</h4>\n                      <p className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                        {formatPriceIndian(price.price)}\n                      </p>\n                      {price.originalPrice > price.price && (\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400 line-through\">\n                          {formatPriceIndian(price.originalPrice)}\n                        </p>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      {price.discount > 0 && (\n                        <span className=\"inline-block bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full mb-2\">\n                          {price.discount}% OFF\n                        </span>\n                      )}\n                      <p className={`text-sm ${price.inStock ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\n                        {price.inStock ? `In Stock (${price.stockCount})` : 'Out of Stock'}\n                      </p>\n                    </div>\n                  </div>\n                  <a\n                    href={price.productUrl}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block\"\n                  >\n                    View on {price.storeName}\n                  </a>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Reviews */}\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-8\">\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-6\">Customer Reviews</h3>\n            \n            {product.reviews.length > 0 ? (\n              <div className=\"space-y-6\">\n                {product.reviews.map((review, index) => (\n                  <div key={index} className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n                    <div className=\"flex items-center mb-2\">\n                      <div className=\"flex items-center\">\n                        {[...Array(5)].map((_, i) => (\n                          <StarIcon\n                            key={i}\n                            className={`h-4 w-4 ${\n                              i < review.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'\n                            }`}\n                          />\n                        ))}\n                      </div>\n                      <span className=\"ml-2 font-medium text-gray-900 dark:text-gray-100\">{review.userName}</span>\n                      <span className=\"ml-2 text-sm text-gray-500 dark:text-gray-400\">\n                        {new Date(review.createdAt).toLocaleDateString()}\n                      </span>\n                    </div>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{review.comment}</p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-600 dark:text-gray-400\">No reviews yet. Be the first to review this product!</p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASA,SAAS,IAAIC,cAAc,QAAQ,2BAA2B;AACvE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC;IAC3C8B,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC;EAAgB,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAErCR,SAAS,CAAC,MAAM;IACd,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACnB,EAAE,EAAE;MAET,IAAI;QACF,MAAMoB,WAAW,GAAG,MAAM9B,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;QACvDE,UAAU,CAACkB,WAAW,CAAC;QACvBd,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAIY,eAAe,EAAE;UACnB,IAAI;YACF,MAAMI,UAAU,GAAG,MAAM/B,eAAe,CAACgC,aAAa,CAACvB,EAAE,CAAC;YAC1DU,eAAe,CAACY,UAAU,CAAC;UAC7B,CAAC,CAAC,OAAOE,aAAa,EAAE;YACtBC,OAAO,CAACpB,KAAK,CAAC,iCAAiC,EAAEmB,aAAa,CAAC;UACjE;QACF;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZpB,QAAQ,CAAC,iDAAiD,CAAC;QAC3DmB,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDe,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACnB,EAAE,EAAEkB,eAAe,CAAC,CAAC;EAEzB,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACT,eAAe,EAAE;MACpBU,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACF;IAEA,IAAI,CAAC5B,EAAE,EAAE;IAETY,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,IAAIH,YAAY,EAAE;QAChB,MAAMlB,eAAe,CAACsC,kBAAkB,CAAC7B,EAAE,CAAC;QAC5CU,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM;QACL,MAAMnB,eAAe,CAACuC,aAAa,CAAC9B,EAAE,CAAC;QACvCU,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDuB,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,SAAS;MACRhB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAChC,OAAO,IAAI,CAACD,EAAE,EAAE;IAErB,IAAI;MACF,MAAMV,cAAc,CAAC4C,SAAS,CAAClC,EAAE,EAAE;QAAEgB,MAAM,EAAEH,UAAU,CAACG,MAAM;QAAEC,OAAO,EAAEJ,UAAU,CAACI;MAAQ,CAAC,CAAC;MAC9F;MACA,MAAMkB,cAAc,GAAG,MAAM7C,cAAc,CAAC+B,UAAU,CAACrB,EAAE,CAAC;MAC1DE,UAAU,CAACiC,cAAc,CAAC;MAC1BrB,aAAa,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZD,OAAO,CAACpB,KAAK,CAAC,0BAA0B,EAAEqB,GAAG,CAAC;MAC9CE,KAAK,CAAC,4CAA4C,CAAC;IACrD;EACF,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKuC,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxFxC,OAAA;QAAKuC,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,IAAIpC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEJ,OAAA;MAAKuC,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxFxC,OAAA;QAAKuC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxC,OAAA;UAAGuC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAEhC,KAAK,IAAI;QAAmB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrF5C,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCT,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG/C,OAAO,CAACgD,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EACjE,MAAMC,YAAY,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAGrD,OAAO,CAACgD,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EAClE,MAAMG,OAAO,GAAGF,YAAY,GAAGP,WAAW;EAE1C,oBACEjD,OAAA;IAAKuC,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5DxC,OAAA;MAAKuC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDxC,OAAA;QAAKuC,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7ExC,OAAA;UAAKuC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBAExDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAKuC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGxC,OAAA;gBACE2D,GAAG,EAAE9D,WAAW,CAACO,OAAO,CAACwD,MAAM,CAAClD,aAAa,CAAC,CAAE;gBAChDmD,GAAG,EAAE/D,WAAW,CAACM,OAAO,CAACwD,MAAM,CAAClD,aAAa,CAAC,EAAEN,OAAO,CAAC0D,IAAI,CAAE;gBAC9DvB,SAAS,EAAC;cAA0C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELxC,OAAO,CAACwD,MAAM,CAACG,MAAM,GAAG,CAAC,iBACxB/D,OAAA;cAAKuC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCpC,OAAO,CAACwD,MAAM,CAACP,GAAG,CAAC,CAACW,KAAK,EAAEC,KAAK,kBAC/BjE,OAAA;gBAEE6C,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACsD,KAAK,CAAE;gBACvC1B,SAAS,EAAE,4CACT7B,aAAa,KAAKuD,KAAK,GAAG,sBAAsB,GAAG,EAAE,EACpD;gBAAAzB,QAAA,eAEHxC,OAAA;kBACE2D,GAAG,EAAE9D,WAAW,CAACmE,KAAK,CAAE;kBACxBH,GAAG,EAAE/D,WAAW,CAACkE,KAAK,EAAE5D,OAAO,CAAC0D,IAAI,CAAE;kBACtCvB,SAAS,EAAC;gBAA0C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC,GAVGqB,KAAK;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5C,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAKuC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CxC,OAAA;gBAAIuC,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EAAEpC,OAAO,CAAC0D;cAAI;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAG9F5C,OAAA;gBACE6C,OAAO,EAAEf,oBAAqB;gBAC9BoC,QAAQ,EAAEpD,eAAgB;gBAC1ByB,SAAS,EAAC,mIAAmI;gBAC7I4B,KAAK,EAAEvD,YAAY,GAAG,sBAAsB,GAAG,iBAAkB;gBAAA4B,QAAA,EAEhE5B,YAAY,gBACXZ,OAAA,CAACR,cAAc;kBAAC+C,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnD5C,OAAA,CAACT,SAAS;kBAACgD,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACvG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN5C,OAAA;cAAKuC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxC,OAAA;gBAAKuC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/B,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,EAAEC,CAAC,kBACtBtE,OAAA,CAACV,QAAQ;kBAEPiD,SAAS,EAAE,WACT+B,CAAC,GAAGpB,IAAI,CAACqB,KAAK,CAACnE,OAAO,CAACe,MAAM,CAAC,GAC1B,iBAAiB,GACjB,kCAAkC;gBACrC,GALEmD,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMP,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5C,OAAA;gBAAMuC,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,GAC5DpC,OAAO,CAACe,MAAM,EAAC,IAAE,EAACf,OAAO,CAACoE,OAAO,CAACT,MAAM,EAAC,WAC5C;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGN5C,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxC,OAAA;gBAAKuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxC,OAAA;kBAAMuC,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAClE5C,iBAAiB,CAACqD,WAAW;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,EACNc,OAAO,GAAG,CAAC,iBACV1D,OAAA;kBAAMuC,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,GAAC,OACtD,EAAC5C,iBAAiB,CAAC8D,OAAO,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5C,OAAA;gBAAGuC,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,GAAC,cAC/C,EAACpC,OAAO,CAACgD,MAAM,CAACW,MAAM,EAAC,QAAM,EAAC3D,OAAO,CAACgD,MAAM,CAACW,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN5C,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxC,OAAA;gBAAIuC,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrF5C,OAAA;gBAAGuC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEpC,OAAO,CAACqE;cAAW;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,EAGLxC,OAAO,CAACsE,QAAQ,IAAItE,OAAO,CAACsE,QAAQ,CAACX,MAAM,GAAG,CAAC,iBAC9C/D,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxC,OAAA;gBAAIuC,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClF5C,OAAA;gBAAIuC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BpC,OAAO,CAACsE,QAAQ,CAACrB,GAAG,CAAC,CAACsB,OAAO,EAAEV,KAAK,kBACnCjE,OAAA;kBAAgBuC,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBAC5ExC,OAAA;oBAAMuC,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC9D+B,OAAO;gBAAA,GAFDV,KAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChExC,OAAA;YAAIuC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7F5C,OAAA;YAAKuC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClEpC,OAAO,CAACgD,MAAM,CAACC,GAAG,CAAC,CAACE,KAAK,EAAEU,KAAK,kBAC/BjE,OAAA;cAAiBuC,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACrFxC,OAAA;gBAAKuC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDxC,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIuC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAEe,KAAK,CAACqB;kBAAS;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnF5C,OAAA;oBAAGuC,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC/D5C,iBAAiB,CAAC2D,KAAK,CAACA,KAAK;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACHW,KAAK,CAACsB,aAAa,GAAGtB,KAAK,CAACA,KAAK,iBAChCvD,OAAA;oBAAGuC,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,EACjE5C,iBAAiB,CAAC2D,KAAK,CAACsB,aAAa;kBAAC;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5C,OAAA;kBAAKuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACxBe,KAAK,CAACuB,QAAQ,GAAG,CAAC,iBACjB9E,OAAA;oBAAMuC,SAAS,EAAC,oHAAoH;oBAAAC,QAAA,GACjIe,KAAK,CAACuB,QAAQ,EAAC,OAClB;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eACD5C,OAAA;oBAAGuC,SAAS,EAAE,WAAWgB,KAAK,CAACwB,OAAO,GAAG,oCAAoC,GAAG,gCAAgC,EAAG;oBAAAvC,QAAA,EAChHe,KAAK,CAACwB,OAAO,GAAG,aAAaxB,KAAK,CAACyB,UAAU,GAAG,GAAG;kBAAc;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEiF,IAAI,EAAE1B,KAAK,CAAC2B,UAAW;gBACvBC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB7C,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,GAC1H,UACS,EAACe,KAAK,CAACqB,SAAS;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA,GA/BIqB,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChExC,OAAA;YAAIuC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAE5FxC,OAAO,CAACoE,OAAO,CAACT,MAAM,GAAG,CAAC,gBACzB/D,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBpC,OAAO,CAACoE,OAAO,CAACnB,GAAG,CAAC,CAACgC,MAAM,EAAEpB,KAAK,kBACjCjE,OAAA;cAAiBuC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAC7ExC,OAAA;gBAAKuC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCxC,OAAA;kBAAKuC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC/B,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,EAAEC,CAAC,kBACtBtE,OAAA,CAACV,QAAQ;oBAEPiD,SAAS,EAAE,WACT+B,CAAC,GAAGe,MAAM,CAAClE,MAAM,GAAG,iBAAiB,GAAG,kCAAkC;kBACzE,GAHEmD,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIP,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5C,OAAA;kBAAMuC,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAAE6C,MAAM,CAACnE;gBAAQ;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5F5C,OAAA;kBAAMuC,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAC5D,IAAI8C,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5C,OAAA;gBAAGuC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE6C,MAAM,CAACjE;cAAO;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAjB5DqB,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN5C,OAAA;YAAGuC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACxG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAlTID,iBAAiB;EAAA,QACNZ,SAAS,EAYIM,OAAO;AAAA;AAAA8F,EAAA,GAb/BxF,iBAAiB;AAoTvB,eAAeA,iBAAiB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}