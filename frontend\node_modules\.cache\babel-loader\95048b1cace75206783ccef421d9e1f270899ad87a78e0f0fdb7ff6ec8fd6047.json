{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const authService = {\n  // Login user\n  login: async (email, password) => {\n    const response = await api.post('/auth/login', {\n      email,\n      password\n    });\n    return response.data;\n  },\n  // Register user\n  register: async (name, email, password) => {\n    const response = await api.post('/auth/register', {\n      name,\n      email,\n      password\n    });\n    return response.data;\n  },\n  // Logout user\n  logout: async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n    }\n  },\n  // Get current user\n  getCurrentUser: async () => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n  // Refresh token\n  refreshToken: async () => {\n    const response = await api.post('/auth/refresh');\n    return response.data;\n  },\n  // Forgot password\n  forgotPassword: async email => {\n    const response = await api.post('/auth/forgot-password', {\n      email\n    });\n    return response.data;\n  },\n  // Reset password\n  resetPassword: async (token, password) => {\n    const response = await api.post('/auth/reset-password', {\n      token,\n      password\n    });\n    return response.data;\n  },\n  // Change password\n  changePassword: async (currentPassword, newPassword) => {\n    const response = await api.post('/auth/change-password', {\n      currentPassword,\n      newPassword\n    });\n    return response.data;\n  },\n  // Verify email\n  verifyEmail: async token => {\n    const response = await api.post('/auth/verify-email', {\n      token\n    });\n    return response.data;\n  },\n  // Resend verification email\n  resendVerification: async email => {\n    const response = await api.post('/auth/resend-verification', {\n      email\n    });\n    return response.data;\n  },\n  // Check if email exists\n  checkEmailExists: async email => {\n    const response = await api.post('/auth/check-email', {\n      email\n    });\n    return response.data;\n  },\n  // Social login (Google, Facebook, etc.)\n  socialLogin: async (provider, token) => {\n    const response = await api.post(`/auth/social/${provider}`, {\n      token\n    });\n    return response.data;\n  },\n  // Update user profile\n  updateProfile: async userData => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data;\n  },\n  // Delete account\n  deleteAccount: async password => {\n    const response = await api.delete('/auth/account', {\n      data: {\n        password\n      }\n    });\n    return response.data;\n  },\n  // Get user sessions\n  getUserSessions: async () => {\n    const response = await api.get('/auth/sessions');\n    return response.data;\n  },\n  // Revoke session\n  revokeSession: async sessionId => {\n    const response = await api.delete(`/auth/sessions/${sessionId}`);\n    return response.data;\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "authService", "login", "email", "password", "response", "post", "data", "register", "name", "logout", "error", "console", "removeItem", "getCurrentUser", "get", "refreshToken", "forgotPassword", "resetPassword", "changePassword", "currentPassword", "newPassword", "verifyEmail", "resendVerification", "checkEmailExists", "socialLogin", "provider", "updateProfile", "userData", "put", "deleteAccount", "delete", "getUserSessions", "revokeSession", "sessionId"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport const authService = {\n  // Login user\n  login: async (email, password) => {\n    const response = await api.post('/auth/login', { email, password });\n    return response.data;\n  },\n\n  // Register user\n  register: async (name, email, password) => {\n    const response = await api.post('/auth/register', { name, email, password });\n    return response.data;\n  },\n\n  // Logout user\n  logout: async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n    }\n  },\n\n  // Get current user\n  getCurrentUser: async () => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n\n  // Refresh token\n  refreshToken: async () => {\n    const response = await api.post('/auth/refresh');\n    return response.data;\n  },\n\n  // Forgot password\n  forgotPassword: async (email) => {\n    const response = await api.post('/auth/forgot-password', { email });\n    return response.data;\n  },\n\n  // Reset password\n  resetPassword: async (token, password) => {\n    const response = await api.post('/auth/reset-password', { token, password });\n    return response.data;\n  },\n\n  // Change password\n  changePassword: async (currentPassword, newPassword) => {\n    const response = await api.post('/auth/change-password', {\n      currentPassword,\n      newPassword,\n    });\n    return response.data;\n  },\n\n  // Verify email\n  verifyEmail: async (token) => {\n    const response = await api.post('/auth/verify-email', { token });\n    return response.data;\n  },\n\n  // Resend verification email\n  resendVerification: async (email) => {\n    const response = await api.post('/auth/resend-verification', { email });\n    return response.data;\n  },\n\n  // Check if email exists\n  checkEmailExists: async (email) => {\n    const response = await api.post('/auth/check-email', { email });\n    return response.data;\n  },\n\n  // Social login (Google, Facebook, etc.)\n  socialLogin: async (provider, token) => {\n    const response = await api.post(`/auth/social/${provider}`, { token });\n    return response.data;\n  },\n\n  // Update user profile\n  updateProfile: async (userData) => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data;\n  },\n\n  // Delete account\n  deleteAccount: async (password) => {\n    const response = await api.delete('/auth/account', {\n      data: { password }\n    });\n    return response.data;\n  },\n\n  // Get user sessions\n  getUserSessions: async () => {\n    const response = await api.get('/auth/sessions');\n    return response.data;\n  },\n\n  // Revoke session\n  revokeSession: async (sessionId) => {\n    const response = await api.delete(`/auth/sessions/${sessionId}`);\n    return response.data;\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMK,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAChC,MAAMC,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,aAAa,EAAE;MAAEH,KAAK;MAAEC;IAAS,CAAC,CAAC;IACnE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,QAAQ,EAAE,MAAAA,CAAOC,IAAI,EAAEN,KAAK,EAAEC,QAAQ,KAAK;IACzC,MAAMC,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,gBAAgB,EAAE;MAAEG,IAAI;MAAEN,KAAK;MAAEC;IAAS,CAAC,CAAC;IAC5E,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,IAAI;MACF,MAAMrB,GAAG,CAACiB,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRb,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;IAClC;EACF,CAAC;EAED;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAMT,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,UAAU,CAAC;IAC1C,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMX,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,eAAe,CAAC;IAChD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,cAAc,EAAE,MAAOd,KAAK,IAAK;IAC/B,MAAME,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,uBAAuB,EAAE;MAAEH;IAAM,CAAC,CAAC;IACnE,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAW,aAAa,EAAE,MAAAA,CAAOrB,KAAK,EAAEO,QAAQ,KAAK;IACxC,MAAMC,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,sBAAsB,EAAE;MAAET,KAAK;MAAEO;IAAS,CAAC,CAAC;IAC5E,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,cAAc,EAAE,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IACtD,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,uBAAuB,EAAE;MACvDc,eAAe;MACfC;IACF,CAAC,CAAC;IACF,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAe,WAAW,EAAE,MAAOzB,KAAK,IAAK;IAC5B,MAAMQ,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,oBAAoB,EAAE;MAAET;IAAM,CAAC,CAAC;IAChE,OAAOQ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,kBAAkB,EAAE,MAAOpB,KAAK,IAAK;IACnC,MAAME,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,2BAA2B,EAAE;MAAEH;IAAM,CAAC,CAAC;IACvE,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAiB,gBAAgB,EAAE,MAAOrB,KAAK,IAAK;IACjC,MAAME,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,mBAAmB,EAAE;MAAEH;IAAM,CAAC,CAAC;IAC/D,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkB,WAAW,EAAE,MAAAA,CAAOC,QAAQ,EAAE7B,KAAK,KAAK;IACtC,MAAMQ,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,IAAI,CAAC,gBAAgBoB,QAAQ,EAAE,EAAE;MAAE7B;IAAM,CAAC,CAAC;IACtE,OAAOQ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAoB,aAAa,EAAE,MAAOC,QAAQ,IAAK;IACjC,MAAMvB,QAAQ,GAAG,MAAMhB,GAAG,CAACwC,GAAG,CAAC,eAAe,EAAED,QAAQ,CAAC;IACzD,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAuB,aAAa,EAAE,MAAO1B,QAAQ,IAAK;IACjC,MAAMC,QAAQ,GAAG,MAAMhB,GAAG,CAAC0C,MAAM,CAAC,eAAe,EAAE;MACjDxB,IAAI,EAAE;QAAEH;MAAS;IACnB,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAyB,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,MAAM3B,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,gBAAgB,CAAC;IAChD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA0B,aAAa,EAAE,MAAOC,SAAS,IAAK;IAClC,MAAM7B,QAAQ,GAAG,MAAMhB,GAAG,CAAC0C,MAAM,CAAC,kBAAkBG,SAAS,EAAE,CAAC;IAChE,OAAO7B,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}