{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { productService } from '../services/api';\nimport { formatPriceIndian } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  var _product$images$selec, _product$images$selec2;\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: ''\n  });\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProduct();\n  }, [id]);\n  const handleReviewSubmit = async e => {\n    e.preventDefault();\n    if (!product || !id) return;\n    try {\n      await productService.addReview(id, {\n        rating: reviewForm.rating,\n        comment: reviewForm.comment\n      });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({\n        userName: '',\n        rating: 5,\n        comment: ''\n      });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-4 text-gray-600\",\n        children: \"Loading product...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-600\",\n        children: error || 'Product not found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full overflow-hidden rounded-lg bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof product.images[selectedImage] === 'string' ? product.images[selectedImage] : (_product$images$selec = product.images[selectedImage]) === null || _product$images$selec === void 0 ? void 0 : _product$images$selec.url,\n            alt: typeof product.images[selectedImage] === 'string' ? product.name : ((_product$images$selec2 = product.images[selectedImage]) === null || _product$images$selec2 === void 0 ? void 0 : _product$images$selec2.alt) || product.name,\n            className: \"w-full h-[500px] object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 grid grid-cols-4 gap-2\",\n          children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedImage(index),\n            className: `relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${selectedImage === index ? 'ring-2 ring-primary-500' : 'hover:ring-2 hover:ring-gray-300'}`,\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: typeof image === 'string' ? image : image === null || image === void 0 ? void 0 : image.url,\n              alt: typeof image === 'string' ? `${product.name} ${index + 1}` : (image === null || image === void 0 ? void 0 : image.alt) || `${product.name} ${index + 1}`,\n              className: \"h-full w-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n              className: `h-5 w-5 ${star <= product.rating ? 'text-yellow-400' : 'text-gray-200'}`\n            }, star, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-600\",\n            children: [\"(\", product.reviews.length, \" reviews)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Price Comparison\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: product.prices.slice().sort((a, b) => a.price - b.price).map(price => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium\",\n                  children: price.storeName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: formatPriceIndian(price.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: price.productUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"btn-primary\",\n                children: \"Visit Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 21\n              }, this)]\n            }, price.storeName, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 whitespace-pre-line\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-6\",\n            children: \"Customer Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleReviewSubmit,\n            className: \"card mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: reviewForm.userName,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    userName: e.target.value\n                  }),\n                  required: true,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 flex items-center gap-1\",\n                  children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => setReviewForm({\n                      ...reviewForm,\n                      rating: star\n                    }),\n                    className: \"p-1 hover:scale-110 transition-transform\",\n                    children: star <= reviewForm.rating ? /*#__PURE__*/_jsxDEV(StarIcon, {\n                      className: \"h-6 w-6 text-yellow-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(StarOutlineIcon, {\n                      className: \"h-6 w-6 text-gray-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 27\n                    }, this)\n                  }, star, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: reviewForm.comment,\n                  onChange: e => setReviewForm({\n                    ...reviewForm,\n                    comment: e.target.value\n                  }),\n                  required: true,\n                  rows: 4,\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn-primary w-full\",\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: product.reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: review.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: new Date(review.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-1\",\n                children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: `h-4 w-4 ${star <= review.rating ? 'text-yellow-400' : 'text-gray-200'}`\n                }, star, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"ZWD6p/O0U8BQgOaZepm1bkTvnLQ=\", false, function () {\n  return [useParams];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "StarIcon", "StarOutlineIcon", "productService", "formatPriceIndian", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "_product$images$selec", "_product$images$selec2", "id", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "reviewForm", "setReviewForm", "userName", "rating", "comment", "fetchProduct", "data", "getProduct", "err", "console", "handleReviewSubmit", "e", "preventDefault", "add<PERSON>eview", "updatedProduct", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "images", "url", "alt", "name", "length", "map", "image", "index", "onClick", "star", "reviews", "prices", "slice", "sort", "a", "b", "price", "storeName", "href", "productUrl", "target", "rel", "description", "onSubmit", "type", "value", "onChange", "required", "rows", "review", "Date", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/pages/ProductDetailPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { Product } from '../types';\nimport { productService } from '../services/api';\nimport { formatPriceIndian } from '../utils/currency';\n\nconst ProductDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const [product, setProduct] = useState<Product | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [reviewForm, setReviewForm] = useState({\n    userName: '',\n    rating: 5,\n    comment: '',\n  });\n\n  useEffect(() => {\n    const fetchProduct = async () => {\n      if (!id) return;\n      setLoading(true);\n      try {\n        const data = await productService.getProduct(id);\n        setProduct(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load product. Please try again later.');\n        console.error('Error fetching product:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id]);\n\n  const handleReviewSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!product || !id) return;\n\n    try {\n      await productService.addReview(id, { rating: reviewForm.rating, comment: reviewForm.comment });\n      // Refresh the product data to get the updated reviews\n      const updatedProduct = await productService.getProduct(id);\n      setProduct(updatedProduct);\n      setReviewForm({ userName: '', rating: 5, comment: '' });\n    } catch (err) {\n      console.error('Error submitting review:', err);\n      alert('Failed to submit review. Please try again.');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">Loading product...</p>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"text-center py-12\">\n        <p className=\"text-red-600\">{error || 'Product not found'}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-gradient-to-br from-purple-50 via-blue-50 to-white min-h-screen\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Image Gallery */}\n        <div>\n          <div className=\"w-full overflow-hidden rounded-lg bg-gray-100\">\n            <img\n              src={typeof product.images[selectedImage] === 'string'\n                ? product.images[selectedImage]\n                : product.images[selectedImage]?.url}\n              alt={typeof product.images[selectedImage] === 'string'\n                ? product.name\n                : product.images[selectedImage]?.alt || product.name}\n              className=\"w-full h-[500px] object-contain\"\n            />\n          </div>\n          {product.images.length > 1 && (\n            <div className=\"mt-4 grid grid-cols-4 gap-2\">\n              {product.images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImage(index)}\n                  className={`relative aspect-square overflow-hidden rounded-lg bg-gray-100 ${\n                    selectedImage === index\n                      ? 'ring-2 ring-primary-500'\n                      : 'hover:ring-2 hover:ring-gray-300'\n                  }`}\n                >\n                  <img\n                    src={typeof image === 'string' ? image : image?.url}\n                    alt={typeof image === 'string'\n                      ? `${product.name} ${index + 1}`\n                      : image?.alt || `${product.name} ${index + 1}`}\n                    className=\"h-full w-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{product.name}</h1>\n\n          {/* Rating */}\n          <div className=\"mt-4 flex items-center\">\n            <div className=\"flex items-center\">\n              {[1, 2, 3, 4, 5].map((star) => (\n                <StarIcon\n                  key={star}\n                  className={`h-5 w-5 ${\n                    star <= product.rating\n                      ? 'text-yellow-400'\n                      : 'text-gray-200'\n                  }`}\n                />\n              ))}\n            </div>\n            <span className=\"ml-2 text-gray-600\">\n              ({product.reviews.length} reviews)\n            </span>\n          </div>\n\n          {/* Price Comparison */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Price Comparison</h2>\n            <div className=\"space-y-4\">\n              {product.prices\n                .slice()\n                .sort((a, b) => a.price - b.price)\n                .map((price) => (\n                  <div\n                    key={price.storeName}\n                    className=\"flex items-center justify-between p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\"\n                  >\n                    <div>\n                      <h3 className=\"font-medium\">{price.storeName}</h3>\n                      <p className=\"text-2xl font-bold\">\n                        {formatPriceIndian(price.price)}\n                      </p>\n                    </div>\n                    <a\n                      href={price.productUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"btn-primary\"\n                    >\n                      Visit Store\n                    </a>\n                  </div>\n                ))}\n            </div>\n          </div>\n\n          {/* Description */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Description</h2>\n            <p className=\"text-gray-600 whitespace-pre-line\">\n              {product.description}\n            </p>\n          </div>\n\n          {/* Reviews */}\n          <div className=\"mt-12\">\n            <h2 className=\"text-xl font-semibold mb-6\">Customer Reviews</h2>\n\n            {/* Review Form */}\n            <form onSubmit={handleReviewSubmit} className=\"card mb-8\">\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={reviewForm.userName}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, userName: e.target.value })\n                    }\n                    required\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Rating\n                  </label>\n                  <div className=\"mt-1 flex items-center gap-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <button\n                        key={star}\n                        type=\"button\"\n                        onClick={() =>\n                          setReviewForm({ ...reviewForm, rating: star })\n                        }\n                        className=\"p-1 hover:scale-110 transition-transform\"\n                      >\n                        {star <= reviewForm.rating ? (\n                          <StarIcon className=\"h-6 w-6 text-yellow-400\" />\n                        ) : (\n                          <StarOutlineIcon className=\"h-6 w-6 text-gray-300\" />\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Review\n                  </label>\n                  <textarea\n                    value={reviewForm.comment}\n                    onChange={(e) =>\n                      setReviewForm({ ...reviewForm, comment: e.target.value })\n                    }\n                    required\n                    rows={4}\n                    className=\"input-field\"\n                  />\n                </div>\n\n                <button type=\"submit\" className=\"btn-primary w-full\">\n                  Submit Review\n                </button>\n              </div>\n            </form>\n\n            {/* Reviews List */}\n            <div className=\"space-y-6\">\n              {product.reviews.map((review, index) => (\n                <div key={index} className=\"card\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">{review.userName}</span>\n                    <span className=\"text-sm text-gray-500\">\n                      {new Date(review.createdAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center mt-1\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <StarIcon\n                        key={star}\n                        className={`h-4 w-4 ${\n                          star <= review.rating\n                            ? 'text-yellow-400'\n                            : 'text-gray-200'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                  <p className=\"mt-2 text-gray-600\">{review.comment}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASA,QAAQ,IAAIC,eAAe,QAAQ,6BAA6B;AAEzE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,iBAAiB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACxC,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAC1C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC;IAC3CuB,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF1B,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACd,EAAE,EAAE;MACTI,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMW,IAAI,GAAG,MAAMvB,cAAc,CAACwB,UAAU,CAAChB,EAAE,CAAC;QAChDE,UAAU,CAACa,IAAI,CAAC;QAChBT,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZX,QAAQ,CAAC,iDAAiD,CAAC;QAC3DY,OAAO,CAACb,KAAK,CAAC,yBAAyB,EAAEY,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDU,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACd,EAAE,CAAC,CAAC;EAER,MAAMmB,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACpB,OAAO,IAAI,CAACD,EAAE,EAAE;IAErB,IAAI;MACF,MAAMR,cAAc,CAAC8B,SAAS,CAACtB,EAAE,EAAE;QAAEY,MAAM,EAAEH,UAAU,CAACG,MAAM;QAAEC,OAAO,EAAEJ,UAAU,CAACI;MAAQ,CAAC,CAAC;MAC9F;MACA,MAAMU,cAAc,GAAG,MAAM/B,cAAc,CAACwB,UAAU,CAAChB,EAAE,CAAC;MAC1DE,UAAU,CAACqB,cAAc,CAAC;MAC1Bb,aAAa,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEY,GAAG,CAAC;MAC9CO,KAAK,CAAC,4CAA4C,CAAC;IACrD;EACF,CAAC;EAED,IAAIrB,OAAO,EAAE;IACX,oBACER,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/B,OAAA;QAAK8B,SAAS,EAAC;MAA4F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClHnC,OAAA;QAAG8B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,IAAIzB,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEN,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/B,OAAA;QAAG8B,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAErB,KAAK,IAAI;MAAmB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,gHAAgH;IAAAC,QAAA,eAC7H/B,OAAA;MAAK8B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD/B,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAK8B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5D/B,OAAA;YACEoC,GAAG,EAAE,OAAO9B,OAAO,CAAC+B,MAAM,CAACzB,aAAa,CAAC,KAAK,QAAQ,GAClDN,OAAO,CAAC+B,MAAM,CAACzB,aAAa,CAAC,IAAAT,qBAAA,GAC7BG,OAAO,CAAC+B,MAAM,CAACzB,aAAa,CAAC,cAAAT,qBAAA,uBAA7BA,qBAAA,CAA+BmC,GAAI;YACvCC,GAAG,EAAE,OAAOjC,OAAO,CAAC+B,MAAM,CAACzB,aAAa,CAAC,KAAK,QAAQ,GAClDN,OAAO,CAACkC,IAAI,GACZ,EAAApC,sBAAA,GAAAE,OAAO,CAAC+B,MAAM,CAACzB,aAAa,CAAC,cAAAR,sBAAA,uBAA7BA,sBAAA,CAA+BmC,GAAG,KAAIjC,OAAO,CAACkC,IAAK;YACvDV,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACL7B,OAAO,CAAC+B,MAAM,CAACI,MAAM,GAAG,CAAC,iBACxBzC,OAAA;UAAK8B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCzB,OAAO,CAAC+B,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/B5C,OAAA;YAEE6C,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC+B,KAAK,CAAE;YACvCd,SAAS,EAAE,iEACTlB,aAAa,KAAKgC,KAAK,GACnB,yBAAyB,GACzB,kCAAkC,EACrC;YAAAb,QAAA,eAEH/B,OAAA;cACEoC,GAAG,EAAE,OAAOO,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEL,GAAI;cACpDC,GAAG,EAAE,OAAOI,KAAK,KAAK,QAAQ,GAC1B,GAAGrC,OAAO,CAACkC,IAAI,IAAII,KAAK,GAAG,CAAC,EAAE,GAC9B,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEJ,GAAG,KAAI,GAAGjC,OAAO,CAACkC,IAAI,IAAII,KAAK,GAAG,CAAC,EAAG;cACjDd,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC,GAdGS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnC,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAI8B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEzB,OAAO,CAACkC;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGpEnC,OAAA;UAAK8B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/B,OAAA;YAAK8B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACW,GAAG,CAAEI,IAAI,iBACxB9C,OAAA,CAACL,QAAQ;cAEPmC,SAAS,EAAE,WACTgB,IAAI,IAAIxC,OAAO,CAACW,MAAM,GAClB,iBAAiB,GACjB,eAAe;YAClB,GALE6B,IAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnC,OAAA;YAAM8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAACzB,OAAO,CAACyC,OAAO,CAACN,MAAM,EAAC,WAC3B;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAI8B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEnC,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzB,OAAO,CAAC0C,MAAM,CACZC,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC,CACjCX,GAAG,CAAEW,KAAK,iBACTrD,OAAA;cAEE8B,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjH/B,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAI8B,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEsB,KAAK,CAACC;gBAAS;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClDnC,OAAA;kBAAG8B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC9BjC,iBAAiB,CAACuD,KAAK,CAACA,KAAK;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNnC,OAAA;gBACEuD,IAAI,EAAEF,KAAK,CAACG,UAAW;gBACvBC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB5B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,GAhBCkB,KAAK,CAACC,SAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBjB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAI8B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DnC,OAAA;YAAG8B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC7CzB,OAAO,CAACqD;UAAW;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpB/B,OAAA;YAAI8B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGhEnC,OAAA;YAAM4D,QAAQ,EAAEpC,kBAAmB;YAACM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACvD/B,OAAA;cAAK8B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/B,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAO8B,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnC,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEhD,UAAU,CAACE,QAAS;kBAC3B+C,QAAQ,EAAGtC,CAAC,IACVV,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,QAAQ,EAAES,CAAC,CAACgC,MAAM,CAACK;kBAAM,CAAC,CAC1D;kBACDE,QAAQ;kBACRlC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAO8B,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnC,OAAA;kBAAK8B,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACW,GAAG,CAAEI,IAAI,iBACxB9C,OAAA;oBAEE6D,IAAI,EAAC,QAAQ;oBACbhB,OAAO,EAAEA,CAAA,KACP9B,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEG,MAAM,EAAE6B;oBAAK,CAAC,CAC9C;oBACDhB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAEnDe,IAAI,IAAIhC,UAAU,CAACG,MAAM,gBACxBjB,OAAA,CAACL,QAAQ;sBAACmC,SAAS,EAAC;oBAAyB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEhDnC,OAAA,CAACJ,eAAe;sBAACkC,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrD,GAXIW,IAAI;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYH,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAO8B,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnC,OAAA;kBACE8D,KAAK,EAAEhD,UAAU,CAACI,OAAQ;kBAC1B6C,QAAQ,EAAGtC,CAAC,IACVV,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEI,OAAO,EAAEO,CAAC,CAACgC,MAAM,CAACK;kBAAM,CAAC,CACzD;kBACDE,QAAQ;kBACRC,IAAI,EAAE,CAAE;kBACRnC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnC,OAAA;gBAAQ6D,IAAI,EAAC,QAAQ;gBAAC/B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPnC,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzB,OAAO,CAACyC,OAAO,CAACL,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK,kBACjC5C,OAAA;cAAiB8B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC/B/B,OAAA;gBAAK8B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD/B,OAAA;kBAAM8B,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEmC,MAAM,CAAClD;gBAAQ;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDnC,OAAA;kBAAM8B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpC,IAAIoC,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnC,OAAA;gBAAK8B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACW,GAAG,CAAEI,IAAI,iBACxB9C,OAAA,CAACL,QAAQ;kBAEPmC,SAAS,EAAE,WACTgB,IAAI,IAAIoB,MAAM,CAACjD,MAAM,GACjB,iBAAiB,GACjB,eAAe;gBAClB,GALE6B,IAAI;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMV,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnC,OAAA;gBAAG8B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEmC,MAAM,CAAChD;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAnB9CS,KAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAzQID,iBAA2B;EAAA,QAChBP,SAAS;AAAA;AAAA4E,EAAA,GADpBrE,iBAA2B;AA2QjC,eAAeA,iBAAiB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}