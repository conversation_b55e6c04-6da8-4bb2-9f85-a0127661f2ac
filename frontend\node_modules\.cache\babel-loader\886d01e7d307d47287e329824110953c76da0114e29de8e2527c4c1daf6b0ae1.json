{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add token to requests if available\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const productService = {\n  // Get all products with filters\n  getProducts: async (filters = {}, page = 1, limit = 20) => {\n    const params = new URLSearchParams();\n    if (filters.search) params.append('search', filters.search);\n    if (filters.category) params.append('category', filters.category);\n    if (filters.subcategory) params.append('subcategory', filters.subcategory);\n    if (filters.brand) params.append('brand', filters.brand);\n    if (filters.minPrice) params.append('minPrice', filters.minPrice.toString());\n    if (filters.maxPrice) params.append('maxPrice', filters.maxPrice.toString());\n    if (filters.rating) params.append('rating', filters.rating.toString());\n    if (filters.inStock !== undefined) params.append('inStock', filters.inStock.toString());\n    if (filters.featured) params.append('featured', filters.featured.toString());\n    if (filters.trending) params.append('trending', filters.trending.toString());\n    if (filters.sort) params.append('sort', filters.sort);\n    params.append('page', page.toString());\n    params.append('limit', limit.toString());\n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n  // Get single product by ID\n  getProduct: async id => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n  // Get featured products\n  getFeaturedProducts: async (limit = 10) => {\n    const response = await api.get(`/products/featured?limit=${limit}`);\n    return response.data;\n  },\n  // Get trending products\n  getTrendingProducts: async (limit = 10) => {\n    const response = await api.get(`/products/trending?limit=${limit}`);\n    return response.data;\n  },\n  // Get products on sale\n  getSaleProducts: async (limit = 20) => {\n    const response = await api.get(`/products/sale?limit=${limit}`);\n    return response.data;\n  },\n  // Get search suggestions\n  getSearchSuggestions: async query => {\n    if (!query || query.length < 2) return [];\n    const response = await api.get(`/products/suggestions?q=${encodeURIComponent(query)}`);\n    return response.data;\n  },\n  // Get categories\n  getCategories: async () => {\n    const response = await api.get('/categories');\n    return response.data;\n  },\n  // Get brands\n  getBrands: async category => {\n    const params = category ? `?category=${encodeURIComponent(category)}` : '';\n    const response = await api.get(`/products/brands${params}`);\n    return response.data;\n  },\n  // Get price range for category\n  getPriceRange: async category => {\n    const params = category ? `?category=${encodeURIComponent(category)}` : '';\n    const response = await api.get(`/products/price-range${params}`);\n    return response.data;\n  },\n  // Add product review\n  addReview: async (productId, review) => {\n    await api.post(`/products/${productId}/reviews`, review);\n  },\n  // Track product view\n  trackView: async productId => {\n    await api.post(`/products/${productId}/view`);\n  },\n  // Track product click\n  trackClick: async (productId, storeName) => {\n    await api.post(`/products/${productId}/click`, {\n      storeName\n    });\n  },\n  // Compare products\n  compareProducts: async productIds => {\n    const response = await api.post('/products/compare', {\n      productIds\n    });\n    return response.data;\n  }\n};\nexport default productService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "productService", "getProducts", "filters", "page", "limit", "params", "URLSearchParams", "search", "append", "category", "subcategory", "brand", "minPrice", "toString", "maxPrice", "rating", "inStock", "undefined", "featured", "trending", "sort", "response", "get", "data", "getProduct", "id", "getFeaturedProducts", "getTrendingProducts", "getSaleProducts", "getSearchSuggestions", "query", "length", "encodeURIComponent", "getCategories", "getBrands", "getPriceRange", "add<PERSON>eview", "productId", "review", "post", "trackView", "trackClick", "storeName", "compareProducts", "productIds"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/productService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Product, ProductFilters, Category } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add token to requests if available\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport interface SearchSuggestion {\n  type: 'product' | 'category' | 'brand';\n  text: string;\n  count?: number;\n}\n\nexport interface SearchResponse {\n  products: Product[];\n  total: number;\n  page: number;\n  totalPages: number;\n  filters: {\n    categories: Array<{ name: string; count: number }>;\n    brands: Array<{ name: string; count: number }>;\n    priceRanges: Array<{ min: number; max: number; count: number }>;\n    ratings: Array<{ rating: number; count: number }>;\n  };\n}\n\nexport const productService = {\n  // Get all products with filters\n  getProducts: async (filters: ProductFilters = {}, page = 1, limit = 20): Promise<SearchResponse> => {\n    const params = new URLSearchParams();\n    \n    if (filters.search) params.append('search', filters.search);\n    if (filters.category) params.append('category', filters.category);\n    if (filters.subcategory) params.append('subcategory', filters.subcategory);\n    if (filters.brand) params.append('brand', filters.brand);\n    if (filters.minPrice) params.append('minPrice', filters.minPrice.toString());\n    if (filters.maxPrice) params.append('maxPrice', filters.maxPrice.toString());\n    if (filters.rating) params.append('rating', filters.rating.toString());\n    if (filters.inStock !== undefined) params.append('inStock', filters.inStock.toString());\n    if (filters.featured) params.append('featured', filters.featured.toString());\n    if (filters.trending) params.append('trending', filters.trending.toString());\n    if (filters.sort) params.append('sort', filters.sort);\n    \n    params.append('page', page.toString());\n    params.append('limit', limit.toString());\n\n    const response = await api.get(`/products?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get single product by ID\n  getProduct: async (id: string): Promise<Product> => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n\n  // Get featured products\n  getFeaturedProducts: async (limit = 10): Promise<Product[]> => {\n    const response = await api.get(`/products/featured?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get trending products\n  getTrendingProducts: async (limit = 10): Promise<Product[]> => {\n    const response = await api.get(`/products/trending?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get products on sale\n  getSaleProducts: async (limit = 20): Promise<Product[]> => {\n    const response = await api.get(`/products/sale?limit=${limit}`);\n    return response.data;\n  },\n\n  // Get search suggestions\n  getSearchSuggestions: async (query: string): Promise<SearchSuggestion[]> => {\n    if (!query || query.length < 2) return [];\n    \n    const response = await api.get(`/products/suggestions?q=${encodeURIComponent(query)}`);\n    return response.data;\n  },\n\n  // Get categories\n  getCategories: async (): Promise<Category[]> => {\n    const response = await api.get('/categories');\n    return response.data;\n  },\n\n  // Get brands\n  getBrands: async (category?: string): Promise<string[]> => {\n    const params = category ? `?category=${encodeURIComponent(category)}` : '';\n    const response = await api.get(`/products/brands${params}`);\n    return response.data;\n  },\n\n  // Get price range for category\n  getPriceRange: async (category?: string): Promise<{ min: number; max: number }> => {\n    const params = category ? `?category=${encodeURIComponent(category)}` : '';\n    const response = await api.get(`/products/price-range${params}`);\n    return response.data;\n  },\n\n  // Add product review\n  addReview: async (productId: string, review: { rating: number; comment: string }): Promise<void> => {\n    await api.post(`/products/${productId}/reviews`, review);\n  },\n\n  // Track product view\n  trackView: async (productId: string): Promise<void> => {\n    await api.post(`/products/${productId}/view`);\n  },\n\n  // Track product click\n  trackClick: async (productId: string, storeName: string): Promise<void> => {\n    await api.post(`/products/${productId}/click`, { storeName });\n  },\n\n  // Compare products\n  compareProducts: async (productIds: string[]): Promise<Product[]> => {\n    const response = await api.post('/products/compare', { productIds });\n    return response.data;\n  }\n};\n\nexport default productService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAqBF,OAAO,MAAMK,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAAA,CAAOC,OAAuB,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAA8B;IAClG,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIJ,OAAO,CAACK,MAAM,EAAEF,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAEN,OAAO,CAACK,MAAM,CAAC;IAC3D,IAAIL,OAAO,CAACO,QAAQ,EAAEJ,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,OAAO,CAACO,QAAQ,CAAC;IACjE,IAAIP,OAAO,CAACQ,WAAW,EAAEL,MAAM,CAACG,MAAM,CAAC,aAAa,EAAEN,OAAO,CAACQ,WAAW,CAAC;IAC1E,IAAIR,OAAO,CAACS,KAAK,EAAEN,MAAM,CAACG,MAAM,CAAC,OAAO,EAAEN,OAAO,CAACS,KAAK,CAAC;IACxD,IAAIT,OAAO,CAACU,QAAQ,EAAEP,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,OAAO,CAACU,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIX,OAAO,CAACY,QAAQ,EAAET,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,OAAO,CAACY,QAAQ,CAACD,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIX,OAAO,CAACa,MAAM,EAAEV,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAEN,OAAO,CAACa,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC;IACtE,IAAIX,OAAO,CAACc,OAAO,KAAKC,SAAS,EAAEZ,MAAM,CAACG,MAAM,CAAC,SAAS,EAAEN,OAAO,CAACc,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC;IACvF,IAAIX,OAAO,CAACgB,QAAQ,EAAEb,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,OAAO,CAACgB,QAAQ,CAACL,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIX,OAAO,CAACiB,QAAQ,EAAEd,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,OAAO,CAACiB,QAAQ,CAACN,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIX,OAAO,CAACkB,IAAI,EAAEf,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEN,OAAO,CAACkB,IAAI,CAAC;IAErDf,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC;IACtCR,MAAM,CAACG,MAAM,CAAC,OAAO,EAAEJ,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;IAExC,MAAMQ,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,aAAajB,MAAM,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChE,OAAOQ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,UAAU,EAAE,MAAOC,EAAU,IAAuB;IAClD,MAAMJ,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,aAAaG,EAAE,EAAE,CAAC;IACjD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,mBAAmB,EAAE,MAAAA,CAAOtB,KAAK,GAAG,EAAE,KAAyB;IAC7D,MAAMiB,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,4BAA4BlB,KAAK,EAAE,CAAC;IACnE,OAAOiB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,mBAAmB,EAAE,MAAAA,CAAOvB,KAAK,GAAG,EAAE,KAAyB;IAC7D,MAAMiB,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,4BAA4BlB,KAAK,EAAE,CAAC;IACnE,OAAOiB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAK,eAAe,EAAE,MAAAA,CAAOxB,KAAK,GAAG,EAAE,KAAyB;IACzD,MAAMiB,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,wBAAwBlB,KAAK,EAAE,CAAC;IAC/D,OAAOiB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,oBAAoB,EAAE,MAAOC,KAAa,IAAkC;IAC1E,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;IAEzC,MAAMV,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,2BAA2BU,kBAAkB,CAACF,KAAK,CAAC,EAAE,CAAC;IACtF,OAAOT,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,aAAa,EAAE,MAAAA,CAAA,KAAiC;IAC9C,MAAMZ,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,aAAa,CAAC;IAC7C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAW,SAAS,EAAE,MAAOzB,QAAiB,IAAwB;IACzD,MAAMJ,MAAM,GAAGI,QAAQ,GAAG,aAAauB,kBAAkB,CAACvB,QAAQ,CAAC,EAAE,GAAG,EAAE;IAC1E,MAAMY,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,mBAAmBjB,MAAM,EAAE,CAAC;IAC3D,OAAOgB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,aAAa,EAAE,MAAO1B,QAAiB,IAA4C;IACjF,MAAMJ,MAAM,GAAGI,QAAQ,GAAG,aAAauB,kBAAkB,CAACvB,QAAQ,CAAC,EAAE,GAAG,EAAE;IAC1E,MAAMY,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,GAAG,CAAC,wBAAwBjB,MAAM,EAAE,CAAC;IAChE,OAAOgB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,SAAS,EAAE,MAAAA,CAAOC,SAAiB,EAAEC,MAA2C,KAAoB;IAClG,MAAMlD,GAAG,CAACmD,IAAI,CAAC,aAAaF,SAAS,UAAU,EAAEC,MAAM,CAAC;EAC1D,CAAC;EAED;EACAE,SAAS,EAAE,MAAOH,SAAiB,IAAoB;IACrD,MAAMjD,GAAG,CAACmD,IAAI,CAAC,aAAaF,SAAS,OAAO,CAAC;EAC/C,CAAC;EAED;EACAI,UAAU,EAAE,MAAAA,CAAOJ,SAAiB,EAAEK,SAAiB,KAAoB;IACzE,MAAMtD,GAAG,CAACmD,IAAI,CAAC,aAAaF,SAAS,QAAQ,EAAE;MAAEK;IAAU,CAAC,CAAC;EAC/D,CAAC;EAED;EACAC,eAAe,EAAE,MAAOC,UAAoB,IAAyB;IACnE,MAAMvB,QAAQ,GAAG,MAAMjC,GAAG,CAACmD,IAAI,CAAC,mBAAmB,EAAE;MAAEK;IAAW,CAAC,CAAC;IACpE,OAAOvB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAevB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}