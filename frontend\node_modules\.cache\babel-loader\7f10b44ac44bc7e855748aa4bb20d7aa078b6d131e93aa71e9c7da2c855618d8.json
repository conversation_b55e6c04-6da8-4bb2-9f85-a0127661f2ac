{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\components\\\\ChatbotWidget.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { PaperAirplaneIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatbotWidget = () => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const [messages, setMessages] = useState([{\n    from: 'bot',\n    text: 'Hi! How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const messagesEndRef = useRef(null);\n  useEffect(() => {\n    if (open && messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages, open]);\n  const handleSend = () => {\n    if (!input.trim()) return;\n    const userMsg = {\n      from: 'user',\n      text: input\n    };\n    setMessages(prev => [...prev, userMsg]);\n    setInput('');\n    // Send message to backend AI\n    console.log('Sending message to backend:', input);\n    fetch('http://localhost:5000/api/chat', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({\n        message: input\n      })\n    }).then(async res => {\n      console.log('Received response:', res.status);\n      if (!res.ok) {\n        const errorText = await res.text();\n        console.error('Error response:', errorText);\n        throw new Error(`Server responded with ${res.status}: ${errorText}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log('Received data:', data);\n      setMessages(prev => [...prev, {\n        from: 'bot',\n        text: data.reply || 'Sorry, I could not understand that.'\n      }]);\n    }).catch(error => {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        from: 'bot',\n        text: 'Sorry, there was an error contacting the AI.'\n      }]);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed bottom-6 right-6 z-50\",\n    children: [!open && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg p-4 flex items-center justify-center\",\n      \"aria-label\": \"Open chatbot\",\n      onClick: () => setOpen(true),\n      children: /*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n        className: \"w-7 h-7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 9\n    }, this), open && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 h-96 bg-white rounded-lg shadow-2xl flex flex-col border border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center p-3 border-b border-gray-200 bg-blue-600 rounded-t-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white font-semibold\",\n          children: \"AI Chatbot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-white\",\n          onClick: () => setOpen(false),\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-3 space-y-2 bg-gray-50\",\n        children: [messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${msg.from === 'user' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-3 py-2 rounded-lg max-w-[70%] text-sm shadow\n                    ${msg.from === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900'}`,\n            children: msg.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 17\n          }, this)\n        }, idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"flex items-center p-3 border-t border-gray-200 bg-white\",\n        onSubmit: e => {\n          e.preventDefault();\n          handleSend();\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          className: \"flex-1 border rounded-full px-3 py-2 mr-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-400\",\n          type: \"text\",\n          placeholder: \"Type your message...\",\n          value: input,\n          onChange: e => setInput(e.target.value),\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 flex items-center justify-center\",\n          \"aria-label\": \"Send\",\n          children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n            className: \"w-5 h-5 rotate-90\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatbotWidget, \"fjTet4va3T6n58Ud4PZgMpb5LLI=\");\n_c = ChatbotWidget;\nexport default ChatbotWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatbotWidget\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "PaperAirplaneIcon", "ChatBubbleLeftRightIcon", "jsxDEV", "_jsxDEV", "Cha<PERSON>bot<PERSON><PERSON>t", "_s", "open", "<PERSON><PERSON><PERSON>", "messages", "setMessages", "from", "text", "input", "setInput", "messagesEndRef", "current", "scrollIntoView", "behavior", "handleSend", "trim", "userMsg", "prev", "console", "log", "fetch", "method", "headers", "body", "JSON", "stringify", "message", "then", "res", "status", "ok", "errorText", "error", "Error", "json", "data", "reply", "catch", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "msg", "idx", "ref", "onSubmit", "e", "preventDefault", "type", "placeholder", "value", "onChange", "target", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/components/ChatbotWidget.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { PaperAirplaneIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';\n\ninterface Message {\n  from: 'user' | 'bot';\n  text: string;\n}\n\nconst ChatbotWidget: React.FC = () => {\n  const [open, setOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    { from: 'bot', text: 'Hi! How can I help you today?' }\n  ]);\n  const [input, setInput] = useState('');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (open && messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages, open]);\n\n  const handleSend = () => {\n    if (!input.trim()) return;\n    const userMsg = { from: 'user' as const, text: input };\n    setMessages((prev) => [...prev, userMsg]);\n    setInput('');\n    // Send message to backend AI\n    console.log('Sending message to backend:', input);\n    fetch('http://localhost:5000/api/chat', {\n      method: 'POST',\n      headers: { \n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({ message: input })\n    })\n      .then(async res => {\n        console.log('Received response:', res.status);\n        if (!res.ok) {\n          const errorText = await res.text();\n          console.error('Error response:', errorText);\n          throw new Error(`Server responded with ${res.status}: ${errorText}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log('Received data:', data);\n        setMessages((prev) => [\n          ...prev,\n          { from: 'bot', text: data.reply || 'Sorry, I could not understand that.' }\n        ]);\n      })\n      .catch((error) => {\n        console.error('Chat error:', error);\n        setMessages((prev) => [\n          ...prev,\n          { from: 'bot', text: 'Sorry, there was an error contacting the AI.' }\n        ]);\n      });\n  };\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Floating button */}\n      {!open && (\n        <button\n          className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg p-4 flex items-center justify-center\"\n          aria-label=\"Open chatbot\"\n          onClick={() => setOpen(true)}\n        >\n          <ChatBubbleLeftRightIcon className=\"w-7 h-7\" />\n        </button>\n      )}\n      {/* Chat window */}\n      {open && (\n        <div className=\"w-80 h-96 bg-white rounded-lg shadow-2xl flex flex-col border border-gray-200\">\n          {/* Header */}\n          <div className=\"flex justify-between items-center p-3 border-b border-gray-200 bg-blue-600 rounded-t-lg\">\n            <span className=\"text-white font-semibold\">AI Chatbot</span>\n            <button className=\"text-white\" onClick={() => setOpen(false)}>&#10005;</button>\n          </div>\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-3 space-y-2 bg-gray-50\">\n            {messages.map((msg, idx) => (\n              <div\n                key={idx}\n                className={`flex ${msg.from === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`px-3 py-2 rounded-lg max-w-[70%] text-sm shadow\n                    ${msg.from === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900'}`}\n                >\n                  {msg.text}\n                </div>\n              </div>\n            ))}\n            <div ref={messagesEndRef} />\n          </div>\n          {/* Input */}\n          <form\n            className=\"flex items-center p-3 border-t border-gray-200 bg-white\"\n            onSubmit={e => {\n              e.preventDefault();\n              handleSend();\n            }}\n          >\n            <input\n              className=\"flex-1 border rounded-full px-3 py-2 mr-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-400\"\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={input}\n              onChange={e => setInput(e.target.value)}\n              autoFocus\n            />\n            <button\n              type=\"submit\"\n              className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 flex items-center justify-center\"\n              aria-label=\"Send\"\n            >\n              <PaperAirplaneIcon className=\"w-5 h-5 rotate-90\" />\n            </button>\n          </form>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ChatbotWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,iBAAiB,EAAEC,uBAAuB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOzF,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAY,CAClD;IAAEa,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAgC,CAAC,CACvD,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,cAAc,GAAGhB,MAAM,CAAiB,IAAI,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,IAAIQ,cAAc,CAACC,OAAO,EAAE;MAClCD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACT,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAEpB,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACN,KAAK,CAACO,IAAI,CAAC,CAAC,EAAE;IACnB,MAAMC,OAAO,GAAG;MAAEV,IAAI,EAAE,MAAe;MAAEC,IAAI,EAAEC;IAAM,CAAC;IACtDH,WAAW,CAAEY,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC,CAAC;IACzCP,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAS,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEX,KAAK,CAAC;IACjDY,KAAK,CAAC,gCAAgC,EAAE;MACtCC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEC,OAAO,EAAElB;MAAM,CAAC;IACzC,CAAC,CAAC,CACCmB,IAAI,CAAC,MAAMC,GAAG,IAAI;MACjBV,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAES,GAAG,CAACC,MAAM,CAAC;MAC7C,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAMC,SAAS,GAAG,MAAMH,GAAG,CAACrB,IAAI,CAAC,CAAC;QAClCW,OAAO,CAACc,KAAK,CAAC,iBAAiB,EAAED,SAAS,CAAC;QAC3C,MAAM,IAAIE,KAAK,CAAC,yBAAyBL,GAAG,CAACC,MAAM,KAAKE,SAAS,EAAE,CAAC;MACtE;MACA,OAAOH,GAAG,CAACM,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDP,IAAI,CAACQ,IAAI,IAAI;MACZjB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgB,IAAI,CAAC;MACnC9B,WAAW,CAAEY,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEX,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE4B,IAAI,CAACC,KAAK,IAAI;MAAsC,CAAC,CAC3E,CAAC;IACJ,CAAC,CAAC,CACDC,KAAK,CAAEL,KAAK,IAAK;MAChBd,OAAO,CAACc,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC3B,WAAW,CAAEY,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEX,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE;MAA+C,CAAC,CACtE,CAAC;IACJ,CAAC,CAAC;EACN,CAAC;EAED,oBACER,OAAA;IAAKuC,SAAS,EAAC,6BAA6B;IAAAC,QAAA,GAEzC,CAACrC,IAAI,iBACJH,OAAA;MACEuC,SAAS,EAAC,sGAAsG;MAChH,cAAW,cAAc;MACzBE,OAAO,EAAEA,CAAA,KAAMrC,OAAO,CAAC,IAAI,CAAE;MAAAoC,QAAA,eAE7BxC,OAAA,CAACF,uBAAuB;QAACyC,SAAS,EAAC;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACT,EAEA1C,IAAI,iBACHH,OAAA;MAAKuC,SAAS,EAAC,+EAA+E;MAAAC,QAAA,gBAE5FxC,OAAA;QAAKuC,SAAS,EAAC,yFAAyF;QAAAC,QAAA,gBACtGxC,OAAA;UAAMuC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5D7C,OAAA;UAAQuC,SAAS,EAAC,YAAY;UAACE,OAAO,EAAEA,CAAA,KAAMrC,OAAO,CAAC,KAAK,CAAE;UAAAoC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eAEN7C,OAAA;QAAKuC,SAAS,EAAC,iDAAiD;QAAAC,QAAA,GAC7DnC,QAAQ,CAACyC,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACrBhD,OAAA;UAEEuC,SAAS,EAAE,QAAQQ,GAAG,CAACxC,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAiC,QAAA,eAE3ExC,OAAA;YACEuC,SAAS,EAAE;AAC7B,sBAAsBQ,GAAG,CAACxC,IAAI,KAAK,MAAM,GAAG,wBAAwB,GAAG,2BAA2B,EAAG;YAAAiC,QAAA,EAElFO,GAAG,CAACvC;UAAI;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GARDG,GAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASL,CACN,CAAC,eACF7C,OAAA;UAAKiD,GAAG,EAAEtC;QAAe;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAEN7C,OAAA;QACEuC,SAAS,EAAC,yDAAyD;QACnEW,QAAQ,EAAEC,CAAC,IAAI;UACbA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBrC,UAAU,CAAC,CAAC;QACd,CAAE;QAAAyB,QAAA,gBAEFxC,OAAA;UACEuC,SAAS,EAAC,uGAAuG;UACjHc,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sBAAsB;UAClCC,KAAK,EAAE9C,KAAM;UACb+C,QAAQ,EAAEL,CAAC,IAAIzC,QAAQ,CAACyC,CAAC,CAACM,MAAM,CAACF,KAAK,CAAE;UACxCG,SAAS;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACF7C,OAAA;UACEqD,IAAI,EAAC,QAAQ;UACbd,SAAS,EAAC,4FAA4F;UACtG,cAAW,MAAM;UAAAC,QAAA,eAEjBxC,OAAA,CAACH,iBAAiB;YAAC0C,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAvHID,aAAuB;AAAA0D,EAAA,GAAvB1D,aAAuB;AAyH7B,eAAeA,aAAa;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}