{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const wishlistService = {\n  // Get user's wishlist\n  getWishlist: async () => {\n    try {\n      const response = await api.get('/wishlist');\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist API not available, returning empty array');\n      // Return empty array if API is not available\n      return [];\n    }\n  },\n  // Add product to wishlist\n  addToWishlist: async productId => {\n    try {\n      const response = await api.post(`/wishlist/add/${productId}`);\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist add API not available');\n      throw error;\n    }\n  },\n  // Remove product from wishlist\n  removeFromWishlist: async productId => {\n    try {\n      const response = await api.delete(`/wishlist/remove/${productId}`);\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist remove API not available');\n      throw error;\n    }\n  },\n  // Check if product is in wishlist\n  checkWishlist: async productId => {\n    try {\n      const response = await api.get(`/wishlist/check/${productId}`);\n      return response.data.inWishlist || false;\n    } catch (error) {\n      console.error('Error checking wishlist:', error);\n      return false;\n    }\n  },\n  // Clear entire wishlist\n  clearWishlist: async () => {\n    const response = await api.delete('/wishlist/clear');\n    return response.data;\n  },\n  // Get wishlist count\n  getWishlistCount: async () => {\n    try {\n      const response = await api.get('/wishlist/count');\n      return response.data.count || 0;\n    } catch (error) {\n      console.error('Error getting wishlist count:', error);\n      return 0;\n    }\n  },\n  // Move wishlist item to cart\n  moveToCart: async productId => {\n    const response = await api.post(`/wishlist/move-to-cart/${productId}`);\n    return response.data;\n  },\n  // Set price alert for wishlist item\n  setPriceAlert: async (productId, targetPrice) => {\n    const response = await api.post(`/wishlist/price-alert/${productId}`, {\n      targetPrice\n    });\n    return response.data;\n  },\n  // Remove price alert\n  removePriceAlert: async productId => {\n    const response = await api.delete(`/wishlist/price-alert/${productId}`);\n    return response.data;\n  },\n  // Get price alerts\n  getPriceAlerts: async () => {\n    const response = await api.get('/wishlist/price-alerts');\n    return response.data;\n  },\n  // Share wishlist\n  shareWishlist: async () => {\n    const response = await api.post('/wishlist/share');\n    return response.data;\n  },\n  // Get shared wishlist\n  getSharedWishlist: async shareId => {\n    const response = await api.get(`/wishlist/shared/${shareId}`);\n    return response.data;\n  },\n  // Import wishlist from another platform\n  importWishlist: async (platform, data) => {\n    const response = await api.post('/wishlist/import', {\n      platform,\n      data\n    });\n    return response.data;\n  },\n  // Export wishlist\n  exportWishlist: async (format = 'json') => {\n    const response = await api.get(`/wishlist/export?format=${format}`);\n    return response.data;\n  }\n};\nexport default wishlistService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "wishlistService", "getWishlist", "response", "get", "data", "error", "console", "log", "addToWishlist", "productId", "post", "removeFromWishlist", "delete", "checkWishlist", "inWishlist", "clearWishlist", "getWishlistCount", "count", "moveToCart", "setPrice<PERSON><PERSON><PERSON>", "targetPrice", "removePrice<PERSON><PERSON><PERSON>", "getPriceAlerts", "shareWishlist", "getSharedWishlist", "shareId", "importWishlist", "platform", "exportWishlist", "format"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/services/wishlistService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport const wishlistService = {\n  // Get user's wishlist\n  getWishlist: async () => {\n    try {\n      const response = await api.get('/wishlist');\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist API not available, returning empty array');\n      // Return empty array if API is not available\n      return [];\n    }\n  },\n\n  // Add product to wishlist\n  addToWishlist: async (productId) => {\n    try {\n      const response = await api.post(`/wishlist/add/${productId}`);\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist add API not available');\n      throw error;\n    }\n  },\n\n  // Remove product from wishlist\n  removeFromWishlist: async (productId) => {\n    try {\n      const response = await api.delete(`/wishlist/remove/${productId}`);\n      return response.data;\n    } catch (error) {\n      console.log('Wishlist remove API not available');\n      throw error;\n    }\n  },\n\n  // Check if product is in wishlist\n  checkWishlist: async (productId) => {\n    try {\n      const response = await api.get(`/wishlist/check/${productId}`);\n      return response.data.inWishlist || false;\n    } catch (error) {\n      console.error('Error checking wishlist:', error);\n      return false;\n    }\n  },\n\n  // Clear entire wishlist\n  clearWishlist: async () => {\n    const response = await api.delete('/wishlist/clear');\n    return response.data;\n  },\n\n  // Get wishlist count\n  getWishlistCount: async () => {\n    try {\n      const response = await api.get('/wishlist/count');\n      return response.data.count || 0;\n    } catch (error) {\n      console.error('Error getting wishlist count:', error);\n      return 0;\n    }\n  },\n\n  // Move wishlist item to cart\n  moveToCart: async (productId) => {\n    const response = await api.post(`/wishlist/move-to-cart/${productId}`);\n    return response.data;\n  },\n\n  // Set price alert for wishlist item\n  setPriceAlert: async (productId, targetPrice) => {\n    const response = await api.post(`/wishlist/price-alert/${productId}`, {\n      targetPrice\n    });\n    return response.data;\n  },\n\n  // Remove price alert\n  removePriceAlert: async (productId) => {\n    const response = await api.delete(`/wishlist/price-alert/${productId}`);\n    return response.data;\n  },\n\n  // Get price alerts\n  getPriceAlerts: async () => {\n    const response = await api.get('/wishlist/price-alerts');\n    return response.data;\n  },\n\n  // Share wishlist\n  shareWishlist: async () => {\n    const response = await api.post('/wishlist/share');\n    return response.data;\n  },\n\n  // Get shared wishlist\n  getSharedWishlist: async (shareId) => {\n    const response = await api.get(`/wishlist/shared/${shareId}`);\n    return response.data;\n  },\n\n  // Import wishlist from another platform\n  importWishlist: async (platform, data) => {\n    const response = await api.post('/wishlist/import', {\n      platform,\n      data\n    });\n    return response.data;\n  },\n\n  // Export wishlist\n  exportWishlist: async (format = 'json') => {\n    const response = await api.get(`/wishlist/export?format=${format}`);\n    return response.data;\n  }\n};\n\nexport default wishlistService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMK,eAAe,GAAG;EAC7B;EACAC,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,WAAW,CAAC;MAC3C,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE;MACA,OAAO,EAAE;IACX;EACF,CAAC;EAED;EACAC,aAAa,EAAE,MAAOC,SAAS,IAAK;IAClC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMd,GAAG,CAACsB,IAAI,CAAC,iBAAiBD,SAAS,EAAE,CAAC;MAC7D,OAAOP,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMF,KAAK;IACb;EACF,CAAC;EAED;EACAM,kBAAkB,EAAE,MAAOF,SAAS,IAAK;IACvC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMd,GAAG,CAACwB,MAAM,CAAC,oBAAoBH,SAAS,EAAE,CAAC;MAClE,OAAOP,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,MAAMF,KAAK;IACb;EACF,CAAC;EAED;EACAQ,aAAa,EAAE,MAAOJ,SAAS,IAAK;IAClC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,mBAAmBM,SAAS,EAAE,CAAC;MAC9D,OAAOP,QAAQ,CAACE,IAAI,CAACU,UAAU,IAAI,KAAK;IAC1C,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,KAAK;IACd;EACF,CAAC;EAED;EACAU,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,MAAMb,QAAQ,GAAG,MAAMd,GAAG,CAACwB,MAAM,CAAC,iBAAiB,CAAC;IACpD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,gBAAgB,EAAE,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,iBAAiB,CAAC;MACjD,OAAOD,QAAQ,CAACE,IAAI,CAACa,KAAK,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,CAAC;IACV;EACF,CAAC;EAED;EACAa,UAAU,EAAE,MAAOT,SAAS,IAAK;IAC/B,MAAMP,QAAQ,GAAG,MAAMd,GAAG,CAACsB,IAAI,CAAC,0BAA0BD,SAAS,EAAE,CAAC;IACtE,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAe,aAAa,EAAE,MAAAA,CAAOV,SAAS,EAAEW,WAAW,KAAK;IAC/C,MAAMlB,QAAQ,GAAG,MAAMd,GAAG,CAACsB,IAAI,CAAC,yBAAyBD,SAAS,EAAE,EAAE;MACpEW;IACF,CAAC,CAAC;IACF,OAAOlB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAiB,gBAAgB,EAAE,MAAOZ,SAAS,IAAK;IACrC,MAAMP,QAAQ,GAAG,MAAMd,GAAG,CAACwB,MAAM,CAAC,yBAAyBH,SAAS,EAAE,CAAC;IACvE,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkB,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAMpB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,wBAAwB,CAAC;IACxD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAmB,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,MAAMrB,QAAQ,GAAG,MAAMd,GAAG,CAACsB,IAAI,CAAC,iBAAiB,CAAC;IAClD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAoB,iBAAiB,EAAE,MAAOC,OAAO,IAAK;IACpC,MAAMvB,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,oBAAoBsB,OAAO,EAAE,CAAC;IAC7D,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAsB,cAAc,EAAE,MAAAA,CAAOC,QAAQ,EAAEvB,IAAI,KAAK;IACxC,MAAMF,QAAQ,GAAG,MAAMd,GAAG,CAACsB,IAAI,CAAC,kBAAkB,EAAE;MAClDiB,QAAQ;MACRvB;IACF,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAwB,cAAc,EAAE,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACzC,MAAM3B,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,2BAA2B0B,MAAM,EAAE,CAAC;IACnE,OAAO3B,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}