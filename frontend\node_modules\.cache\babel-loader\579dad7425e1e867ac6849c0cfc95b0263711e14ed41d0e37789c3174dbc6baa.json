{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\price-comparison-app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return {\n        ...state,\n        loading: true\n      };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true\n};\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const user = await authService.getProfile();\n          dispatch({\n            type: 'LOGIN_SUCCESS',\n            payload: {\n              user,\n              token\n            }\n          });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({\n            type: 'LOGIN_FAILURE'\n          });\n        }\n      } else {\n        dispatch({\n          type: 'SET_LOADING',\n          payload: false\n        });\n      }\n    };\n    checkAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      dispatch({\n        type: 'LOGIN_START'\n      });\n      const response = await authService.login(email, password);\n      localStorage.setItem('token', response.token);\n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      dispatch({\n        type: 'LOGIN_FAILURE'\n      });\n      throw error;\n    }\n  };\n  const register = async (name, email, password) => {\n    try {\n      dispatch({\n        type: 'LOGIN_START'\n      });\n      const response = await authService.register(name, email, password);\n      localStorage.setItem('token', response.token);\n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      dispatch({\n        type: 'LOGIN_FAILURE'\n      });\n      throw error;\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    authService.logout();\n    dispatch({\n      type: 'LOGOUT'\n    });\n  };\n  const updateUser = userData => {\n    if (state.user) {\n      dispatch({\n        type: 'UPDATE_USER',\n        payload: {\n          ...state.user,\n          ...userData\n        }\n      });\n    }\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "authReducer", "state", "action", "type", "loading", "user", "payload", "token", "isAuthenticated", "initialState", "localStorage", "getItem", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "checkAuth", "getProfile", "error", "removeItem", "login", "email", "password", "response", "setItem", "register", "name", "logout", "updateUser", "userData", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/CascadeProjects/price-comparison-app/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { User, AuthState } from '../types';\nimport { authService } from '../services/authService';\n\ninterface AuthContextType extends AuthState {\n  login: (email: string, password: string) => Promise<void>;\n  register: (name: string, email: string, password: string) => Promise<void>;\n  logout: () => void;\n  updateUser: (userData: Partial<User>) => void;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ntype AuthAction =\n  | { type: 'LOGIN_START' }\n  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }\n  | { type: 'LOGIN_FAILURE' }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_USER'; payload: User }\n  | { type: 'SET_LOADING'; payload: boolean };\n\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return { ...state, loading: true };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\nconst initialState: AuthState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const user = await authService.getProfile();\n          dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({ type: 'LOGIN_FAILURE' });\n        }\n      } else {\n        dispatch({ type: 'SET_LOADING', payload: false });\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = async (email: string, password: string) => {\n    try {\n      dispatch({ type: 'LOGIN_START' });\n      const response = await authService.login(email, password);\n      \n      localStorage.setItem('token', response.token);\n      dispatch({ \n        type: 'LOGIN_SUCCESS', \n        payload: { user: response.user, token: response.token } \n      });\n    } catch (error) {\n      dispatch({ type: 'LOGIN_FAILURE' });\n      throw error;\n    }\n  };\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      dispatch({ type: 'LOGIN_START' });\n      const response = await authService.register(name, email, password);\n      \n      localStorage.setItem('token', response.token);\n      dispatch({ \n        type: 'LOGIN_SUCCESS', \n        payload: { user: response.user, token: response.token } \n      });\n    } catch (error) {\n      dispatch({ type: 'LOGIN_FAILURE' });\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    authService.logout();\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  const updateUser = (userData: Partial<User>) => {\n    if (state.user) {\n      dispatch({ type: 'UPDATE_USER', payload: { ...state.user, ...userData } });\n    }\n  };\n\n  const value: AuthContextType = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAE/E,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAStD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAUzE,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGF,KAAK;QAAEG,OAAO,EAAE;MAAK,CAAC;IACpC,KAAK,eAAe;MAClB,OAAO;QACL,GAAGH,KAAK;QACRI,IAAI,EAAEH,MAAM,CAACI,OAAO,CAACD,IAAI;QACzBE,KAAK,EAAEL,MAAM,CAACI,OAAO,CAACC,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBJ,OAAO,EAAE;MACX,CAAC;IACH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGH,KAAK;QACRI,IAAI,EAAE,IAAI;QACVE,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBJ,OAAO,EAAE;MACX,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRI,IAAI,EAAE,IAAI;QACVE,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBJ,OAAO,EAAE;MACX,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRI,IAAI,EAAEH,MAAM,CAACI;MACf,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGL,KAAK;QACRG,OAAO,EAAEF,MAAM,CAACI;MAClB,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,MAAMQ,YAAuB,GAAG;EAC9BJ,IAAI,EAAE,IAAI;EACVE,KAAK,EAAEG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCH,eAAe,EAAE,KAAK;EACtBJ,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMQ,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACb,KAAK,EAAEc,QAAQ,CAAC,GAAGtB,UAAU,CAACO,WAAW,EAAES,YAAY,CAAC;;EAE/D;EACAf,SAAS,CAAC,MAAM;IACd,MAAMsB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMT,KAAK,GAAGG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIJ,KAAK,EAAE;QACT,IAAI;UACF,MAAMF,IAAI,GAAG,MAAMV,WAAW,CAACsB,UAAU,CAAC,CAAC;UAC3CF,QAAQ,CAAC;YAAEZ,IAAI,EAAE,eAAe;YAAEG,OAAO,EAAE;cAAED,IAAI;cAAEE;YAAM;UAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,OAAOW,KAAK,EAAE;UACdR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;UAChCJ,QAAQ,CAAC;YAAEZ,IAAI,EAAE;UAAgB,CAAC,CAAC;QACrC;MACF,CAAC,MAAM;QACLY,QAAQ,CAAC;UAAEZ,IAAI,EAAE,aAAa;UAAEG,OAAO,EAAE;QAAM,CAAC,CAAC;MACnD;IACF,CAAC;IAEDU,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACFP,QAAQ,CAAC;QAAEZ,IAAI,EAAE;MAAc,CAAC,CAAC;MACjC,MAAMoB,QAAQ,GAAG,MAAM5B,WAAW,CAACyB,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MAEzDZ,YAAY,CAACc,OAAO,CAAC,OAAO,EAAED,QAAQ,CAAChB,KAAK,CAAC;MAC7CQ,QAAQ,CAAC;QACPZ,IAAI,EAAE,eAAe;QACrBG,OAAO,EAAE;UAAED,IAAI,EAAEkB,QAAQ,CAAClB,IAAI;UAAEE,KAAK,EAAEgB,QAAQ,CAAChB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdH,QAAQ,CAAC;QAAEZ,IAAI,EAAE;MAAgB,CAAC,CAAC;MACnC,MAAMe,KAAK;IACb;EACF,CAAC;EAED,MAAMO,QAAQ,GAAG,MAAAA,CAAOC,IAAY,EAAEL,KAAa,EAAEC,QAAgB,KAAK;IACxE,IAAI;MACFP,QAAQ,CAAC;QAAEZ,IAAI,EAAE;MAAc,CAAC,CAAC;MACjC,MAAMoB,QAAQ,GAAG,MAAM5B,WAAW,CAAC8B,QAAQ,CAACC,IAAI,EAAEL,KAAK,EAAEC,QAAQ,CAAC;MAElEZ,YAAY,CAACc,OAAO,CAAC,OAAO,EAAED,QAAQ,CAAChB,KAAK,CAAC;MAC7CQ,QAAQ,CAAC;QACPZ,IAAI,EAAE,eAAe;QACrBG,OAAO,EAAE;UAAED,IAAI,EAAEkB,QAAQ,CAAClB,IAAI;UAAEE,KAAK,EAAEgB,QAAQ,CAAChB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdH,QAAQ,CAAC;QAAEZ,IAAI,EAAE;MAAgB,CAAC,CAAC;MACnC,MAAMe,KAAK;IACb;EACF,CAAC;EAED,MAAMS,MAAM,GAAGA,CAAA,KAAM;IACnBjB,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCxB,WAAW,CAACgC,MAAM,CAAC,CAAC;IACpBZ,QAAQ,CAAC;MAAEZ,IAAI,EAAE;IAAS,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMyB,UAAU,GAAIC,QAAuB,IAAK;IAC9C,IAAI5B,KAAK,CAACI,IAAI,EAAE;MACdU,QAAQ,CAAC;QAAEZ,IAAI,EAAE,aAAa;QAAEG,OAAO,EAAE;UAAE,GAAGL,KAAK,CAACI,IAAI;UAAE,GAAGwB;QAAS;MAAE,CAAC,CAAC;IAC5E;EACF,CAAC;EAED,MAAMC,KAAsB,GAAG;IAC7B,GAAG7B,KAAK;IACRmB,KAAK;IACLK,QAAQ;IACRE,MAAM;IACNC;EACF,CAAC;EAED,oBACE/B,OAAA,CAACC,WAAW,CAACiC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjB,QAAA,EAChCA;EAAQ;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACrB,EAAA,CAhFWF,YAAqD;AAAAwB,EAAA,GAArDxB,YAAqD;AAkFlE,OAAO,MAAMyB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG/C,UAAU,CAACM,WAAW,CAAC;EACvC,IAAIyC,OAAO,KAAKxC,SAAS,EAAE;IACzB,MAAM,IAAIyC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}